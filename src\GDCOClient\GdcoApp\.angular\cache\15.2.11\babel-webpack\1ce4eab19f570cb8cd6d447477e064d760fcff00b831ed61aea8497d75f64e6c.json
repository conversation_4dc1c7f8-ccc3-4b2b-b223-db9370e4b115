{"ast": null, "code": "import { gdcoUtils } from '@gdco/core';\nimport { SimpleStore, ModelFactory } from '@gdco/store';\nimport { RecurringTaskDialogType } from './workspace-page.state';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@gdco/store\";\n/**\r\n * Store managing the state for the workspace page. State will automatically be preserved between page views\r\n * to provide a smoother user experience.\r\n */\nexport class WorkspacePageStore extends SimpleStore {\n  constructor(modelFactory) {\n    super(modelFactory, WorkspacePageStore.DEFAULT_STATE);\n    /** Indicates if the workspace is currently being loaded. */\n    this.loadingWorkspace$ = this.observeProperty('loadingWorkspace');\n    /** Indicates if the workspace schedules are currently being loaded. */\n    this.loadingSchedules$ = this.observeProperty('loadingSchedules');\n    /** Indicates if the workspace templates are currently being loaded. */\n    this.loadingTemplates$ = this.observeProperty('loadingTemplates');\n    /** Indicates if a schedule is currently being created. */\n    this.creatingSchedule$ = this.observeProperty('creatingSchedule');\n    /** Indicates if a schedule template is currently being created. */\n    this.creatingTemplate$ = this.observeProperty('creatingTemplate');\n    /** Indicates if the selected schedules are currently being deleted. */\n    this.deletingSelectedSchedules$ = this.observeProperty('deletingSelectedSchedules');\n    /** Indicates if the selected templates are currently being deleted. */\n    this.deletingSelectedTemplates$ = this.observeProperty('deletingSelectedTemplates');\n    /** The id of the current workspace. */\n    this.workspaceId$ = this.observeProperty('workspaceId');\n    /** The ids of all schedules in the current workspace. */\n    this.scheduleIds$ = this.observeProperty('scheduleIds');\n    /** The ids of all schedule templates in the current workspace. */\n    this.templateIds$ = this.observeProperty('templateIds');\n    /** The ids of the schedules that are currently selected. */\n    this.selectedScheduleIds$ = this.observeProperty('selectedScheduleIds');\n    /** The ids of the templates that are currently selected. */\n    this.selectedTemplateIds$ = this.observeProperty('selectedTemplateIds');\n    /** The recurring task dialog that is current open. */\n    this.recurringTaskDialogType$ = this.observeProperty('recurringTaskDialogType');\n    /** The UI model of the recurring task (schedule) that's currently being edited or duplicated. */\n    this.editOrDuplicateRecurringTask$ = this.observeProperty('editOrDuplicateRecurringTask');\n    /** The id of the schedule currently being edited. */\n    this.editScheduleId$ = this.observeProperty('editScheduleId');\n    /** The id of the schedule being duplicated. */\n    this.duplicateScheduleId$ = this.observeProperty('duplicateScheduleId');\n    /** Fault code selected by the user to create a recurring task for. */\n    this.selectedFaultCode$ = this.observeProperty('selectedFaultCode');\n    /** Campus the schedules are currently filtered by. */\n    this.selectedCampus$ = this.observeProperty('selectedCampus');\n    /** User the schedules are currently filtered by. */\n    this.selectedUserId$ = this.observeProperty('selectedUserId');\n    /** Indicates if completed schedules are shown to the user. */\n    this.showCompletedSchedules$ = this.observeProperty('showCompletedSchedules');\n    /** Schedules selected on the calendar page to view the instances for. */\n    this.viewScheduleIds$ = this.observeProperty('viewScheduleIds');\n    /** Current date viewed in the calendar. */\n    this.viewDate$ = this.observeProperty('viewDate');\n    /** All schedule instance Ids currently displayed on the calendar page. */\n    this.scheduleInstanceIds$ = this.observeProperty('scheduleInstanceIds');\n    /** Id of the schedule instance currently viewed by the user. */\n    this.selectedScheduleInstanceId$ = this.observeProperty('selectedScheduleInstanceId');\n    /** Ids of the tasks that were created by the currently viewed schedule instance. */\n    this.selectedScheduleInstanceTaskIds$ = this.observeProperty('selectedScheduleInstanceTaskIds');\n    /** Indicates if the dialog for viewing a schedule instance is currently open. */\n    this.scheduleInstanceDialogOpen$ = this.observeProperty('scheduleInstanceDialogOpen');\n    /** Indicates if the tasks for the currently viewed schedule instance are being loaded. */\n    this.loadingScheduleInstanceTasks$ = this.observeProperty('loadingScheduleInstanceTasks');\n    /** Indicates if the dialog to edit the column options on the scheudle tasks tab is open. */\n    this.columnOptionsDialogOpen$ = this.observeProperty('columnOptionsDialogOpen');\n    /** Indicates if the workspace column options are currently being saved. */\n    this.savingColumnOptions$ = this.observeProperty('savingColumnOptions');\n  }\n  /**\r\n   * Initializes the store with the given workspace Id.\r\n   * @param workspaceId The id of the current workspace.\r\n   */\n  initializePageStart(workspaceId) {\n    this.updateState({\n      workspaceId: workspaceId,\n      scheduleIds: workspaceId !== this.state.workspaceId ? [] : this.state.scheduleIds,\n      templateIds: workspaceId !== this.state.workspaceId ? [] : this.state.templateIds\n    });\n  }\n  /** State mutation indicating a workspace is loading. */\n  loadWorkspaceStart() {\n    this.updateState({\n      loadingWorkspace: true\n    });\n  }\n  /** State mutation indicating a workspace has finished loading. */\n  loadWorkspaceEnd() {\n    this.updateState({\n      loadingWorkspace: false\n    });\n  }\n  /** State mutation indicating the workspace schedules are loading. */\n  loadSchedulesStart() {\n    this.updateState({\n      loadingSchedules: true\n    });\n  }\n  /**\r\n   * State mutation indicating the workspace schedules have finished loading.\r\n   * @param scheduleIds The ids of the schedules loaded.\r\n   */\n  loadSchedulesEnd(scheduleIds) {\n    this.updateState({\n      loadingSchedules: false,\n      scheduleIds: scheduleIds\n    });\n  }\n  /**\r\n   * Selects the campus to filter the schedules by.\r\n   * @param selectedCampus The name of the campus to filter by.\r\n   */\n  selectCampus(selectedCampus) {\n    this.updateState({\n      selectedCampus\n    });\n  }\n  /**\r\n   * Selects the user to filter the schedules by.\r\n   * @param selectedUserId The id of the user to filter by.\r\n   */\n  selectUser(selectedUserId) {\n    this.updateState({\n      selectedUserId\n    });\n  }\n  /**\r\n   * Sets whether completed schedules should be displayed.\r\n   * @param showCompletedSchedules True if completed schedules should be show.\r\n   */\n  showCompletedSchedules(showCompletedSchedules) {\n    this.updateState({\n      showCompletedSchedules\n    });\n  }\n  /**\r\n   * Selects the given schedules.\r\n   * @param schedules The schedules to select.\r\n   */\n  selectSchedules(schedules) {\n    const selectedScheduleIds = (schedules || []).map(schedule => schedule.scheduleId);\n    this.updateState({\n      selectedScheduleIds\n    });\n  }\n  /**\r\n   * Adds a schedule id to the list of schedule ids for the current workspace.\r\n   * @param scheduleId The id of the schedule to add.\r\n   */\n  addSchedule(scheduleId) {\n    const scheduleIds = this.state.scheduleIds.concat(scheduleId);\n    this.updateState({\n      scheduleIds\n    });\n  }\n  /** Resets the selected schedules. */\n  deselectAllSchedules() {\n    this.updateState({\n      selectedScheduleIds: []\n    });\n  }\n  /**\r\n   * Selects the given schedule templates.\r\n   * @param templates The templates to select.\r\n   */\n  selectTemplates(templates) {\n    const selectedTemplateIds = (templates || []).map(template => template.scheduleTemplateId);\n    this.updateState({\n      selectedTemplateIds\n    });\n  }\n  /**\r\n   * Adds an id to the list of schedule template ids for the current workspace.\r\n   * @param templateId The id of the schedule template to add.\r\n   */\n  addTemplate(templateId) {\n    const templateIds = this.state.templateIds.concat(templateId);\n    this.updateState({\n      templateIds\n    });\n  }\n  /** Resets the selected schedule templates. */\n  deselectAllTemplates() {\n    this.updateState({\n      selectedTemplateIds: []\n    });\n  }\n  /** State mutation indicating the workspace templates are loading. */\n  loadTemplatesStart() {\n    this.updateState({\n      loadingTemplates: true\n    });\n  }\n  /**\r\n   * State mutation indicating the workspace templates have finished loading.\r\n   * @param templateIds The ids of the templates loaded.\r\n   */\n  loadTemplatesEnd(templateIds) {\n    this.updateState({\n      loadingTemplates: false,\n      templateIds: templateIds\n    });\n  }\n  /** State mutation indicating the selected schedules are being deleted. */\n  deleteSelectedSchedulesStart() {\n    this.updateState({\n      deletingSelectedSchedules: true\n    });\n  }\n  /**\r\n   * State mutation indicating the selected schedules have finished being deleted.\r\n   * @param deletedScheduleIds The ids of the schedules that were deleted.\r\n   */\n  deleteSelectedSchedulesEnd(deletedScheduleIds) {\n    const scheduleIds = deletedScheduleIds && deletedScheduleIds.length > 0 ? this.state.scheduleIds.filter(id => !deletedScheduleIds.includes(id)) : this.state.scheduleIds;\n    this.updateState({\n      deletingSelectedSchedules: false,\n      scheduleIds: scheduleIds\n    });\n  }\n  /** State mutation indicating the selected schedules on the calendar page are being deleted. */\n  deleteViewedSchedulesStart() {\n    this.updateState({\n      deletingSelectedSchedules: true\n    });\n  }\n  /**\r\n   * State mutation indicating the selected schedules on the calendar page have finished being deleted.\r\n   * @param deletedScheduleIds The ids of the schedules that were deleted.\r\n   */\n  deleteViewedSchedulesEnd(deletedScheduleIds) {\n    const viewScheduleIds = deletedScheduleIds && deletedScheduleIds.length > 0 ? this.state.viewScheduleIds.filter(id => !deletedScheduleIds.includes(id)) : this.state.viewScheduleIds;\n    this.updateState({\n      deletingSelectedSchedules: false,\n      viewScheduleIds: viewScheduleIds\n    });\n  }\n  /** State mutation indicating the selected templates are being deleted. */\n  deleteSelectedTemplatesStart() {\n    this.updateState({\n      deletingSelectedTemplates: true\n    });\n  }\n  /**\r\n   * State mutation indicating the selected templates have finished being deleted.\r\n   * @param deletedScheduleIds The ids of the templates that were deleted.\r\n   */\n  deleteSelectedTemplatesEnd(deletedTemplateIds) {\n    const templateIds = deletedTemplateIds && deletedTemplateIds.length > 0 ? this.state.templateIds.filter(id => !deletedTemplateIds.includes(id)) : this.state.templateIds;\n    this.updateState({\n      deletingSelectedTemplates: false,\n      templateIds: templateIds\n    });\n  }\n  removeInstances(instanceIds) {\n    const scheduleInstanceIds = this.state.scheduleInstanceIds?.filter(id => !instanceIds.includes(id));\n    this.updateState({\n      scheduleInstanceIds\n    });\n  }\n  /** Closes the recurring task dialog. */\n  closeRecurringTaskDialog() {\n    this.updateState({\n      recurringTaskDialogType: RecurringTaskDialogType.Closed,\n      editOrDuplicateRecurringTask: null,\n      editScheduleId: null,\n      duplicateScheduleId: null,\n      selectedFaultCode: null\n    });\n  }\n  /** Opens the dialog for selecting task for recurring task creation. */\n  openNewRecurringTaskSelectionDialog() {\n    this.updateState({\n      recurringTaskDialogType: RecurringTaskDialogType.SelectTask,\n      editOrDuplicateRecurringTask: null,\n      editScheduleId: null,\n      duplicateScheduleId: null,\n      selectedFaultCode: null\n    });\n  }\n  /**\r\n   * Opens the dialog for creating a recurring task.\r\n   * @param faultCode The fault code to create the recurring task with.\r\n   */\n  openNewRecurringTaskDialog(faultCode) {\n    this.updateState({\n      recurringTaskDialogType: RecurringTaskDialogType.New,\n      editOrDuplicateRecurringTask: null,\n      editScheduleId: null,\n      duplicateScheduleId: null,\n      selectedFaultCode: faultCode\n    });\n  }\n  /**\r\n   * Opens the dialog for editing a recurring task.\r\n   * @param recurringTask The recurring task to edit.\r\n   * @param schedule The existing schedule associated with the recurring task.\r\n   */\n  openEditRecurringTaskDialog(recurringTask, schedule) {\n    this.updateState({\n      recurringTaskDialogType: RecurringTaskDialogType.Edit,\n      editOrDuplicateRecurringTask: recurringTask,\n      editScheduleId: schedule.scheduleId,\n      duplicateScheduleId: null,\n      selectedFaultCode: schedule.clientData?.faultCode\n    });\n  }\n  /**\r\n   * Opens the dialog for duplicating a recurring task.\r\n   * @param recurringTask The recurring task to duplicate.\r\n   * @param schedule The existing schedule associated with the recurring task.\r\n   */\n  openDuplicateRecurringTaskDialog(recurringTask, schedule) {\n    this.updateState({\n      recurringTaskDialogType: RecurringTaskDialogType.Duplicate,\n      editOrDuplicateRecurringTask: recurringTask,\n      editScheduleId: null,\n      duplicateScheduleId: schedule.scheduleId,\n      selectedFaultCode: schedule.clientData?.faultCode\n    });\n  }\n  /** State mutation indicating a new recurring task (schedule) is being created. */\n  createNewRecurringTaskStart() {\n    this.updateState({\n      creatingSchedule: true\n    });\n  }\n  /** State mutation indicating a new recurring task (schedule) has finished being created. */\n  createNewRecurringTaskEnd() {\n    this.updateState({\n      creatingSchedule: false\n    });\n  }\n  /** State mutation indicating a recurring task (schedule) is being updated. */\n  updateRecurringTaskStart() {\n    this.updateState({\n      creatingSchedule: true\n    });\n  }\n  /** State mutation indicating a recurring task (schedule) has finished being updated. */\n  updateRecurringTaskEnd() {\n    this.updateState({\n      creatingSchedule: false\n    });\n  }\n  /** State mutation indicating a new schedule template is being created. */\n  createNewTemplateStart() {\n    this.updateState({\n      creatingTemplate: true\n    });\n  }\n  /** State mutation indicating a new schedule template has finished being created. */\n  createNewTemplateEnd() {\n    this.updateState({\n      creatingTemplate: false\n    });\n  }\n  /** State mutation indicating a schedule template is being updated. */\n  updateTemplateStart() {\n    this.updateState({\n      creatingTemplate: true\n    });\n  }\n  /** State mutation indicating a schedule template has finished being updated. */\n  updateTemplateEnd() {\n    this.updateState({\n      creatingTemplate: false\n    });\n  }\n  /**\r\n   * State mutation indicating the schedule instances are loading.\r\n   * @param viewScheduleIds The ids of the schedule the instances are being loaded for.\r\n   */\n  loadScheduleInstancesStart(viewScheduleIds) {\n    this.updateState({\n      viewScheduleIds\n    });\n  }\n  /**\r\n   * State mutation indicating the schedule instances have finished loading.\r\n   * @param scheduleInstanceIds The ids of the schedule instances loaded.\r\n   */\n  loadScheduleInstancesEnd(scheduleInstanceIds) {\n    this.updateState({\n      scheduleInstanceIds\n    });\n  }\n  /**\r\n   * State mutation indicating the schedule instances have finished loading.\r\n   * @param instanceIds The ids of the schedule instances loaded.\r\n   */\n  loadScheduleInstancesPageEnd(instanceIds, viewDate) {\n    const scheduleInstanceIds = gdcoUtils.union(this.state.scheduleInstanceIds, instanceIds);\n    this.updateState({\n      scheduleInstanceIds,\n      viewDate\n    });\n  }\n  /**\r\n   * Opens the dialog for viewing a schedule instance.\r\n   * @param scheduleInstanceId The id of the schedule instance to view.\r\n   */\n  openScheduleInstanceDialog(scheduleInstanceId) {\n    this.updateState({\n      scheduleInstanceDialogOpen: true,\n      selectedScheduleInstanceId: scheduleInstanceId,\n      selectedScheduleInstanceTaskIds: null,\n      loadingScheduleInstanceTasks: true\n    });\n  }\n  /**\r\n   * Sets the Ids of the tasks that were created by the current viewed schedule instance.\r\n   * @param taskIds The ids of the tasks created.\r\n   */\n  setSelectedScheduleInstanceTaskIds(taskIds) {\n    this.updateState({\n      selectedScheduleInstanceTaskIds: taskIds,\n      loadingScheduleInstanceTasks: false\n    });\n  }\n  /** Closes the schedule instance dialog. */\n  closeScheduleInstanceDialog() {\n    this.updateState({\n      scheduleInstanceDialogOpen: false,\n      selectedScheduleInstanceId: null,\n      selectedScheduleInstanceTaskIds: null\n    });\n  }\n  /** Clears any schedules currently being viewed */\n  clearViewedSchedules() {\n    this.updateState({\n      viewScheduleIds: [],\n      scheduleInstanceIds: []\n    });\n  }\n  /** Opens the dialog for modified the display scheduled tasks columns. */\n  openColumnOptionsDialog() {\n    this.updateState({\n      columnOptionsDialogOpen: true\n    });\n  }\n  /** Closes the column options dialog. */\n  closeColumnOptionsDialog() {\n    this.updateState({\n      columnOptionsDialogOpen: false\n    });\n  }\n  /** State mutation indicating the workspace's column options are being updated. */\n  updateWorkspaceColumnOptionsStart() {\n    this.updateState({\n      savingColumnOptions: true\n    });\n  }\n  /** State mutation indicating the workspace's column options have finished updating. */\n  updateWorkspaceColumnOptionsEnd() {\n    this.updateState({\n      savingColumnOptions: false\n    });\n  }\n}\nWorkspacePageStore.DEFAULT_STATE = {\n  loadingWorkspace: false,\n  loadingSchedules: false,\n  loadingTemplates: false,\n  creatingSchedule: false,\n  creatingTemplate: false,\n  deletingSelectedSchedules: false,\n  deletingSelectedTemplates: false,\n  workspaceId: null,\n  scheduleIds: [],\n  templateIds: [],\n  selectedScheduleIds: [],\n  selectedTemplateIds: [],\n  recurringTaskDialogType: RecurringTaskDialogType.Closed,\n  editOrDuplicateRecurringTask: null,\n  editScheduleId: null,\n  duplicateScheduleId: null,\n  selectedCampus: null,\n  selectedUserId: null,\n  showCompletedSchedules: false,\n  searchTerm: '',\n  selectedFaultCode: null,\n  viewScheduleIds: [],\n  viewDate: new Date(),\n  scheduleInstanceIds: null,\n  selectedScheduleInstanceId: null,\n  selectedScheduleInstanceTaskIds: null,\n  scheduleInstanceDialogOpen: false,\n  loadingScheduleInstanceTasks: false,\n  columnOptionsDialogOpen: false,\n  savingColumnOptions: false\n};\nWorkspacePageStore.ɵfac = function WorkspacePageStore_Factory(t) {\n  return new (t || WorkspacePageStore)(i0.ɵɵinject(i1.ModelFactory));\n};\nWorkspacePageStore.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n  token: WorkspacePageStore,\n  factory: WorkspacePageStore.ɵfac,\n  providedIn: 'root'\n});", "map": {"version": 3, "names": ["gdcoUtils", "SimpleStore", "ModelFactory", "RecurringTaskDialogType", "WorkspacePageStore", "constructor", "modelFactory", "DEFAULT_STATE", "loadingWorkspace$", "observeProperty", "loadingSchedules$", "loadingTemplates$", "creatingSchedule$", "creatingTemplate$", "deletingSelectedSchedules$", "deletingSelectedTemplates$", "workspaceId$", "scheduleIds$", "templateIds$", "selectedScheduleIds$", "selectedTemplateIds$", "recurringTaskDialogType$", "editOrDuplicateRecurringTask$", "editScheduleId$", "duplicateScheduleId$", "selectedFaultCode$", "selectedCampus$", "selectedUserId$", "showCompletedSchedules$", "viewScheduleIds$", "viewDate$", "scheduleInstanceIds$", "selectedScheduleInstanceId$", "selectedScheduleInstanceTaskIds$", "scheduleInstanceDialogOpen$", "loadingScheduleInstanceTasks$", "columnOptionsDialogOpen$", "savingColumnOptions$", "initializePageStart", "workspaceId", "updateState", "scheduleIds", "state", "templateIds", "loadWorkspaceStart", "loadingWorkspace", "loadWorkspaceEnd", "loadSchedulesStart", "loadingSchedules", "loadSchedulesEnd", "selectCampus", "selectedCampus", "selectUser", "selectedUserId", "showCompletedSchedules", "selectSchedules", "schedules", "selectedScheduleIds", "map", "schedule", "scheduleId", "addSchedule", "concat", "deselectAllSchedules", "selectTemplates", "templates", "selectedTemplateIds", "template", "scheduleTemplateId", "addTemplate", "templateId", "deselectAllTemplates", "loadTemplatesStart", "loadingTemplates", "loadTemplatesEnd", "deleteSelectedSchedulesStart", "deletingSelectedSchedules", "deleteSelectedSchedulesEnd", "deletedScheduleIds", "length", "filter", "id", "includes", "deleteViewedSchedulesStart", "deleteViewedSchedulesEnd", "viewScheduleIds", "deleteSelectedTemplatesStart", "deletingSelectedTemplates", "deleteSelectedTemplatesEnd", "deletedTemplateIds", "removeInstances", "instanceIds", "scheduleInstanceIds", "closeRecurringTaskDialog", "recurringTaskDialogType", "Closed", "editOrDuplicateRecurringTask", "editScheduleId", "duplicateScheduleId", "selectedFaultCode", "openNewRecurringTaskSelectionDialog", "SelectTask", "openNewRecurringTaskDialog", "faultCode", "New", "openEditRecurringTaskDialog", "recurringTask", "Edit", "clientData", "openDuplicateRecurringTaskDialog", "Duplicate", "createNewRecurringTaskStart", "creatingSchedule", "createNewRecurringTaskEnd", "updateRecurringTaskStart", "updateRecurringTaskEnd", "createNewTemplateStart", "creatingTemplate", "createNewTemplateEnd", "updateTemplateStart", "updateTemplateEnd", "loadScheduleInstancesStart", "loadScheduleInstancesEnd", "loadScheduleInstancesPageEnd", "viewDate", "union", "openScheduleInstanceDialog", "scheduleInstanceId", "scheduleInstanceDialogOpen", "selectedScheduleInstanceId", "selectedScheduleInstanceTaskIds", "loadingScheduleInstanceTasks", "setSelectedScheduleInstanceTaskIds", "taskIds", "closeScheduleInstanceDialog", "clearViewedSchedules", "openColumnOptionsDialog", "columnOptionsDialogOpen", "closeColumnOptionsDialog", "updateWorkspaceColumnOptionsStart", "savingColumnOptions", "updateWorkspaceColumnOptionsEnd", "searchTerm", "Date", "i0", "ɵɵinject", "i1", "factory", "ɵfac", "providedIn"], "sources": ["D:\\Repo\\GDCO\\MCIO-GDCO-AppService\\src\\GDCOClient\\GdcoApp\\projects\\applications\\tasks-scheduler\\src\\app\\pages\\workspace\\workspace-page.store.ts"], "sourcesContent": ["/**\r\n * @license\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n */\r\n\r\nimport { Injectable } from '@angular/core';\r\nimport { gdcoUtils } from '@gdco/core';\r\nimport { SimpleStore, ModelFactory } from '@gdco/store';\r\n\r\nimport { Schedule, ScheduleTemplate } from '@gdco/reference-systems/gdco-service';\r\nimport { WorkspacePageState, RecurringTaskDialogType } from './workspace-page.state';\r\nimport { RecurringTask } from '../../common';\r\n\r\n/**\r\n * Store managing the state for the workspace page. State will automatically be preserved between page views\r\n * to provide a smoother user experience.\r\n */\r\n@Injectable({ providedIn: 'root' })\r\nexport class WorkspacePageStore extends SimpleStore<WorkspacePageState> {\r\n  static readonly DEFAULT_STATE: Readonly<WorkspacePageState> = {\r\n    loadingWorkspace: false,\r\n    loadingSchedules: false,\r\n    loadingTemplates: false,\r\n    creatingSchedule: false,\r\n    creatingTemplate: false,\r\n    deletingSelectedSchedules: false,\r\n    deletingSelectedTemplates: false,\r\n    workspaceId: null,\r\n    scheduleIds: [],\r\n    templateIds: [],\r\n    selectedScheduleIds: [],\r\n    selectedTemplateIds: [],\r\n    recurringTaskDialogType: RecurringTaskDialogType.Closed,\r\n    editOrDuplicateRecurringTask: null,\r\n    editScheduleId: null,\r\n    duplicateScheduleId: null,\r\n    selectedCampus: null,\r\n    selectedUserId: null,\r\n    showCompletedSchedules: false,\r\n    searchTerm: '',\r\n    selectedFaultCode: null,\r\n    viewScheduleIds: [],\r\n    viewDate: new Date(),\r\n    scheduleInstanceIds: null,\r\n    selectedScheduleInstanceId: null,\r\n    selectedScheduleInstanceTaskIds: null,\r\n    scheduleInstanceDialogOpen: false,\r\n    loadingScheduleInstanceTasks: false,\r\n    columnOptionsDialogOpen: false,\r\n    savingColumnOptions: false\r\n  };\r\n\r\n  /** Indicates if the workspace is currently being loaded. */\r\n  readonly loadingWorkspace$ = this.observeProperty('loadingWorkspace');\r\n\r\n  /** Indicates if the workspace schedules are currently being loaded. */\r\n  readonly loadingSchedules$ = this.observeProperty('loadingSchedules');\r\n\r\n  /** Indicates if the workspace templates are currently being loaded. */\r\n  readonly loadingTemplates$ = this.observeProperty('loadingTemplates');\r\n\r\n  /** Indicates if a schedule is currently being created. */\r\n  readonly creatingSchedule$ = this.observeProperty('creatingSchedule');\r\n\r\n  /** Indicates if a schedule template is currently being created. */\r\n  readonly creatingTemplate$ = this.observeProperty('creatingTemplate');\r\n\r\n  /** Indicates if the selected schedules are currently being deleted. */\r\n  readonly deletingSelectedSchedules$ = this.observeProperty('deletingSelectedSchedules');\r\n\r\n  /** Indicates if the selected templates are currently being deleted. */\r\n  readonly deletingSelectedTemplates$ = this.observeProperty('deletingSelectedTemplates');\r\n\r\n  /** The id of the current workspace. */\r\n  readonly workspaceId$ = this.observeProperty('workspaceId');\r\n\r\n  /** The ids of all schedules in the current workspace. */\r\n  readonly scheduleIds$ = this.observeProperty('scheduleIds');\r\n\r\n  /** The ids of all schedule templates in the current workspace. */\r\n  readonly templateIds$ = this.observeProperty('templateIds');\r\n\r\n  /** The ids of the schedules that are currently selected. */\r\n  readonly selectedScheduleIds$ = this.observeProperty('selectedScheduleIds');\r\n\r\n  /** The ids of the templates that are currently selected. */\r\n  readonly selectedTemplateIds$ = this.observeProperty('selectedTemplateIds');\r\n\r\n  /** The recurring task dialog that is current open. */\r\n  readonly recurringTaskDialogType$ = this.observeProperty('recurringTaskDialogType');\r\n\r\n  /** The UI model of the recurring task (schedule) that's currently being edited or duplicated. */\r\n  readonly editOrDuplicateRecurringTask$ = this.observeProperty('editOrDuplicateRecurringTask');\r\n\r\n  /** The id of the schedule currently being edited. */\r\n  readonly editScheduleId$ = this.observeProperty('editScheduleId');\r\n\r\n  /** The id of the schedule being duplicated. */\r\n  readonly duplicateScheduleId$ = this.observeProperty('duplicateScheduleId');\r\n\r\n  /** Fault code selected by the user to create a recurring task for. */\r\n  readonly selectedFaultCode$ = this.observeProperty('selectedFaultCode');\r\n\r\n  /** Campus the schedules are currently filtered by. */\r\n  readonly selectedCampus$ = this.observeProperty('selectedCampus');\r\n\r\n  /** User the schedules are currently filtered by. */\r\n  readonly selectedUserId$ = this.observeProperty('selectedUserId');\r\n\r\n  /** Indicates if completed schedules are shown to the user. */\r\n  readonly showCompletedSchedules$ = this.observeProperty('showCompletedSchedules');\r\n\r\n  /** Schedules selected on the calendar page to view the instances for. */\r\n  readonly viewScheduleIds$ = this.observeProperty('viewScheduleIds');\r\n\r\n  /** Current date viewed in the calendar. */\r\n  readonly viewDate$ = this.observeProperty('viewDate');\r\n\r\n  /** All schedule instance Ids currently displayed on the calendar page. */\r\n  readonly scheduleInstanceIds$ = this.observeProperty('scheduleInstanceIds');\r\n\r\n  /** Id of the schedule instance currently viewed by the user. */\r\n  readonly selectedScheduleInstanceId$ = this.observeProperty('selectedScheduleInstanceId');\r\n\r\n  /** Ids of the tasks that were created by the currently viewed schedule instance. */\r\n  readonly selectedScheduleInstanceTaskIds$ = this.observeProperty('selectedScheduleInstanceTaskIds');\r\n\r\n  /** Indicates if the dialog for viewing a schedule instance is currently open. */\r\n  readonly scheduleInstanceDialogOpen$ = this.observeProperty('scheduleInstanceDialogOpen');\r\n\r\n  /** Indicates if the tasks for the currently viewed schedule instance are being loaded. */\r\n  readonly loadingScheduleInstanceTasks$ = this.observeProperty('loadingScheduleInstanceTasks');\r\n\r\n  /** Indicates if the dialog to edit the column options on the scheudle tasks tab is open. */\r\n  readonly columnOptionsDialogOpen$ = this.observeProperty('columnOptionsDialogOpen');\r\n\r\n  /** Indicates if the workspace column options are currently being saved. */\r\n  readonly savingColumnOptions$ = this.observeProperty('savingColumnOptions');\r\n\r\n  constructor(modelFactory: ModelFactory) {\r\n    super(modelFactory, WorkspacePageStore.DEFAULT_STATE);\r\n  }\r\n\r\n  /**\r\n   * Initializes the store with the given workspace Id.\r\n   * @param workspaceId The id of the current workspace.\r\n   */\r\n  initializePageStart(workspaceId: string): void {\r\n    this.updateState({\r\n      workspaceId: workspaceId,\r\n      scheduleIds: workspaceId !== this.state.workspaceId ? [] : this.state.scheduleIds,\r\n      templateIds: workspaceId !== this.state.workspaceId ? [] : this.state.templateIds\r\n    });\r\n  }\r\n\r\n  /** State mutation indicating a workspace is loading. */\r\n  loadWorkspaceStart(): void {\r\n    this.updateState({ loadingWorkspace: true });\r\n  }\r\n\r\n  /** State mutation indicating a workspace has finished loading. */\r\n  loadWorkspaceEnd(): void {\r\n    this.updateState({ loadingWorkspace: false });\r\n  }\r\n\r\n  /** State mutation indicating the workspace schedules are loading. */\r\n  loadSchedulesStart(): void {\r\n    this.updateState({ loadingSchedules: true });\r\n  }\r\n\r\n  /**\r\n   * State mutation indicating the workspace schedules have finished loading.\r\n   * @param scheduleIds The ids of the schedules loaded.\r\n   */\r\n  loadSchedulesEnd(scheduleIds: string[]): void {\r\n    this.updateState({\r\n      loadingSchedules: false,\r\n      scheduleIds: scheduleIds\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Selects the campus to filter the schedules by.\r\n   * @param selectedCampus The name of the campus to filter by.\r\n   */\r\n  selectCampus(selectedCampus: string): void {\r\n    this.updateState({ selectedCampus });\r\n  }\r\n\r\n  /**\r\n   * Selects the user to filter the schedules by.\r\n   * @param selectedUserId The id of the user to filter by.\r\n   */\r\n  selectUser(selectedUserId: string): void {\r\n    this.updateState({ selectedUserId });\r\n  }\r\n\r\n  /**\r\n   * Sets whether completed schedules should be displayed.\r\n   * @param showCompletedSchedules True if completed schedules should be show.\r\n   */\r\n  showCompletedSchedules(showCompletedSchedules: boolean): void {\r\n    this.updateState({ showCompletedSchedules });\r\n  }\r\n\r\n  /**\r\n   * Selects the given schedules.\r\n   * @param schedules The schedules to select.\r\n   */\r\n  selectSchedules(schedules: Schedule[]): void {\r\n    const selectedScheduleIds = (schedules || []).map(schedule => schedule.scheduleId);\r\n\r\n    this.updateState({ selectedScheduleIds });\r\n  }\r\n\r\n  /**\r\n   * Adds a schedule id to the list of schedule ids for the current workspace.\r\n   * @param scheduleId The id of the schedule to add.\r\n   */\r\n  addSchedule(scheduleId: string): void {\r\n    const scheduleIds = this.state.scheduleIds.concat(scheduleId);\r\n\r\n    this.updateState({ scheduleIds });\r\n  }\r\n\r\n  /** Resets the selected schedules. */\r\n  deselectAllSchedules(): void {\r\n    this.updateState({ selectedScheduleIds: [] });\r\n  }\r\n\r\n  /**\r\n   * Selects the given schedule templates.\r\n   * @param templates The templates to select.\r\n   */\r\n  selectTemplates(templates: ScheduleTemplate[]): void {\r\n    const selectedTemplateIds = (templates || []).map(template => template.scheduleTemplateId);\r\n\r\n    this.updateState({ selectedTemplateIds });\r\n  }\r\n\r\n  /**\r\n   * Adds an id to the list of schedule template ids for the current workspace.\r\n   * @param templateId The id of the schedule template to add.\r\n   */\r\n  addTemplate(templateId: string): void {\r\n    const templateIds = this.state.templateIds.concat(templateId);\r\n\r\n    this.updateState({ templateIds });\r\n  }\r\n\r\n  /** Resets the selected schedule templates. */\r\n  deselectAllTemplates(): void {\r\n    this.updateState({ selectedTemplateIds: [] });\r\n  }\r\n\r\n  /** State mutation indicating the workspace templates are loading. */\r\n  loadTemplatesStart(): void {\r\n    this.updateState({ loadingTemplates: true });\r\n  }\r\n\r\n  /**\r\n   * State mutation indicating the workspace templates have finished loading.\r\n   * @param templateIds The ids of the templates loaded.\r\n   */\r\n  loadTemplatesEnd(templateIds: string[]): void {\r\n    this.updateState({\r\n      loadingTemplates: false,\r\n      templateIds: templateIds\r\n    });\r\n  }\r\n\r\n  /** State mutation indicating the selected schedules are being deleted. */\r\n  deleteSelectedSchedulesStart(): void {\r\n    this.updateState({ deletingSelectedSchedules: true });\r\n  }\r\n\r\n  /**\r\n   * State mutation indicating the selected schedules have finished being deleted.\r\n   * @param deletedScheduleIds The ids of the schedules that were deleted.\r\n   */\r\n  deleteSelectedSchedulesEnd(deletedScheduleIds: string[]): void {\r\n    const scheduleIds =\r\n      deletedScheduleIds && deletedScheduleIds.length > 0\r\n        ? this.state.scheduleIds.filter(id => !deletedScheduleIds.includes(id))\r\n        : this.state.scheduleIds;\r\n\r\n    this.updateState({\r\n      deletingSelectedSchedules: false,\r\n      scheduleIds: scheduleIds\r\n    });\r\n  }\r\n\r\n  /** State mutation indicating the selected schedules on the calendar page are being deleted. */\r\n  deleteViewedSchedulesStart(): void {\r\n    this.updateState({ deletingSelectedSchedules: true });\r\n  }\r\n\r\n  /**\r\n   * State mutation indicating the selected schedules on the calendar page have finished being deleted.\r\n   * @param deletedScheduleIds The ids of the schedules that were deleted.\r\n   */\r\n  deleteViewedSchedulesEnd(deletedScheduleIds: string[]): void {\r\n    const viewScheduleIds =\r\n      deletedScheduleIds && deletedScheduleIds.length > 0\r\n        ? this.state.viewScheduleIds.filter(id => !deletedScheduleIds.includes(id))\r\n        : this.state.viewScheduleIds;\r\n\r\n    this.updateState({\r\n      deletingSelectedSchedules: false,\r\n      viewScheduleIds: viewScheduleIds\r\n    });\r\n  }\r\n\r\n  /** State mutation indicating the selected templates are being deleted. */\r\n  deleteSelectedTemplatesStart(): void {\r\n    this.updateState({ deletingSelectedTemplates: true });\r\n  }\r\n\r\n  /**\r\n   * State mutation indicating the selected templates have finished being deleted.\r\n   * @param deletedScheduleIds The ids of the templates that were deleted.\r\n   */\r\n  deleteSelectedTemplatesEnd(deletedTemplateIds: string[]): void {\r\n    const templateIds =\r\n      deletedTemplateIds && deletedTemplateIds.length > 0\r\n        ? this.state.templateIds.filter(id => !deletedTemplateIds.includes(id))\r\n        : this.state.templateIds;\r\n\r\n    this.updateState({\r\n      deletingSelectedTemplates: false,\r\n      templateIds: templateIds\r\n    });\r\n  }\r\n\r\n  removeInstances(instanceIds: string[]): void {\r\n    const scheduleInstanceIds = this.state.scheduleInstanceIds?.filter(id => !instanceIds.includes(id));\r\n\r\n    this.updateState({ scheduleInstanceIds });\r\n  }\r\n\r\n  /** Closes the recurring task dialog. */\r\n  closeRecurringTaskDialog(): void {\r\n    this.updateState({\r\n      recurringTaskDialogType: RecurringTaskDialogType.Closed,\r\n      editOrDuplicateRecurringTask: null,\r\n      editScheduleId: null,\r\n      duplicateScheduleId: null,\r\n      selectedFaultCode: null\r\n    });\r\n  }\r\n\r\n  /** Opens the dialog for selecting task for recurring task creation. */\r\n  openNewRecurringTaskSelectionDialog(): void {\r\n    this.updateState({\r\n      recurringTaskDialogType: RecurringTaskDialogType.SelectTask,\r\n      editOrDuplicateRecurringTask: null,\r\n      editScheduleId: null,\r\n      duplicateScheduleId: null,\r\n      selectedFaultCode: null\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Opens the dialog for creating a recurring task.\r\n   * @param faultCode The fault code to create the recurring task with.\r\n   */\r\n  openNewRecurringTaskDialog(faultCode: number): void {\r\n    this.updateState({\r\n      recurringTaskDialogType: RecurringTaskDialogType.New,\r\n      editOrDuplicateRecurringTask: null,\r\n      editScheduleId: null,\r\n      duplicateScheduleId: null,\r\n      selectedFaultCode: faultCode\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Opens the dialog for editing a recurring task.\r\n   * @param recurringTask The recurring task to edit.\r\n   * @param schedule The existing schedule associated with the recurring task.\r\n   */\r\n  openEditRecurringTaskDialog(recurringTask: RecurringTask, schedule: Schedule): void {\r\n    this.updateState({\r\n      recurringTaskDialogType: RecurringTaskDialogType.Edit,\r\n      editOrDuplicateRecurringTask: recurringTask,\r\n      editScheduleId: schedule.scheduleId,\r\n      duplicateScheduleId: null,\r\n      selectedFaultCode: schedule.clientData?.faultCode\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Opens the dialog for duplicating a recurring task.\r\n   * @param recurringTask The recurring task to duplicate.\r\n   * @param schedule The existing schedule associated with the recurring task.\r\n   */\r\n  openDuplicateRecurringTaskDialog(recurringTask: RecurringTask, schedule: Schedule): void {\r\n    this.updateState({\r\n      recurringTaskDialogType: RecurringTaskDialogType.Duplicate,\r\n      editOrDuplicateRecurringTask: recurringTask,\r\n      editScheduleId: null,\r\n      duplicateScheduleId: schedule.scheduleId,\r\n      selectedFaultCode: schedule.clientData?.faultCode\r\n    });\r\n  }\r\n\r\n  /** State mutation indicating a new recurring task (schedule) is being created. */\r\n  createNewRecurringTaskStart(): void {\r\n    this.updateState({ creatingSchedule: true });\r\n  }\r\n\r\n  /** State mutation indicating a new recurring task (schedule) has finished being created. */\r\n  createNewRecurringTaskEnd(): void {\r\n    this.updateState({ creatingSchedule: false });\r\n  }\r\n\r\n  /** State mutation indicating a recurring task (schedule) is being updated. */\r\n  updateRecurringTaskStart(): void {\r\n    this.updateState({ creatingSchedule: true });\r\n  }\r\n\r\n  /** State mutation indicating a recurring task (schedule) has finished being updated. */\r\n  updateRecurringTaskEnd(): void {\r\n    this.updateState({ creatingSchedule: false });\r\n  }\r\n\r\n  /** State mutation indicating a new schedule template is being created. */\r\n  createNewTemplateStart(): void {\r\n    this.updateState({ creatingTemplate: true });\r\n  }\r\n\r\n  /** State mutation indicating a new schedule template has finished being created. */\r\n  createNewTemplateEnd(): void {\r\n    this.updateState({ creatingTemplate: false });\r\n  }\r\n\r\n  /** State mutation indicating a schedule template is being updated. */\r\n  updateTemplateStart(): void {\r\n    this.updateState({ creatingTemplate: true });\r\n  }\r\n\r\n  /** State mutation indicating a schedule template has finished being updated. */\r\n  updateTemplateEnd(): void {\r\n    this.updateState({ creatingTemplate: false });\r\n  }\r\n\r\n  /**\r\n   * State mutation indicating the schedule instances are loading.\r\n   * @param viewScheduleIds The ids of the schedule the instances are being loaded for.\r\n   */\r\n  loadScheduleInstancesStart(viewScheduleIds: string[]): void {\r\n    this.updateState({ viewScheduleIds });\r\n  }\r\n\r\n  /**\r\n   * State mutation indicating the schedule instances have finished loading.\r\n   * @param scheduleInstanceIds The ids of the schedule instances loaded.\r\n   */\r\n  loadScheduleInstancesEnd(scheduleInstanceIds: string[]): void {\r\n    this.updateState({ scheduleInstanceIds });\r\n  }\r\n\r\n  /**\r\n   * State mutation indicating the schedule instances have finished loading.\r\n   * @param instanceIds The ids of the schedule instances loaded.\r\n   */\r\n  loadScheduleInstancesPageEnd(instanceIds: string[], viewDate: Date): void {\r\n    const scheduleInstanceIds = gdcoUtils.union(this.state.scheduleInstanceIds, instanceIds);\r\n\r\n    this.updateState({ scheduleInstanceIds, viewDate });\r\n  }\r\n\r\n  /**\r\n   * Opens the dialog for viewing a schedule instance.\r\n   * @param scheduleInstanceId The id of the schedule instance to view.\r\n   */\r\n  openScheduleInstanceDialog(scheduleInstanceId: string): void {\r\n    this.updateState({\r\n      scheduleInstanceDialogOpen: true,\r\n      selectedScheduleInstanceId: scheduleInstanceId,\r\n      selectedScheduleInstanceTaskIds: null,\r\n      loadingScheduleInstanceTasks: true\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Sets the Ids of the tasks that were created by the current viewed schedule instance.\r\n   * @param taskIds The ids of the tasks created.\r\n   */\r\n  setSelectedScheduleInstanceTaskIds(taskIds: string[]): void {\r\n    this.updateState({\r\n      selectedScheduleInstanceTaskIds: taskIds,\r\n      loadingScheduleInstanceTasks: false\r\n    });\r\n  }\r\n\r\n  /** Closes the schedule instance dialog. */\r\n  closeScheduleInstanceDialog(): void {\r\n    this.updateState({\r\n      scheduleInstanceDialogOpen: false,\r\n      selectedScheduleInstanceId: null,\r\n      selectedScheduleInstanceTaskIds: null\r\n    });\r\n  }\r\n\r\n  /** Clears any schedules currently being viewed */\r\n  clearViewedSchedules(): void {\r\n    this.updateState({\r\n      viewScheduleIds: [],\r\n      scheduleInstanceIds: []\r\n    });\r\n  }\r\n\r\n  /** Opens the dialog for modified the display scheduled tasks columns. */\r\n  openColumnOptionsDialog(): void {\r\n    this.updateState({ columnOptionsDialogOpen: true });\r\n  }\r\n\r\n  /** Closes the column options dialog. */\r\n  closeColumnOptionsDialog(): void {\r\n    this.updateState({ columnOptionsDialogOpen: false });\r\n  }\r\n\r\n  /** State mutation indicating the workspace's column options are being updated. */\r\n  updateWorkspaceColumnOptionsStart(): void {\r\n    this.updateState({ savingColumnOptions: true });\r\n  }\r\n\r\n  /** State mutation indicating the workspace's column options have finished updating. */\r\n  updateWorkspaceColumnOptionsEnd(): void {\r\n    this.updateState({ savingColumnOptions: false });\r\n  }\r\n}\r\n"], "mappings": "AAMA,SAASA,SAAS,QAAQ,YAAY;AACtC,SAASC,WAAW,EAAEC,YAAY,QAAQ,aAAa;AAGvD,SAA6BC,uBAAuB,QAAQ,wBAAwB;;;AAGpF;;;;AAKA,OAAM,MAAOC,kBAAmB,SAAQH,WAA+B;EAyHrEI,YAAYC,YAA0B;IACpC,KAAK,CAACA,YAAY,EAAEF,kBAAkB,CAACG,aAAa,CAAC;IAxFvD;IACS,KAAAC,iBAAiB,GAAG,IAAI,CAACC,eAAe,CAAC,kBAAkB,CAAC;IAErE;IACS,KAAAC,iBAAiB,GAAG,IAAI,CAACD,eAAe,CAAC,kBAAkB,CAAC;IAErE;IACS,KAAAE,iBAAiB,GAAG,IAAI,CAACF,eAAe,CAAC,kBAAkB,CAAC;IAErE;IACS,KAAAG,iBAAiB,GAAG,IAAI,CAACH,eAAe,CAAC,kBAAkB,CAAC;IAErE;IACS,KAAAI,iBAAiB,GAAG,IAAI,CAACJ,eAAe,CAAC,kBAAkB,CAAC;IAErE;IACS,KAAAK,0BAA0B,GAAG,IAAI,CAACL,eAAe,CAAC,2BAA2B,CAAC;IAEvF;IACS,KAAAM,0BAA0B,GAAG,IAAI,CAACN,eAAe,CAAC,2BAA2B,CAAC;IAEvF;IACS,KAAAO,YAAY,GAAG,IAAI,CAACP,eAAe,CAAC,aAAa,CAAC;IAE3D;IACS,KAAAQ,YAAY,GAAG,IAAI,CAACR,eAAe,CAAC,aAAa,CAAC;IAE3D;IACS,KAAAS,YAAY,GAAG,IAAI,CAACT,eAAe,CAAC,aAAa,CAAC;IAE3D;IACS,KAAAU,oBAAoB,GAAG,IAAI,CAACV,eAAe,CAAC,qBAAqB,CAAC;IAE3E;IACS,KAAAW,oBAAoB,GAAG,IAAI,CAACX,eAAe,CAAC,qBAAqB,CAAC;IAE3E;IACS,KAAAY,wBAAwB,GAAG,IAAI,CAACZ,eAAe,CAAC,yBAAyB,CAAC;IAEnF;IACS,KAAAa,6BAA6B,GAAG,IAAI,CAACb,eAAe,CAAC,8BAA8B,CAAC;IAE7F;IACS,KAAAc,eAAe,GAAG,IAAI,CAACd,eAAe,CAAC,gBAAgB,CAAC;IAEjE;IACS,KAAAe,oBAAoB,GAAG,IAAI,CAACf,eAAe,CAAC,qBAAqB,CAAC;IAE3E;IACS,KAAAgB,kBAAkB,GAAG,IAAI,CAAChB,eAAe,CAAC,mBAAmB,CAAC;IAEvE;IACS,KAAAiB,eAAe,GAAG,IAAI,CAACjB,eAAe,CAAC,gBAAgB,CAAC;IAEjE;IACS,KAAAkB,eAAe,GAAG,IAAI,CAAClB,eAAe,CAAC,gBAAgB,CAAC;IAEjE;IACS,KAAAmB,uBAAuB,GAAG,IAAI,CAACnB,eAAe,CAAC,wBAAwB,CAAC;IAEjF;IACS,KAAAoB,gBAAgB,GAAG,IAAI,CAACpB,eAAe,CAAC,iBAAiB,CAAC;IAEnE;IACS,KAAAqB,SAAS,GAAG,IAAI,CAACrB,eAAe,CAAC,UAAU,CAAC;IAErD;IACS,KAAAsB,oBAAoB,GAAG,IAAI,CAACtB,eAAe,CAAC,qBAAqB,CAAC;IAE3E;IACS,KAAAuB,2BAA2B,GAAG,IAAI,CAACvB,eAAe,CAAC,4BAA4B,CAAC;IAEzF;IACS,KAAAwB,gCAAgC,GAAG,IAAI,CAACxB,eAAe,CAAC,iCAAiC,CAAC;IAEnG;IACS,KAAAyB,2BAA2B,GAAG,IAAI,CAACzB,eAAe,CAAC,4BAA4B,CAAC;IAEzF;IACS,KAAA0B,6BAA6B,GAAG,IAAI,CAAC1B,eAAe,CAAC,8BAA8B,CAAC;IAE7F;IACS,KAAA2B,wBAAwB,GAAG,IAAI,CAAC3B,eAAe,CAAC,yBAAyB,CAAC;IAEnF;IACS,KAAA4B,oBAAoB,GAAG,IAAI,CAAC5B,eAAe,CAAC,qBAAqB,CAAC;EAI3E;EAEA;;;;EAIA6B,mBAAmBA,CAACC,WAAmB;IACrC,IAAI,CAACC,WAAW,CAAC;MACfD,WAAW,EAAEA,WAAW;MACxBE,WAAW,EAAEF,WAAW,KAAK,IAAI,CAACG,KAAK,CAACH,WAAW,GAAG,EAAE,GAAG,IAAI,CAACG,KAAK,CAACD,WAAW;MACjFE,WAAW,EAAEJ,WAAW,KAAK,IAAI,CAACG,KAAK,CAACH,WAAW,GAAG,EAAE,GAAG,IAAI,CAACG,KAAK,CAACC;KACvE,CAAC;EACJ;EAEA;EACAC,kBAAkBA,CAAA;IAChB,IAAI,CAACJ,WAAW,CAAC;MAAEK,gBAAgB,EAAE;IAAI,CAAE,CAAC;EAC9C;EAEA;EACAC,gBAAgBA,CAAA;IACd,IAAI,CAACN,WAAW,CAAC;MAAEK,gBAAgB,EAAE;IAAK,CAAE,CAAC;EAC/C;EAEA;EACAE,kBAAkBA,CAAA;IAChB,IAAI,CAACP,WAAW,CAAC;MAAEQ,gBAAgB,EAAE;IAAI,CAAE,CAAC;EAC9C;EAEA;;;;EAIAC,gBAAgBA,CAACR,WAAqB;IACpC,IAAI,CAACD,WAAW,CAAC;MACfQ,gBAAgB,EAAE,KAAK;MACvBP,WAAW,EAAEA;KACd,CAAC;EACJ;EAEA;;;;EAIAS,YAAYA,CAACC,cAAsB;IACjC,IAAI,CAACX,WAAW,CAAC;MAAEW;IAAc,CAAE,CAAC;EACtC;EAEA;;;;EAIAC,UAAUA,CAACC,cAAsB;IAC/B,IAAI,CAACb,WAAW,CAAC;MAAEa;IAAc,CAAE,CAAC;EACtC;EAEA;;;;EAIAC,sBAAsBA,CAACA,sBAA+B;IACpD,IAAI,CAACd,WAAW,CAAC;MAAEc;IAAsB,CAAE,CAAC;EAC9C;EAEA;;;;EAIAC,eAAeA,CAACC,SAAqB;IACnC,MAAMC,mBAAmB,GAAG,CAACD,SAAS,IAAI,EAAE,EAAEE,GAAG,CAACC,QAAQ,IAAIA,QAAQ,CAACC,UAAU,CAAC;IAElF,IAAI,CAACpB,WAAW,CAAC;MAAEiB;IAAmB,CAAE,CAAC;EAC3C;EAEA;;;;EAIAI,WAAWA,CAACD,UAAkB;IAC5B,MAAMnB,WAAW,GAAG,IAAI,CAACC,KAAK,CAACD,WAAW,CAACqB,MAAM,CAACF,UAAU,CAAC;IAE7D,IAAI,CAACpB,WAAW,CAAC;MAAEC;IAAW,CAAE,CAAC;EACnC;EAEA;EACAsB,oBAAoBA,CAAA;IAClB,IAAI,CAACvB,WAAW,CAAC;MAAEiB,mBAAmB,EAAE;IAAE,CAAE,CAAC;EAC/C;EAEA;;;;EAIAO,eAAeA,CAACC,SAA6B;IAC3C,MAAMC,mBAAmB,GAAG,CAACD,SAAS,IAAI,EAAE,EAAEP,GAAG,CAACS,QAAQ,IAAIA,QAAQ,CAACC,kBAAkB,CAAC;IAE1F,IAAI,CAAC5B,WAAW,CAAC;MAAE0B;IAAmB,CAAE,CAAC;EAC3C;EAEA;;;;EAIAG,WAAWA,CAACC,UAAkB;IAC5B,MAAM3B,WAAW,GAAG,IAAI,CAACD,KAAK,CAACC,WAAW,CAACmB,MAAM,CAACQ,UAAU,CAAC;IAE7D,IAAI,CAAC9B,WAAW,CAAC;MAAEG;IAAW,CAAE,CAAC;EACnC;EAEA;EACA4B,oBAAoBA,CAAA;IAClB,IAAI,CAAC/B,WAAW,CAAC;MAAE0B,mBAAmB,EAAE;IAAE,CAAE,CAAC;EAC/C;EAEA;EACAM,kBAAkBA,CAAA;IAChB,IAAI,CAAChC,WAAW,CAAC;MAAEiC,gBAAgB,EAAE;IAAI,CAAE,CAAC;EAC9C;EAEA;;;;EAIAC,gBAAgBA,CAAC/B,WAAqB;IACpC,IAAI,CAACH,WAAW,CAAC;MACfiC,gBAAgB,EAAE,KAAK;MACvB9B,WAAW,EAAEA;KACd,CAAC;EACJ;EAEA;EACAgC,4BAA4BA,CAAA;IAC1B,IAAI,CAACnC,WAAW,CAAC;MAAEoC,yBAAyB,EAAE;IAAI,CAAE,CAAC;EACvD;EAEA;;;;EAIAC,0BAA0BA,CAACC,kBAA4B;IACrD,MAAMrC,WAAW,GACfqC,kBAAkB,IAAIA,kBAAkB,CAACC,MAAM,GAAG,CAAC,GAC/C,IAAI,CAACrC,KAAK,CAACD,WAAW,CAACuC,MAAM,CAACC,EAAE,IAAI,CAACH,kBAAkB,CAACI,QAAQ,CAACD,EAAE,CAAC,CAAC,GACrE,IAAI,CAACvC,KAAK,CAACD,WAAW;IAE5B,IAAI,CAACD,WAAW,CAAC;MACfoC,yBAAyB,EAAE,KAAK;MAChCnC,WAAW,EAAEA;KACd,CAAC;EACJ;EAEA;EACA0C,0BAA0BA,CAAA;IACxB,IAAI,CAAC3C,WAAW,CAAC;MAAEoC,yBAAyB,EAAE;IAAI,CAAE,CAAC;EACvD;EAEA;;;;EAIAQ,wBAAwBA,CAACN,kBAA4B;IACnD,MAAMO,eAAe,GACnBP,kBAAkB,IAAIA,kBAAkB,CAACC,MAAM,GAAG,CAAC,GAC/C,IAAI,CAACrC,KAAK,CAAC2C,eAAe,CAACL,MAAM,CAACC,EAAE,IAAI,CAACH,kBAAkB,CAACI,QAAQ,CAACD,EAAE,CAAC,CAAC,GACzE,IAAI,CAACvC,KAAK,CAAC2C,eAAe;IAEhC,IAAI,CAAC7C,WAAW,CAAC;MACfoC,yBAAyB,EAAE,KAAK;MAChCS,eAAe,EAAEA;KAClB,CAAC;EACJ;EAEA;EACAC,4BAA4BA,CAAA;IAC1B,IAAI,CAAC9C,WAAW,CAAC;MAAE+C,yBAAyB,EAAE;IAAI,CAAE,CAAC;EACvD;EAEA;;;;EAIAC,0BAA0BA,CAACC,kBAA4B;IACrD,MAAM9C,WAAW,GACf8C,kBAAkB,IAAIA,kBAAkB,CAACV,MAAM,GAAG,CAAC,GAC/C,IAAI,CAACrC,KAAK,CAACC,WAAW,CAACqC,MAAM,CAACC,EAAE,IAAI,CAACQ,kBAAkB,CAACP,QAAQ,CAACD,EAAE,CAAC,CAAC,GACrE,IAAI,CAACvC,KAAK,CAACC,WAAW;IAE5B,IAAI,CAACH,WAAW,CAAC;MACf+C,yBAAyB,EAAE,KAAK;MAChC5C,WAAW,EAAEA;KACd,CAAC;EACJ;EAEA+C,eAAeA,CAACC,WAAqB;IACnC,MAAMC,mBAAmB,GAAG,IAAI,CAAClD,KAAK,CAACkD,mBAAmB,EAAEZ,MAAM,CAACC,EAAE,IAAI,CAACU,WAAW,CAACT,QAAQ,CAACD,EAAE,CAAC,CAAC;IAEnG,IAAI,CAACzC,WAAW,CAAC;MAAEoD;IAAmB,CAAE,CAAC;EAC3C;EAEA;EACAC,wBAAwBA,CAAA;IACtB,IAAI,CAACrD,WAAW,CAAC;MACfsD,uBAAuB,EAAE3F,uBAAuB,CAAC4F,MAAM;MACvDC,4BAA4B,EAAE,IAAI;MAClCC,cAAc,EAAE,IAAI;MACpBC,mBAAmB,EAAE,IAAI;MACzBC,iBAAiB,EAAE;KACpB,CAAC;EACJ;EAEA;EACAC,mCAAmCA,CAAA;IACjC,IAAI,CAAC5D,WAAW,CAAC;MACfsD,uBAAuB,EAAE3F,uBAAuB,CAACkG,UAAU;MAC3DL,4BAA4B,EAAE,IAAI;MAClCC,cAAc,EAAE,IAAI;MACpBC,mBAAmB,EAAE,IAAI;MACzBC,iBAAiB,EAAE;KACpB,CAAC;EACJ;EAEA;;;;EAIAG,0BAA0BA,CAACC,SAAiB;IAC1C,IAAI,CAAC/D,WAAW,CAAC;MACfsD,uBAAuB,EAAE3F,uBAAuB,CAACqG,GAAG;MACpDR,4BAA4B,EAAE,IAAI;MAClCC,cAAc,EAAE,IAAI;MACpBC,mBAAmB,EAAE,IAAI;MACzBC,iBAAiB,EAAEI;KACpB,CAAC;EACJ;EAEA;;;;;EAKAE,2BAA2BA,CAACC,aAA4B,EAAE/C,QAAkB;IAC1E,IAAI,CAACnB,WAAW,CAAC;MACfsD,uBAAuB,EAAE3F,uBAAuB,CAACwG,IAAI;MACrDX,4BAA4B,EAAEU,aAAa;MAC3CT,cAAc,EAAEtC,QAAQ,CAACC,UAAU;MACnCsC,mBAAmB,EAAE,IAAI;MACzBC,iBAAiB,EAAExC,QAAQ,CAACiD,UAAU,EAAEL;KACzC,CAAC;EACJ;EAEA;;;;;EAKAM,gCAAgCA,CAACH,aAA4B,EAAE/C,QAAkB;IAC/E,IAAI,CAACnB,WAAW,CAAC;MACfsD,uBAAuB,EAAE3F,uBAAuB,CAAC2G,SAAS;MAC1Dd,4BAA4B,EAAEU,aAAa;MAC3CT,cAAc,EAAE,IAAI;MACpBC,mBAAmB,EAAEvC,QAAQ,CAACC,UAAU;MACxCuC,iBAAiB,EAAExC,QAAQ,CAACiD,UAAU,EAAEL;KACzC,CAAC;EACJ;EAEA;EACAQ,2BAA2BA,CAAA;IACzB,IAAI,CAACvE,WAAW,CAAC;MAAEwE,gBAAgB,EAAE;IAAI,CAAE,CAAC;EAC9C;EAEA;EACAC,yBAAyBA,CAAA;IACvB,IAAI,CAACzE,WAAW,CAAC;MAAEwE,gBAAgB,EAAE;IAAK,CAAE,CAAC;EAC/C;EAEA;EACAE,wBAAwBA,CAAA;IACtB,IAAI,CAAC1E,WAAW,CAAC;MAAEwE,gBAAgB,EAAE;IAAI,CAAE,CAAC;EAC9C;EAEA;EACAG,sBAAsBA,CAAA;IACpB,IAAI,CAAC3E,WAAW,CAAC;MAAEwE,gBAAgB,EAAE;IAAK,CAAE,CAAC;EAC/C;EAEA;EACAI,sBAAsBA,CAAA;IACpB,IAAI,CAAC5E,WAAW,CAAC;MAAE6E,gBAAgB,EAAE;IAAI,CAAE,CAAC;EAC9C;EAEA;EACAC,oBAAoBA,CAAA;IAClB,IAAI,CAAC9E,WAAW,CAAC;MAAE6E,gBAAgB,EAAE;IAAK,CAAE,CAAC;EAC/C;EAEA;EACAE,mBAAmBA,CAAA;IACjB,IAAI,CAAC/E,WAAW,CAAC;MAAE6E,gBAAgB,EAAE;IAAI,CAAE,CAAC;EAC9C;EAEA;EACAG,iBAAiBA,CAAA;IACf,IAAI,CAAChF,WAAW,CAAC;MAAE6E,gBAAgB,EAAE;IAAK,CAAE,CAAC;EAC/C;EAEA;;;;EAIAI,0BAA0BA,CAACpC,eAAyB;IAClD,IAAI,CAAC7C,WAAW,CAAC;MAAE6C;IAAe,CAAE,CAAC;EACvC;EAEA;;;;EAIAqC,wBAAwBA,CAAC9B,mBAA6B;IACpD,IAAI,CAACpD,WAAW,CAAC;MAAEoD;IAAmB,CAAE,CAAC;EAC3C;EAEA;;;;EAIA+B,4BAA4BA,CAAChC,WAAqB,EAAEiC,QAAc;IAChE,MAAMhC,mBAAmB,GAAG5F,SAAS,CAAC6H,KAAK,CAAC,IAAI,CAACnF,KAAK,CAACkD,mBAAmB,EAAED,WAAW,CAAC;IAExF,IAAI,CAACnD,WAAW,CAAC;MAAEoD,mBAAmB;MAAEgC;IAAQ,CAAE,CAAC;EACrD;EAEA;;;;EAIAE,0BAA0BA,CAACC,kBAA0B;IACnD,IAAI,CAACvF,WAAW,CAAC;MACfwF,0BAA0B,EAAE,IAAI;MAChCC,0BAA0B,EAAEF,kBAAkB;MAC9CG,+BAA+B,EAAE,IAAI;MACrCC,4BAA4B,EAAE;KAC/B,CAAC;EACJ;EAEA;;;;EAIAC,kCAAkCA,CAACC,OAAiB;IAClD,IAAI,CAAC7F,WAAW,CAAC;MACf0F,+BAA+B,EAAEG,OAAO;MACxCF,4BAA4B,EAAE;KAC/B,CAAC;EACJ;EAEA;EACAG,2BAA2BA,CAAA;IACzB,IAAI,CAAC9F,WAAW,CAAC;MACfwF,0BAA0B,EAAE,KAAK;MACjCC,0BAA0B,EAAE,IAAI;MAChCC,+BAA+B,EAAE;KAClC,CAAC;EACJ;EAEA;EACAK,oBAAoBA,CAAA;IAClB,IAAI,CAAC/F,WAAW,CAAC;MACf6C,eAAe,EAAE,EAAE;MACnBO,mBAAmB,EAAE;KACtB,CAAC;EACJ;EAEA;EACA4C,uBAAuBA,CAAA;IACrB,IAAI,CAAChG,WAAW,CAAC;MAAEiG,uBAAuB,EAAE;IAAI,CAAE,CAAC;EACrD;EAEA;EACAC,wBAAwBA,CAAA;IACtB,IAAI,CAAClG,WAAW,CAAC;MAAEiG,uBAAuB,EAAE;IAAK,CAAE,CAAC;EACtD;EAEA;EACAE,iCAAiCA,CAAA;IAC/B,IAAI,CAACnG,WAAW,CAAC;MAAEoG,mBAAmB,EAAE;IAAI,CAAE,CAAC;EACjD;EAEA;EACAC,+BAA+BA,CAAA;IAC7B,IAAI,CAACrG,WAAW,CAAC;MAAEoG,mBAAmB,EAAE;IAAK,CAAE,CAAC;EAClD;;AAhgBgBxI,kBAAA,CAAAG,aAAa,GAAiC;EAC5DsC,gBAAgB,EAAE,KAAK;EACvBG,gBAAgB,EAAE,KAAK;EACvByB,gBAAgB,EAAE,KAAK;EACvBuC,gBAAgB,EAAE,KAAK;EACvBK,gBAAgB,EAAE,KAAK;EACvBzC,yBAAyB,EAAE,KAAK;EAChCW,yBAAyB,EAAE,KAAK;EAChChD,WAAW,EAAE,IAAI;EACjBE,WAAW,EAAE,EAAE;EACfE,WAAW,EAAE,EAAE;EACfc,mBAAmB,EAAE,EAAE;EACvBS,mBAAmB,EAAE,EAAE;EACvB4B,uBAAuB,EAAE3F,uBAAuB,CAAC4F,MAAM;EACvDC,4BAA4B,EAAE,IAAI;EAClCC,cAAc,EAAE,IAAI;EACpBC,mBAAmB,EAAE,IAAI;EACzB/C,cAAc,EAAE,IAAI;EACpBE,cAAc,EAAE,IAAI;EACpBC,sBAAsB,EAAE,KAAK;EAC7BwF,UAAU,EAAE,EAAE;EACd3C,iBAAiB,EAAE,IAAI;EACvBd,eAAe,EAAE,EAAE;EACnBuC,QAAQ,EAAE,IAAImB,IAAI,EAAE;EACpBnD,mBAAmB,EAAE,IAAI;EACzBqC,0BAA0B,EAAE,IAAI;EAChCC,+BAA+B,EAAE,IAAI;EACrCF,0BAA0B,EAAE,KAAK;EACjCG,4BAA4B,EAAE,KAAK;EACnCM,uBAAuB,EAAE,KAAK;EAC9BG,mBAAmB,EAAE;CACtB;;mBAhCUxI,kBAAkB,EAAA4I,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAhJ,YAAA;AAAA;;SAAlBE,kBAAkB;EAAA+I,OAAA,EAAlB/I,kBAAkB,CAAAgJ,IAAA;EAAAC,UAAA,EADL;AAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}