<gdco-action-bar wrap="wrap">
  <div fxLayout="row wrap" fxLayoutGap="16px" class="actions">
    <button
      class="template-action-button"
      mat-flat-button
      color="primary"
      (click)="onNewRecurringTaskClicked()"
      gdcoTrack="NewRecurringTaskClicked"
      [gdcoTrackData]="{ view: 'calendar' }"
    >
      New recurring task
    </button>
    <button
      class="template-action-button"
      mat-stroked-button
      color="primary"
      [disabled]="!selectedSchedules?.length || deleting"
      (click)="onDeleteClicked()"
      gdcoTrack="DeleteSchedulesClicked"
      [gdcoTrackData]="{ count: selectedSchedules?.length }"
    >
      <gdco-spinner-button-content
        text="Delete"
        workingText="Deleting"
        [working]="deleting"
      ></gdco-spinner-button-content>
    </button>
    <a mat-stroked-button class="template-action-button" color="primary" routerLink=".." gdcoTrack="ViewListClicked"
      ><mat-icon>view_list</mat-icon> View list</a
    >
  </div>

  <div class="filter-item">
    <gdco-label class="filter-label">Campus:</gdco-label>
    <mat-form-field class="gdco-compact">
      <mat-select
        gdcoMatSelectAccessibility
        aria-label="Select campus"
        placeholder="All"
        [value]="selectedCampuses"
        multiple
        (selectionChange)="onCampusSelectionChanged($event)"
      >
        <mat-option
          *ngFor="let campus of campuses"
          [value]="campus"
          gdcoTrack="ScheduleCampusFilter"
          [gdcoTrackData]="{ option: campus }"
          >{{ campus }}</mat-option
        >
      </mat-select>
    </mat-form-field>
  </div>

  <div class="filter-item">
    <gdco-label class="filter-label">Created by:</gdco-label>
    <mat-form-field class="gdco-compact">
      <mat-select
        gdcoMatSelectAccessibility
        aria-label="Created by"
        placeholder="Anyone"
        [value]="selectedUser"
        (selectionChange)="onUserSelectionChanged($event)"
      >
        <mat-option [value]="null" gdcoTrack="ScheduleCreatedByFilter" [gdcoTrackData]="{ option: 'Anyone' }"
          >Anyone</mat-option
        >
        <mat-option [value]="userId" gdcoTrack="ScheduleCreatedByFilter" [gdcoTrackData]="{ option: 'Me' }"
          >Me</mat-option
        >
        <mat-option
          *ngFor="let user of users"
          [value]="user.ObjectId"
          gdcoTrack="ScheduleCreatedByFilter"
          [gdcoTrackData]="{ option: 'User' }"
        >
          <span>{{ user.DisplayName }}</span>
        </mat-option>
      </mat-select>
    </mat-form-field>
  </div>

  <mat-checkbox
    *ngIf="hasCompletedSchedules"
    [checked]="showCompletedSchedules"
    (change)="onShowCompletedSchedulesChanged($event)"
    >Show completed schedules</mat-checkbox
  >
</gdco-action-bar>
