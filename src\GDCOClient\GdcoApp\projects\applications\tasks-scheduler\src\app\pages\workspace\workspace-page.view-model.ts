/**
 * @license
 * Copyright (c) Microsoft Corporation. All rights reserved.
 */

import { FrequencyType, RecurringTask, RecurringTaskConverter, PreviewTemplate } from '../../common';
import { TemplateForm, TemplateFormConverter } from './template-details/edit';
import { TemplateDetailsStore } from './template-details/template-details.store';
import { WorkspacePageStore } from './workspace-page.store';
import { Injectable } from '@angular/core';
import { GdcoAuth } from '@gdco/auth';
import { GdcoQueryBuilder, GdcoQueryOperator } from '@gdco/common';
import {
  GdcoNotifications,
  GdcoConfirmationDialog,
  GdcoConfirmationData,
  GdcoAppInsights,
  gdcoUtils
} from '@gdco/core';
import {
  AzureGraphObject,
  DatacentersManager,
  DomainDataManager,
  FaultCode,
  Metadata,
  SearchTasksPayload,
  Task,
  TasksManager,
  UsersManager
} from '@gdco/core-reference-systems/gdco-service';
import {
  DeleteSchedulePayload,
  DeleteScheduleTemplatePayload,
  GetScheduleByIdPayload,
  ListScheduleInstancesByScheduleIdPayload,
  ListScheduleInstancesPageByScheduleIdPayload,
  ListScheduleTemplateVersionsByIdPayload,
  ListSchedulesByWorkspaceIdPayload,
  PreviewUnsavedScheduleTemplatePayload,
  Schedule,
  ScheduleInstance,
  ScheduleInstanceState,
  ScheduleState,
  ScheduleTemplate,
  ScheduleTemplateParameterDataType,
  ScheduleWorkspace,
  SchedulerManager
} from '@gdco/reference-systems/gdco-service';
import {
  Action,
  isActionCanceled,
  ActionPayloadContext,
  ActionType,
  ActionContext,
  awaitable,
  filterItems,
  sortItems,
  mapItems,
  distictItems,
  groupItemsBy
} from '@gdco/store';
import { RecurringTaskFieldResolver } from '@tasks/scheduling';
import { Observable, combineLatest } from 'rxjs';
import { map } from 'rxjs/operators';

/**
 * View model for the workspace page. All events and/or logic in the workspace page component should be delegated here instead.
 */
@Injectable({ providedIn: 'root' })
export class WorkspacePageViewModel {
  readonly workspace$: Observable<ScheduleWorkspace>;
  readonly schedules$: Observable<Schedule[]>;
  readonly activeSchedules$: Observable<Schedule[]>;
  readonly mySchedules$: Observable<Schedule[]>;
  readonly templates$: Observable<ScheduleTemplate[]>;
  readonly template$: Observable<ScheduleTemplate>;
  readonly diffModifiedTemplate$: Observable<ScheduleTemplate>;
  readonly diffOriginalTemplate$: Observable<ScheduleTemplate>;
  readonly selectedSchedules$: Observable<Schedule[]>;
  readonly selectedTemplates$: Observable<ScheduleTemplate[]>;
  readonly selectedTemplate$: Observable<ScheduleTemplate>;
  readonly selectedFaultCode$: Observable<FaultCode>;
  readonly filteredSchedules$: Observable<Schedule[]>;
  readonly scheduleCampuses$: Observable<string[]>;
  readonly filteredScheduleCampuses$: Observable<string[]>;
  readonly scheduleCreatedByUsers$: Observable<AzureGraphObject[]>;
  readonly faultCodes$: Observable<FaultCode[]>;
  readonly faultCodeMap$: Observable<{ [code: number]: FaultCode }>;
  readonly templateFaultCodes$: Observable<FaultCode[]>;
  readonly metadata$: Observable<Metadata[]>;
  readonly viewSchedules$: Observable<Schedule[]>;
  readonly scheduleInstances$: Observable<ScheduleInstance[]>;
  readonly selectedScheduleInstance$: Observable<ScheduleInstance>;
  readonly selectedScheduleInstanceSchedule$: Observable<Schedule>;
  readonly selectedScheduleInstanceTasks$: Observable<Task[]>;
  readonly schedulesByCampus$: Observable<{ [name: string]: Schedule[] }>;
  readonly schedulesById$: Observable<{ [id: string]: Schedule }>;
  readonly hasCompletedSchedules$: Observable<boolean>;
  readonly editSchedule$: Observable<Schedule>;
  readonly duplicateSchedule$: Observable<Schedule>;
  readonly editScheduleTemplate$: Observable<ScheduleTemplate>;
  readonly duplicateScheduleTemplate$: Observable<ScheduleTemplate>;
  readonly scheduleFieldMap$: Observable<{ [id: string]: { [name: string]: any } }>;

  /** Gets the current workspace. */
  get workspace(): ScheduleWorkspace {
    return this._schedulerManager.workspaces.get(this.store.state.workspaceId);
  }

  /** Gets all schedules in the current workspace. */
  get schedules(): Schedule[] {
    return this._schedulerManager.schedules.getEntities(this.store.state.scheduleIds);
  }

  /** Gets all templates in the current workspace. */
  get templates(): ScheduleTemplate[] {
    return this._schedulerManager.templates.getEntities(this.store.state.templateIds);
  }

  get template(): ScheduleTemplate {
    return this._schedulerManager.templates.get(this.templateDetailsStore.state.templateId);
  }

  /** Gets all schedule instances currently displayed. */
  get instances(): ScheduleInstance[] {
    return this._schedulerManager.instances.getEntities(this.store.state.scheduleInstanceIds);
  }

  constructor(
    public readonly store: WorkspacePageStore,
    public readonly templateDetailsStore: TemplateDetailsStore,
    private _auth: GdcoAuth,
    private _confirmationDialog: GdcoConfirmationDialog,
    private _notifications: GdcoNotifications,
    private _appInsights: GdcoAppInsights,
    private _schedulerManager: SchedulerManager,
    private _domainDataManager: DomainDataManager,
    private _datacentersManager: DatacentersManager,
    private _usersManager: UsersManager,
    private _tasksManager: TasksManager,
    private _fieldResolver: RecurringTaskFieldResolver
  ) {
    this.workspace$ = this._schedulerManager.workspaces.observe(this.store.workspaceId$);
    this.schedules$ = this._schedulerManager.schedules.observeEntities(this.store.scheduleIds$);
    this.activeSchedules$ = this.schedules$.pipe(filterItems(schedule => schedule.state === ScheduleState.Active));

    this.hasCompletedSchedules$ = this.schedules$.pipe(
      map(schedules => !!schedules?.find(schedule => schedule.state === ScheduleState.Complete))
    );

    this.filteredSchedules$ = combineLatest([
      this.schedules$,
      this.store.selectedCampuses$,
      this.store.selectedUserId$,
      this.store.showCompletedSchedules$
    ]).pipe(
      map(([schedules, selectedCampuses, userId, showCompletedSchedules]) => {
        return this._filterSchedules(schedules, selectedCampuses, userId, showCompletedSchedules);
      })
    );

    this.mySchedules$ = this.filteredSchedules$.pipe(
      filterItems(schedule => schedule.createdBy === this._auth.currentUser.userId),
      sortItems((a, b) => {
        const aDatacenter = this._fieldResolver.getFieldForSchedule(a, 'DatacenterCode') || '';
        const bDatacenter = this._fieldResolver.getFieldForSchedule(b, 'DatacenterCode') || '';

        // Sort by datacenter before sorting by name
        return aDatacenter.toLowerCase().localeCompare(bDatacenter.toLowerCase());
      }),
      sortItems((a, b) => a.name.toLowerCase().localeCompare(b.name.toLowerCase()))
    );

    this.template$ = this._schedulerManager.templates.observe(this.templateDetailsStore.templateId$);
    this.templates$ = this._schedulerManager.templates.observeEntities(this.store.templateIds$);
    this.selectedSchedules$ = this._schedulerManager.schedules.observeEntities(this.store.selectedScheduleIds$);
    this.selectedTemplates$ = this._schedulerManager.templates.observeEntities(this.store.selectedTemplateIds$);

    this.diffOriginalTemplate$ = this._schedulerManager.templateVersions.observe(
      combineLatest([this.templateDetailsStore.templateId$, this.templateDetailsStore.diffOriginalVersion$]).pipe(
        map(([templateId, version]) => this._createTemplateVersionKey(templateId, version))
      )
    );
    this.diffModifiedTemplate$ = this._schedulerManager.templateVersions.observe(
      combineLatest([this.templateDetailsStore.templateId$, this.templateDetailsStore.diffModifiedVersion$]).pipe(
        map(([templateId, version]) => this._createTemplateVersionKey(templateId, version))
      )
    );

    this.viewSchedules$ = this._schedulerManager.schedules.observeEntities(this.store.viewScheduleIds$);
    this.scheduleInstances$ = this._schedulerManager.instances.observeEntities(this.store.scheduleInstanceIds$);

    this.selectedScheduleInstance$ = this._schedulerManager.instances.observe(this.store.selectedScheduleInstanceId$);
    this.selectedScheduleInstanceSchedule$ = this._schedulerManager.schedules.observe(
      this.selectedScheduleInstance$.pipe(map(instance => instance?.scheduleId))
    );
    this.selectedScheduleInstanceTasks$ = this._tasksManager.tasks.observeEntities(
      this.store.selectedScheduleInstanceTaskIds$
    );

    this.selectedTemplate$ = combineLatest([this.templates$, this.store.selectedFaultCode$]).pipe(
      map(([templates, faultCode]) => {
        if (!templates || !faultCode) {
          return null;
        }

        return templates.find(template => this._faultCodeExistsInTemplate(template, faultCode));
      })
    );

    this.selectedFaultCode$ = this._domainDataManager.faultCodes.observe(
      this.store.selectedFaultCode$.pipe(map(faultCode => faultCode?.toString()))
    );

    // We need to depend on templates$ here because the field resolver uses them internally
    this.scheduleCampuses$ = combineLatest([this.schedules$, this.templates$]).pipe(
      map(([schedules]) => this._getCampusesFromSchedules(schedules))
    );

    // We need to depend on templates$ here because the field resolver uses them internally
    this.filteredScheduleCampuses$ = combineLatest([this.filteredSchedules$, this.templates$]).pipe(
      map(([schedules]) => this._getCampusesFromSchedules(schedules))
    );

    const createdByUserIds = this.schedules$.pipe(
      mapItems(schedule => schedule.createdBy),
      distictItems()
    );

    this.scheduleCreatedByUsers$ = this._usersManager.users
      .observeEntities(createdByUserIds)
      .pipe(sortItems((a, b) => a.DisplayName.toLowerCase().localeCompare(b.DisplayName.toLowerCase())));

    this.faultCodes$ = this._domainDataManager.faultCodes.observeAll();
    this.metadata$ = this._domainDataManager.metadata$;

    this.faultCodeMap$ = this.faultCodes$.pipe(groupItemsBy('code', true));

    this.templateFaultCodes$ = combineLatest([this.templates$, this.faultCodes$]).pipe(
      map(([templates, faultCodes]) => {
        if (!templates || !faultCodes) {
          return [];
        }

        return faultCodes
          .filter(faultCode => {
            return templates.find(template => {
              return (
                this._faultCodeExistsInTemplate(template, faultCode.code) && !template.clientData?.disableUiCreation
              );
            });
          })
          .sort((a, b) => a.description.toLowerCase().localeCompare(b.description.toLowerCase()));
      })
    );

    // We need to depend on templates$ here because the field resolver uses them internally
    this.schedulesByCampus$ = combineLatest([this.scheduleCampuses$, this.filteredSchedules$, this.templates$]).pipe(
      map(([campuses, schedules]) => {
        if (!campuses || !schedules) {
          return {};
        }

        return campuses.reduce<{ [campus: string]: Schedule[] }>((prev, curr) => {
          prev[curr] = schedules
            .filter(schedule => {
              const campus = this._fieldResolver.getFieldForSchedule(schedule, 'FacilityCampus');

              return this._getCampusName(campus) === curr;
            })
            .sort((a, b) => {
              const aDatacenter = this._fieldResolver.getFieldForSchedule(a, 'DatacenterCode') || '';
              const bDatacenter = this._fieldResolver.getFieldForSchedule(b, 'DatacenterCode') || '';

              // Sort by datacenter before sorting by name
              return aDatacenter.toLowerCase().localeCompare(bDatacenter.toLowerCase());
            })
            .sort((a, b) => a.name.toLowerCase().localeCompare(b.name.toLowerCase()));

          return prev;
        }, {});
      })
    );

    this.schedulesById$ = this.schedules$.pipe(groupItemsBy('scheduleId', true));

    this.editSchedule$ = this._schedulerManager.schedules.observe(this.store.editScheduleId$);
    this.editScheduleTemplate$ = this._schedulerManager.templates.observe(
      this.editSchedule$.pipe(map(schedule => schedule?.templateId))
    );

    this.duplicateSchedule$ = this._schedulerManager.schedules.observe(this.store.duplicateScheduleId$);
    this.duplicateScheduleTemplate$ = this._schedulerManager.templates.observe(
      this.duplicateSchedule$.pipe(map(schedule => schedule?.templateId))
    );

    // We need to depend on templates$ here because the field resolver uses them internally
    this.scheduleFieldMap$ = combineLatest([this.schedules$, this.templates$]).pipe(
      map(([schedules]) => {
        return schedules.reduce<{ [id: string]: { [name: string]: any } }>((prev, curr) => {
          prev[curr.scheduleId] = {
            FacilityCampus: this._fieldResolver.getFieldForSchedule(curr, 'FacilityCampus'),
            DatacenterCode: this._fieldResolver.getFieldForSchedule(curr, 'DatacenterCode'),
            Colocation: this._fieldResolver.getFieldForSchedule(curr, 'Colocation'),
            Group: this._fieldResolver.getFieldForSchedule(curr, 'Group'),
            AssignedTo: this._fieldResolver.getFieldForSchedule(curr, 'AssignedTo'),
            FacilityTimeZone: this._fieldResolver.getFieldForSchedule(curr, 'FacilityTimeZone')
          };

          return prev;
        }, {});
      })
    );
  }

  /**
   * Initializes the workspace page, loading all schedules and templates in the workspace.
   * @param context.payload The id of the workspace.
   */
  @Action({ type: ActionType.CancelInProgress })
  async initializePage(context: ActionPayloadContext<string>): Promise<void> {
    const workspaceId = context.payload;

    this.store.initializePageStart(workspaceId);

    await this.loadWorkspace(context);
    await Promise.all([this.loadSchedules(context), this.loadTemplates(context)]);

    await this.loadMetadata(context);
  }

  /**
   * Initializes the schedule instances page by schedule id.
   * @param context.payload The schedule id to load the schedule instances page.
   */
  @Action({ type: ActionType.CancelInProgress })
  async initializeScheduleInstancesPageByScheduleId(context: ActionPayloadContext<string>): Promise<void> {
    const scheduleId = context.payload;

    this.store.clearViewedSchedules();
    await this.loadScheduleInstances(new ActionPayloadContext([scheduleId]));

    const schedule = this._schedulerManager.schedules.get(scheduleId);
    if (schedule?.state === ScheduleState.Complete) {
      this.store.showCompletedSchedules(true);
    }
  }

  /**
   * Loads the workspace with the given id.
   * @param context.payload The id of the workspace to load.
   */
  @Action({ type: ActionType.ReuseInProgress })
  async loadWorkspace(context: ActionPayloadContext<string>): Promise<void> {
    try {
      this.store.loadWorkspaceStart();

      await this._schedulerManager.getWorkspaceById(context);
    } catch (error) {
      if (!isActionCanceled(error)) {
        this._notifications.addError('Failed to load workspace.', error);
      }

      throw error; // Always re-throw errors
    } finally {
      this.store.loadWorkspaceEnd();
    }
  }

  /** Loads all schedules for the current workspace. */
  @Action({ type: ActionType.ReuseInProgress })
  async loadSchedules(context: ActionContext): Promise<void> {
    let scheduleIds: string[] = [];
    const { workspaceId } = this.store.state;

    this.store.loadSchedulesStart();

    // Verify the workspace is loaded
    await this.loadWorkspace(new ActionPayloadContext(workspaceId, context));

    try {
      const payload: ListSchedulesByWorkspaceIdPayload = {
        workspaceId: workspaceId
      };

      const schedules = await this._schedulerManager.listSchedulesByWorkspaceId(
        new ActionPayloadContext(payload, context)
      );

      // TODO: Remove this 'loadDatacenters' call after SOP schedules are updated to include campus
      await this._datacentersManager.loadDatacenters(context);

      scheduleIds = schedules.map(schedule => schedule.scheduleId);
    } catch (error) {
      if (!isActionCanceled(error)) {
        this._notifications.addError('Failed to load existing recurring tasks.', error);
      }

      throw error; // Always re-throw errors
    } finally {
      this.store.loadSchedulesEnd(scheduleIds);
    }
  }

  /** Loads all schedule templates for the current workspace. */
  @Action({ type: ActionType.ReuseInProgress })
  async loadTemplates(context: ActionContext): Promise<void> {
    let templateIds: string[] = [];
    const { workspaceId } = this.store.state;

    this.store.loadTemplatesStart();

    // Verify the workspace is loaded
    await this.loadWorkspace(new ActionPayloadContext(workspaceId, context));

    try {
      const schedules = await this._schedulerManager.listScheduleTemplatesByWorkspaceId(
        new ActionPayloadContext(workspaceId, context)
      );

      templateIds = schedules.map(schedule => schedule.scheduleTemplateId);
    } catch (error) {
      if (!isActionCanceled(error)) {
        this._notifications.addError('Failed to load existing templates.', error);
      }

      throw error; // Always re-throw errors
    } finally {
      this.store.loadTemplatesEnd(templateIds);
    }
  }

  @Action()
  async loadTemplateVersions(context: ActionPayloadContext<string>): Promise<void> {
    const templateId = context.payload;
    const { workspaceId } = this.store.state;

    try {
      const payload: ListScheduleTemplateVersionsByIdPayload = {
        workspaceId: workspaceId,
        templateId: templateId
      };

      const templates = await this._schedulerManager.listScheduleTemplateVersionsById(
        new ActionPayloadContext(payload, context)
      );

      this.templateDetailsStore.diffTemplateView(this.template.version, this.template.version - 1);
    } catch (error) {
      if (!isActionCanceled(error)) {
        this._notifications.addError('Failed to load existing templates.', error);
      }

      throw error; // Always re-throw errors
    } finally {
    }
  }

  /**
   * Loads all scheduled instances for the schedules with the given Ids.
   * @param context.payload A list of schedule Ids to load the instances for.
   */
  @Action({ type: ActionType.CancelInProgress })
  async loadScheduleInstances(context: ActionPayloadContext<string[]>): Promise<void> {
    const scheduleIds = context.payload;
    const { workspaceId, viewDate } = this.store.state;
    let scheduleInstanceIds: string[] = [];

    this.store.loadScheduleInstancesStart(scheduleIds);

    // Verify the workspace is loaded
    await this.loadWorkspace(new ActionPayloadContext(workspaceId, context));

    try {
      const promises: Promise<ScheduleInstance[]>[] = [];

      // Load the instances for all given schedules in parallel
      for (const scheduleId of scheduleIds) {
        const payload: ListScheduleInstancesByScheduleIdPayload = {
          workspaceId: workspaceId,
          scheduleId: scheduleId
        };

        // Load all active instances
        const promise = this._schedulerManager.listScheduleInstancesByScheduleId(
          new ActionPayloadContext(payload, context)
        );

        promises.push(promise);

        // Don't load pages beyond the end date or before the start date
        const schedule = this._schedulerManager.schedules.get(scheduleId);
        const startDate = schedule?.recurrencePattern.startDate;
        const endDate = schedule?.recurrencePattern.endDate;
        if (
          !schedule ||
          (viewDate.getFullYear() >= startDate.getFullYear() &&
            (!endDate || viewDate.getFullYear() <= endDate.getFullYear()))
        ) {
          const paginPayload: ListScheduleInstancesPageByScheduleIdPayload = {
            workspaceId: workspaceId,
            scheduleId: scheduleId,
            pageDate: viewDate
          };

          // Load current page of instances
          const pagingPromise = this._schedulerManager.listScheduleInstancesPageByScheduleId(
            new ActionPayloadContext(paginPayload, context)
          );

          promises.push(pagingPromise);
        }
      }

      const results = await Promise.all(promises);

      scheduleInstanceIds = results.reduce<string[]>((prev, curr) => {
        return prev.concat(...curr.map(instance => instance.scheduleInstanceId));
      }, []);

      scheduleInstanceIds = gdcoUtils.distinct(scheduleInstanceIds);
    } catch (error) {
      if (!isActionCanceled(error)) {
        this._notifications.addError('Failed to load schedule instances.', error);
      }

      throw error; // Always re-throw errors
    } finally {
      this.store.loadScheduleInstancesEnd(scheduleInstanceIds);
    }
  }

  /** Loads all scheduled instances in the current year for the given date. */
  @Action({ type: ActionType.ReuseInProgress })
  async loadScheduleInstancesPage(context: ActionPayloadContext<Date>): Promise<void> {
    const pageDate = context.payload;
    const { workspaceId } = this.store.state;
    let scheduleInstanceIds: string[] = [];

    // Verify the workspace is loaded
    await this.loadWorkspace(new ActionPayloadContext(workspaceId, context));

    try {
      const promises: Promise<ScheduleInstance[]>[] = [];

      // Load the instance pages for all current viewed schedules in parallel
      for (const scheduleId of this.store.state.viewScheduleIds) {
        const schedule = this._schedulerManager.schedules.get(scheduleId);
        const startDate = schedule?.recurrencePattern.startDate;
        const endDate = schedule?.recurrencePattern.endDate;

        // Don't load pages beyond the end date or before the start date
        if (
          !schedule ||
          (pageDate.getFullYear() >= startDate.getFullYear() &&
            (!endDate || pageDate.getFullYear() <= endDate.getFullYear()))
        ) {
          const payload: ListScheduleInstancesPageByScheduleIdPayload = {
            workspaceId: workspaceId,
            scheduleId: scheduleId,
            pageDate: pageDate
          };

          const promise = this._schedulerManager.listScheduleInstancesPageByScheduleId(
            new ActionPayloadContext(payload, context)
          );

          promises.push(promise);
        }
      }

      const results = await Promise.all(promises);

      scheduleInstanceIds = results.reduce<string[]>((prev, curr) => {
        return prev.concat(...curr.map(instance => instance.scheduleInstanceId));
      }, []);
    } catch (error) {
      if (!isActionCanceled(error)) {
        this._notifications.addError('Failed to load schedule instances.', error);
      }

      throw error; // Always re-throw errors
    } finally {
      this.store.loadScheduleInstancesPageEnd(scheduleInstanceIds, pageDate);
    }
  }

  /** Loads all metadata needed by the workspace. */
  @Action({ type: ActionType.ReuseInProgress })
  async loadMetadata(context: ActionContext): Promise<void> {
    const schedules = this._schedulerManager.schedules.getEntities(this.store.state.scheduleIds);
    const userIds = schedules.map(schedule => schedule.createdBy);

    await Promise.all([
      this._domainDataManager.loadTaskMetadata(context),
      this._domainDataManager.loadFaultCodes(context),
      this._datacentersManager.loadDatacenters(context),
      this._usersManager.getUsersByIds(new ActionPayloadContext(userIds, context))
    ]);
  }

  /**
   * Deletes the given schedule.
   * @param context.payload The schedule to delete.
   */
  @Action()
  async deleteSchedule(context: ActionPayloadContext<Schedule>): Promise<boolean> {
    const schedule = context.payload;

    try {
      const dialogData: GdcoConfirmationData = {
        title: 'Delete recurring task',
        text: 'Are you sure you want to delete the selected recurring task?'
      };

      // Require user confirmation
      const confirmed = await awaitable(this._confirmationDialog.confirm(dialogData), context.cancellationToken);
      if (!confirmed) {
        return false;
      }

      const payload: DeleteSchedulePayload = {
        workspaceId: schedule.workspaceId,
        scheduleId: schedule.scheduleId
      };

      await this._schedulerManager.deleteSchedule(new ActionPayloadContext(payload, context));

      this._appInsights.trackEvent('ScheduleDeleted', {
        workspaceId: schedule.workspaceId,
        templateId: schedule.templateId,
        datacenter: this._fieldResolver.getFieldForSchedule(schedule, 'DatacenterCode'),
        recurrenceType: schedule.recurrencePattern.recurrenceType
      });

      const removedInstances = this.instances
        .filter(instance => schedule.scheduleId === instance.scheduleId)
        .map(instance => instance.scheduleInstanceId);

      // Make sure the schedule doesn't stay selected
      this.store.deselectAllSchedules();
      this.store.removeInstances(removedInstances);
    } catch (error) {
      if (!isActionCanceled(error)) {
        this._notifications.addError('Failed to delete recurring task', error);
      }

      throw error; // Always re-throw errors
    }

    return true;
  }

  /** Deletes all schedules selected by the user on the schedules page. */
  @Action()
  async deleteSelectedSchedules(context: ActionContext): Promise<void> {
    const deletedIds: string[] = [];
    const { workspaceId, selectedScheduleIds } = this.store.state;

    try {
      const dialogData: GdcoConfirmationData = {
        title: 'Delete selected recurring tasks',
        text: 'Are you sure you want to delete the selected recurring tasks?'
      };

      // Require user confirmation
      const confirmed = await awaitable(this._confirmationDialog.confirm(dialogData), context.cancellationToken);
      if (!confirmed) {
        return;
      }

      this.store.deleteSelectedSchedulesStart();

      const promises: Promise<any>[] = [];

      // Delete schedules in parallel
      for (const scheduleId of selectedScheduleIds) {
        const payload: DeleteSchedulePayload = {
          workspaceId: workspaceId,
          scheduleId: scheduleId
        };

        const promise = this._schedulerManager.deleteSchedule(new ActionPayloadContext(payload, context));
        promises.push(promise);

        deletedIds.push(scheduleId);
      }

      await Promise.all(promises);

      const removedInstanceIds = this.instances
        .filter(instance => deletedIds.includes(instance.scheduleId))
        .map(instance => instance.scheduleInstanceId);

      this.store.removeInstances(removedInstanceIds);

      this._appInsights.trackEvent('BulkSchedulesDeleted', {
        workspaceId: workspaceId,
        count: selectedScheduleIds.length.toString(),
        page: 'list'
      });

      // Make sure the schedules don't stay selected
      this.store.deselectAllSchedules();
    } catch (error) {
      if (!isActionCanceled(error)) {
        this._notifications.addError('Failed to delete selected recurring tasks', error);
      }

      throw error; // Always re-throw errors
    } finally {
      this.store.deleteSelectedSchedulesEnd(deletedIds);
    }
  }

  /** Deletes all schedules selected by the user on the calendar page. */
  @Action()
  async deleteViewedSchedules(context: ActionContext): Promise<void> {
    const deletedIds: string[] = [];
    const { workspaceId, viewScheduleIds } = this.store.state;

    try {
      const dialogData: GdcoConfirmationData = {
        title: 'Delete selected recurring tasks',
        text: 'Are you sure you want to delete the selected recurring tasks?'
      };

      // Require user confirmation
      const confirmed = await awaitable(this._confirmationDialog.confirm(dialogData), context.cancellationToken);
      if (!confirmed) {
        return;
      }

      this.store.deleteViewedSchedulesStart();

      const promises: Promise<any>[] = [];

      // Delete schedules in parallel
      for (const scheduleId of viewScheduleIds) {
        const payload: DeleteSchedulePayload = {
          workspaceId: workspaceId,
          scheduleId: scheduleId
        };

        const promise = this._schedulerManager.deleteSchedule(new ActionPayloadContext(payload, context));
        promises.push(promise);

        deletedIds.push(scheduleId);
      }

      await Promise.all(promises);

      const removedInstanceIds = this.instances
        .filter(instance => deletedIds.includes(instance.scheduleId))
        .map(instance => instance.scheduleInstanceId);

      this.store.removeInstances(removedInstanceIds);

      this._appInsights.trackEvent('BulkSchedulesDeleted', {
        workspaceId: workspaceId,
        count: viewScheduleIds.length.toString(),
        page: 'calendar'
      });
    } catch (error) {
      if (!isActionCanceled(error)) {
        this._notifications.addError('Failed to delete selected recurring tasks', error);
      }

      throw error; // Always re-throw errors
    } finally {
      this.store.deleteViewedSchedulesEnd(deletedIds);
    }
  }

  /**
   * Deletes the given schedule template. This will fail if the template is currently used by any schedules.
   * @param context.payload The schedule template to delete.
   */
  @Action()
  async deleteTemplate(context: ActionPayloadContext<ScheduleTemplate>): Promise<boolean> {
    const template = context.payload;

    try {
      const dialogData: GdcoConfirmationData = {
        title: 'Delete template',
        text: 'Are you sure you want to delete the selected template?'
      };

      // Require user confirmation
      const confirmed = await awaitable(this._confirmationDialog.confirm(dialogData), context.cancellationToken);
      if (!confirmed) {
        return false;
      }

      const payload: DeleteScheduleTemplatePayload = {
        workspaceId: template.workspaceId,
        templateId: template.scheduleTemplateId
      };

      await this._schedulerManager.deleteScheduleTemplate(new ActionPayloadContext(payload, context));

      // Make sure the schedule template doesn't stay selected
      this.store.deselectAllTemplates();
    } catch (error) {
      if (!isActionCanceled(error)) {
        this._notifications.addError('Failed to delete template', error);
      }

      throw error; // Always re-throw errors
    }

    return true;
  }

  /** Deletes all schedule templates selected by the user. */
  @Action()
  async deleteSelectedTemplates(context: ActionContext): Promise<void> {
    const deletedIds: string[] = [];
    const { workspaceId, selectedTemplateIds } = this.store.state;

    try {
      const dialogData: GdcoConfirmationData = {
        title: 'Delete selected templates',
        text: 'Are you sure you want to delete the selected templates?'
      };

      // Require user confirmation
      const confirmed = await awaitable(this._confirmationDialog.confirm(dialogData), context.cancellationToken);
      if (!confirmed) {
        return;
      }

      this.store.deleteSelectedTemplatesStart();

      const promises: Promise<any>[] = [];

      // Delete schedule templates in parallel
      for (const templateId of selectedTemplateIds) {
        const payload: DeleteScheduleTemplatePayload = {
          workspaceId: workspaceId,
          templateId: templateId
        };

        const promise = this._schedulerManager.deleteScheduleTemplate(new ActionPayloadContext(payload, context));
        promises.push(promise);

        deletedIds.push(templateId);
      }

      await Promise.all(promises);

      // Make sure the schedule templates don't stay selected
      this.store.deselectAllTemplates();
    } catch (error) {
      if (!isActionCanceled(error)) {
        this._notifications.addError('Failed to delete selected templates', error);
      }

      throw error; // Always re-throw errors
    } finally {
      this.store.deleteSelectedTemplatesEnd(deletedIds);
    }
  }

  /**
   * Creates a new schedule.
   * @param context.payload The user entered data for the schedule to create.
   */
  @Action()
  async createNewRecurringTask(context: ActionPayloadContext<RecurringTask>): Promise<boolean> {
    const recurringTask = context.payload;
    const { workspaceId, selectedFaultCode, templateIds } = this.store.state;

    try {
      const result = await this._checkDayOfMonth(recurringTask);
      if (!result) {
        return false;
      }

      this.store.createNewRecurringTaskStart();

      const templates = this._schedulerManager.templates.getEntities(templateIds);
      const template = templates.find(t => this._faultCodeExistsInTemplate(t, selectedFaultCode));

      const faultCode = this._domainDataManager.faultCodesEntityStore.get(selectedFaultCode.toString());

      const schedule: Partial<Schedule> = {
        ...new RecurringTaskConverter().convertBack(recurringTask, null, this.workspace.clientData?.useUtc),
        workspaceId: workspaceId,
        name: recurringTask.name || faultCode.description,
        templateId: template.scheduleTemplateId,
        clientData: {
          faultCode: selectedFaultCode
        }
      };

      // Temporarily hardcode the selected fault code as a parameter
      if (template.parameterDefinitions.find(parameter => parameter.name === 'faultCode')) {
        const predefinedParameters = schedule.templateParameterRetrievalSetting?.predefinedParameters;
        if (predefinedParameters && predefinedParameters[0]) {
          if (!predefinedParameters[0]['faultCode']) {
            predefinedParameters[0]['faultCode'] = selectedFaultCode;
          }
        }
      }

      // Update executionTimeOffset if it was set in the template
      if (template.clientData?.executionTimeOffsetInDays) {
        schedule.recurrencePattern.executionTimeOffset = `${template.clientData.executionTimeOffsetInDays.toString()}.00:00:00`;
      }

      const newSchedule = await this._schedulerManager.createSchedule(new ActionPayloadContext(schedule, context));

      this._appInsights.trackEvent('ScheduleCreated', {
        workspaceId: workspaceId,
        templateId: template.scheduleTemplateId,
        datacenter: this._fieldResolver.getFieldForSchedule(newSchedule, 'DatacenterCode'),
        selectedFaultCode: selectedFaultCode?.toString(),
        recurrenceType: schedule.recurrencePattern.recurrenceType,
        maxNumOfOccurrences:
          schedule.recurrencePattern.maxNumOfOccurrences > 0
            ? schedule.recurrencePattern.maxNumOfOccurrences.toString()
            : undefined,
        daysOfWeek: JSON.stringify(schedule.recurrencePattern.daysOfWeek)
      });

      this.store.addSchedule(newSchedule.scheduleId);
    } catch (error) {
      if (!isActionCanceled(error)) {
        this._notifications.addError('Failed to create recurring task', error);
      }

      throw error; // Always re-throw errors
    } finally {
      this.store.createNewRecurringTaskEnd();
    }

    return true;
  }

  /**
   * Updates an existing schedule.
   * @param context.payload The user entered data to update the schedule from.
   */
  @Action()
  async updateRecurringTask(context: ActionPayloadContext<RecurringTask>): Promise<boolean> {
    const recurringTask = context.payload;
    const { workspaceId, editScheduleId, selectedFaultCode } = this.store.state;

    try {
      const result = await this._checkDayOfMonth(recurringTask);
      if (!result) {
        return false;
      }

      this.store.updateRecurringTaskStart();

      const payload: GetScheduleByIdPayload = {
        workspaceId: workspaceId,
        scheduleId: editScheduleId
      };

      const existingSchedule = await this._schedulerManager.getScheduleById(new ActionPayloadContext(payload, context));

      const template = this._schedulerManager.templates.get(existingSchedule.templateId);

      const schedule = new RecurringTaskConverter().convertBack(
        recurringTask,
        existingSchedule,
        this.workspace.clientData?.useUtc
      );

      schedule.clientData = {
        ...existingSchedule.clientData
      };

      // Temporarily hardcode the selected fault code as a parameter
      if (template.parameterDefinitions.find(parameter => parameter.name === 'faultCode')) {
        const predefinedParameters = schedule.templateParameterRetrievalSetting?.predefinedParameters;
        if (predefinedParameters && predefinedParameters[0]) {
          if (!predefinedParameters[0]['faultCode']) {
            predefinedParameters[0]['faultCode'] = selectedFaultCode;
          }
        }
      }

      // Update executionTimeOffset if it was set in the template
      if (template.clientData?.executionTimeOffsetInDays) {
        schedule.recurrencePattern.executionTimeOffset = `${template.clientData.executionTimeOffsetInDays.toString()}.00:00:00`;
      }

      await this._schedulerManager.updateSchedule(new ActionPayloadContext(schedule, context));

      this._appInsights.trackEvent('ScheduleUpdated', {
        workspaceId: workspaceId,
        templateId: template.scheduleTemplateId,
        datacenter: this._fieldResolver.getFieldForSchedule(schedule, 'DatacenterCode'),
        recurrenceType: schedule.recurrencePattern.recurrenceType
      });

      if (this.store.state.viewScheduleIds.includes(schedule.scheduleId)) {
        await this.loadScheduleInstances(new ActionPayloadContext(this.store.state.viewScheduleIds, context));
      }
    } catch (error) {
      if (!isActionCanceled(error)) {
        this._notifications.addError('Failed to update scheduled task', error);
      }

      throw error; // Always re-throw errors
    } finally {
      this.store.updateRecurringTaskEnd();
    }

    return true;
  }

  /**
   * Creates a new schedule template from the given user input.
   * @returns The id of the newly created template.
   */
  @Action()
  async createScheduleTemplate(context: ActionPayloadContext<TemplateForm>): Promise<string> {
    const form = context.payload;
    const { workspaceId } = this.store.state;

    try {
      this.templateDetailsStore.createScheduleTemplateStart();

      const scheduleTemplate = new TemplateFormConverter().convertBack(form, workspaceId);

      if (scheduleTemplate.parameterDefinitions) {
        const faultCodeExists = scheduleTemplate.parameterDefinitions.find(param => param.name === 'faultCode');
        if (!faultCodeExists) {
          scheduleTemplate.parameterDefinitions.push({
            name: 'faultCode',
            dataType: ScheduleTemplateParameterDataType.Number
          });
        }
      }

      const newTemplate = await this._schedulerManager.createScheduleTemplate(
        new ActionPayloadContext(scheduleTemplate, context)
      );

      this.store.addTemplate(newTemplate.scheduleTemplateId);

      return newTemplate.scheduleTemplateId;
    } catch (error) {
      if (!isActionCanceled(error)) {
        this._notifications.addError('Failed to create template', error);
      }

      throw error; // Always re-throw errors
    } finally {
      this.templateDetailsStore.createScheduleTemplateEnd();
    }
  }

  /** Previews the creation of a task with an unsaved schedule template and given parameters. */
  @Action()
  async previewUnsavedTemplate(context: ActionPayloadContext<PreviewTemplate>): Promise<void> {
    const { template, parameters } = context.payload;

    let task: Partial<Task>;

    try {
      this.templateDetailsStore.previewUnsavedTemplateStart();

      if (template.parameterDefinitions) {
        const faultCodeExists = template.parameterDefinitions.find(param => param.name === 'faultCode');
        if (!faultCodeExists) {
          template.parameterDefinitions.push({
            name: 'faultCode',
            dataType: ScheduleTemplateParameterDataType.Number
          });
        }
      }

      const payload: PreviewUnsavedScheduleTemplatePayload = {
        template: template,
        parameters: parameters
      };

      task = (await this._schedulerManager.previewUnsavedScheduleTemplate(
        new ActionPayloadContext(payload, context)
      )) as Task;
    } catch (error) {
      if (!isActionCanceled(error)) {
        this._notifications.addError('Failed to preview template', error);
      }

      throw error; // Always re-throw errors
    } finally {
      this.templateDetailsStore.previewUnsavedTemplateEnd(task);
    }
  }

  /**
   * Copies a template from a different workspace or environment.
   * @param context.payload The existing template to copy into the current workspace.
   */
  @Action()
  async copyExistingTemplate(context: ActionPayloadContext<ScheduleTemplate>): Promise<void> {
    const template = context.payload;
    const { workspaceId } = this.store.state;

    try {
      this.store.createNewTemplateStart();

      template.workspaceId = workspaceId;

      const newTemplate = await this._schedulerManager.createScheduleTemplate(
        new ActionPayloadContext(template, context)
      );

      this.store.addTemplate(newTemplate.scheduleTemplateId);
    } catch (error) {
      if (!isActionCanceled(error)) {
        this._notifications.addError('Failed to create template', error);
      }

      throw error; // Always re-throw errors
    } finally {
      this.store.createNewTemplateEnd();
    }
  }

  /**
   * Updates an existing template.
   * @param context.payload The user entered data to update the template from.
   */
  @Action()
  async updateScheduleTemplate(context: ActionPayloadContext<TemplateForm>): Promise<void> {
    const template = context.payload;

    try {
      this.templateDetailsStore.createScheduleTemplateStart();

      const scheduleTemplate = new TemplateFormConverter().convertBack(
        template,
        this.store.state.workspaceId,
        this.templateDetailsStore.state.templateId
      );

      if (scheduleTemplate.parameterDefinitions) {
        const faultCodeExists = scheduleTemplate.parameterDefinitions.find(param => param.name === 'faultCode');
        if (!faultCodeExists) {
          scheduleTemplate.parameterDefinitions.push({
            name: 'faultCode',
            dataType: ScheduleTemplateParameterDataType.Number
          });
        }
      }

      await this._schedulerManager.updateScheduleTemplate(new ActionPayloadContext(scheduleTemplate, context));

      this.templateDetailsStore.standardView();
    } catch (error) {
      if (!isActionCanceled(error)) {
        this._notifications.addError('Failed to save template', error);
      }

      throw error; // Always re-throw errors
    } finally {
      this.templateDetailsStore.createScheduleTemplateEnd();
    }
  }

  /**
   * Begins editing the given schedule.
   * @param schedule The schedule to edit.
   */
  editRecurringTask(schedule: Schedule): void {
    const recurringTask = new RecurringTaskConverter().convert(schedule);

    this.store.openEditRecurringTaskDialog(recurringTask, schedule);
  }

  /**
   * Begins duplicating the given schedule.
   * @param schedule The schedule to duplicate.
   */
  duplicateRecurringTask(schedule: Schedule): void {
    const recurringTask = new RecurringTaskConverter().convert(schedule);

    this.store.openDuplicateRecurringTaskDialog(recurringTask, schedule);
  }

  /**
   * Views the details of the given schedule instance and loads all tasks created by the instance.
   * @param context.payload The instance details to view.
   */
  @Action({ type: ActionType.CancelInProgress })
  async viewScheduleInstance(context: ActionPayloadContext<ScheduleInstance>): Promise<void> {
    const scheduleInstance = context.payload;

    try {
      this.store.openScheduleInstanceDialog(scheduleInstance.scheduleInstanceId);

      let taskIds: string[] = [];
      if (
        scheduleInstance.state === ScheduleInstanceState.Succeeded ||
        scheduleInstance.state === ScheduleInstanceState.PartiallySucceeded
      ) {
        const queryBuilder = new GdcoQueryBuilder()
          .startWith('CreatedByScheduleWorkspaceId', GdcoQueryOperator.equals, scheduleInstance.workspaceId)
          .and('CreatedByScheduleId', GdcoQueryOperator.equals, scheduleInstance.scheduleId)
          .and('CreatedByScheduleInstanceId', GdcoQueryOperator.equals, scheduleInstance.scheduleInstanceId);

        const payload: SearchTasksPayload = {
          query: queryBuilder,
          skip: 0
        };

        const tasks = await this._tasksManager.searchTasks(new ActionPayloadContext(payload, context));

        taskIds = tasks.results.map(task => task.Id);
      }

      this.store.setSelectedScheduleInstanceTaskIds(taskIds);
    } catch (error) {
      if (!isActionCanceled(error)) {
        this._notifications.addError('Failed to load scheduled task details', error);
      }

      throw error;
    }
  }

  /**
   * Updates the columns displayed in a workspace.
   * @param context.payload The new set of columns that should be displayed.
   */
  @Action()
  async updateWorkspaceColumnOptions(context: ActionPayloadContext<string[]>): Promise<void> {
    const options = context.payload;

    try {
      const confirmationData: GdcoConfirmationData = {
        title: 'Are you sure?',
        text: 'These changes apply to all users of the current workspace. Are you sure you want to make these changes?'
      };

      // Require user confirmation
      const confirmed = await awaitable(this._confirmationDialog.confirm(confirmationData), context.cancellationToken);
      if (!confirmed) {
        return;
      }

      this.store.updateWorkspaceColumnOptionsStart();

      const updatedWorkspace: ScheduleWorkspace = {
        ...this.workspace,
        clientData: {
          ...this.workspace.clientData,
          columns: options
        }
      };

      await this._schedulerManager.updateWorkspace(new ActionPayloadContext(updatedWorkspace, context));

      this.store.closeColumnOptionsDialog();
    } catch (error) {
      if (!isActionCanceled(error)) {
        this._notifications.addError('Failed to save new column options', error);
      }

      throw error;
    } finally {
      this.store.updateWorkspaceColumnOptionsEnd();
    }
  }

  private _faultCodeExistsInTemplate(template: ScheduleTemplate, faultCode: number): boolean {
    if (!template || !template.clientData) {
      return false;
    }

    return template.clientData.faultCodes?.includes(faultCode);
  }

  private _getCampusesFromSchedules(schedules: Schedule[]): string[] {
    if (!schedules) {
      return [];
    }

    return schedules
      .map(schedule => this._fieldResolver.getFieldForSchedule(schedule, 'FacilityCampus'))
      .map(campus => this._getCampusName(campus))
      .filter((value, index, arr) => arr.indexOf(value) === index)
      .sort();
  }

  private _filterSchedules(
    schedules: Schedule[],
    selectedCampuses: string[],
    userId: string,
    showCompletedSchedules: boolean
  ): Schedule[] {
    if (!schedules) {
      return [];
    }

    let filteredSchedules = schedules;
    if (selectedCampuses && selectedCampuses.length > 0) {
      filteredSchedules = filteredSchedules.filter(schedule => {
        const campus = this._fieldResolver.getFieldForSchedule(schedule, 'FacilityCampus');
        const campusName = this._getCampusName(campus);

        return selectedCampuses.includes(campusName);
      });
    }

    if (userId) {
      filteredSchedules = filteredSchedules.filter(schedule => schedule.createdBy === userId);
    }

    if (!showCompletedSchedules) {
      filteredSchedules = filteredSchedules.filter(schedule => schedule.state === ScheduleState.Active);
    }

    return filteredSchedules;
  }

  private async _checkDayOfMonth(recurringTask: RecurringTask): Promise<boolean> {
    if (recurringTask.frequency.frequencyType === FrequencyType.Monthly) {
      if (recurringTask.frequency.recurrence.day > 28) {
        const confirmationData: GdcoConfirmationData = {
          title: 'Notice',
          text: `Some months have fewer than ${recurringTask.frequency.recurrence.day} days. For these months, the occurrence will fall on the last day of the month.`,
          width: 500,
          acceptButtonText: 'OK',
          rejectButtonText: 'Cancel'
        };

        return await awaitable(this._confirmationDialog.confirm(confirmationData), null);
      }
    }

    return true;
  }

  private _getCampusName(campus?: string | null): string {
    return campus || 'Unknown';
  }

  private _createTemplateVersionKey(templateId: string, version: number): string {
    if (!templateId || !version) {
      return null;
    }

    return `${templateId}:${version}`;
  }
}
