/**
 * @license
 * Copyright (c) Microsoft Corporation. All rights reserved.
 */

import { SchedulerAuthResources } from '../../../../common';
import { Component, ChangeDetectionStrategy, Input, Output, EventEmitter } from '@angular/core';
import { MatCheckboxChange } from '@angular/material/checkbox';
import { MatSelectChange } from '@angular/material/select';
import { AzureGraphObject } from '@gdco/core-reference-systems/gdco-service';
import { Schedule } from '@gdco/reference-systems/gdco-service';

/**
 * User actions for the schedule list.
 */
@Component({
  selector: 'tasks-scheduler-schedule-actions',
  templateUrl: './schedule-actions.component.html',
  styleUrls: ['./schedule-actions.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class WorkspaceScheduleActionsComponent {
  /** The current selected schedules. */
  @Input() selectedSchedules: Schedule[] = [];

  /** Indicates if schedules are currently being deleted. */
  @Input() deleting = false;

  /** Campuses the user can filter using. */
  @Input() campuses: string[] = [];

  /** The current selected campuses. */
  @Input() selectedCampuses: string[] = [];

  /** Users the user can filter using. */
  @Input() users: AzureGraphObject[] = [];

  /** The current selected user id. */
  @Input() selectedUser: string;

  /** Object Id of the current user. */
  @Input() currentUserId: string;

  /** Indicates if there exists any completed schedules in the curent workspace. */
  @Input() hasCompletedSchedules = false;

  /** Indicates if show completed schedules checkbox is checked. */
  @Input() showCompletedSchedules = false;

  /** Event emitted when the user requests to delete the selected schedules. */
  @Output() delete: EventEmitter<void> = new EventEmitter();

  /** Event emitted when the user requests to create a new schedule. */
  @Output() new: EventEmitter<void> = new EventEmitter();

  /** Event emitted when the user selects campuses to filter by. */
  @Output() campusSelected: EventEmitter<string[]> = new EventEmitter();

  /** Event emitted when the user selects a user to filter by. */
  @Output() userSelected: EventEmitter<string> = new EventEmitter();

  /** Event emitted when the user selects to show completed schedules. */
  @Output() showCompletedSchedulesChange: EventEmitter<boolean> = new EventEmitter();

  /** Event emitted when the user requests to edit the table columns. */
  @Output() editColumns: EventEmitter<void> = new EventEmitter();

  readonly allWorkspacesResource = SchedulerAuthResources.allWorkspacesResource;

  /** Event handler for when the user clicks on the "New recurring task" button. */
  onNewRecurringTaskClicked(): void {
    this.new.emit();
  }

  /** Event handler for when the user clicks on the Delete button. */
  onDeleteClicked(): void {
    this.delete.emit();
  }

  /** Event handler for when the user selects campuses to filter by. */
  onCampusSelectionChanged(event: MatSelectChange): void {
    this.campusSelected.emit(event.value);
  }

  /** Event handler for when the user selects a user to filter by. */
  onUserSelectionChanged(event: MatSelectChange): void {
    this.userSelected.emit(event.value);
  }

  /** Gets the user pricipal name of the selected user. */
  selectedUserPrincipalName(): string {
    if (!this.selectedUser || !this.users) {
      return null;
    }

    const selected = this.users.find(user => user.ObjectId === this.selectedUser);
    if (selected) {
      return selected.UserPrincipalName;
    }

    return null;
  }

  /** Event handler for when the user checks the "Show completed schedules" checkbox. */
  onShowCompletedSchedulesChanged(event: MatCheckboxChange): void {
    this.showCompletedSchedulesChange.emit(event.checked);
  }

  /** Event handler for when the user clicks on the "Column options" button. */
  onColumnOptionsButtonClicked(): void {
    this.editColumns.emit();
  }
}
