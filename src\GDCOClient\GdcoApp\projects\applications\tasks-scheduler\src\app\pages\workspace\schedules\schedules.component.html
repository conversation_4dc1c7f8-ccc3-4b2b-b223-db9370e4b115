<gdco-spinner *ngIf="(loadingWorkspace$ | async) || (loadingSchedules$ | async); else showSchedules"></gdco-spinner>
<ng-template #showSchedules>
  <tasks-scheduler-schedule-actions
    [selectedSchedules]="selectedSchedules$ | async"
    [deleting]="deletingSelectedSchedules$ | async"
    [campuses]="scheduleCampuses$ | async"
    [users]="scheduleCreatedByUsers$ | async"
    [selectedCampuses]="selectedCampuses$ | async"
    [selectedUser]="selectedUser$ | async"
    [currentUserId]="currentUserId"
    [hasCompletedSchedules]="hasCompletedSchedules$ | async"
    [showCompletedSchedules]="showCompletedSchedules$ | async"
    (new)="onNewRecurringTask()"
    (delete)="onDeleteSelectedSchedules()"
    (campusSelected)="onCampusSelected($event)"
    (userSelected)="onUserSelected($event)"
    (showCompletedSchedulesChange)="onShowCompletedSchedulesChange($event)"
    (editColumns)="onEditColumnOptions()"
  ></tasks-scheduler-schedule-actions>

  <mat-card fxFlex class="schedule-list">
    <tasks-scheduler-schedule-list
      [workspace]="workspace$ | async"
      [schedules]="filteredSchedules$ | async"
      [selectedSchedules]="selectedSchedules$ | async"
      [schedulesById]="schedulesById$ | async"
      [scheduleFieldMap]="scheduleFieldMap$ | async"
      (selectedSchedulesChange)="onSelectedSchedulesChanged($event)"
      (edit)="onEditRecurringTask($event)"
      (duplicate)="onDuplicateRecurringTask($event)"
      (delete)="onDeleteSchedule($event)"
    >
    </tasks-scheduler-schedule-list>
  </mat-card>
</ng-template>

<!-- New recurring task selection dialog -->
<gdco-dialog
  label="New recurring task"
  [disableClose]="true"
  [opened]="(recurringTaskDialogType$ | async) === RecurringTaskDialogType.SelectTask"
  (openedChange)="onDialogOpenChanged($event, RecurringTaskDialogType.SelectTask)"
>
  <ng-template gdcoDialogContent>
    <tasks-scheduler-select-task
      [faultCodes]="templateFaultCodes$ | async"
      (next)="onTaskSelected($event)"
      (cancel)="onCloseRecurringTask()"
    >
    </tasks-scheduler-select-task>
  </ng-template>
</gdco-dialog>

<!-- New recurring task dialog -->
<gdco-dialog
  label="New recurring task"
  [opened]="(recurringTaskDialogType$ | async) === RecurringTaskDialogType.New"
  (openedChange)="onDialogOpenChanged($event, RecurringTaskDialogType.New)"
  [disableClose]="true"
>
  <ng-template gdcoDialogContent>
    <tasks-scheduler-recurring-task
      [template]="selectedTemplate$ | async"
      [faultCode]="selectedFaultCode$ | async"
      [saving]="creatingSchedule$ | async"
      (save)="onSaveRecurringTask($event)"
      (cancel)="onCloseRecurringTask()"
    ></tasks-scheduler-recurring-task>
  </ng-template>
</gdco-dialog>

<!-- Edit recurring task dialog -->
<gdco-dialog
  label="Edit recurring task"
  [opened]="(recurringTaskDialogType$ | async) === RecurringTaskDialogType.Edit"
  (openedChange)="onDialogOpenChanged($event, RecurringTaskDialogType.Edit)"
  [disableClose]="true"
>
  <ng-template gdcoDialogContent>
    <tasks-scheduler-recurring-task
      [template]="editScheduleTemplate$ | async"
      [faultCode]="selectedFaultCode$ | async"
      [recurringTask]="editOrDuplicateRecurringTask$ | async"
      [saving]="creatingSchedule$ | async"
      (save)="onSaveUpdatedRecurringTask($event)"
      (cancel)="onCloseRecurringTask()"
    ></tasks-scheduler-recurring-task>
  </ng-template>
</gdco-dialog>

<!-- Duplicate recurring task dialog -->
<gdco-dialog
  label="Duplicate recurring task"
  [opened]="(recurringTaskDialogType$ | async) === RecurringTaskDialogType.Duplicate"
  (openedChange)="onDialogOpenChanged($event, RecurringTaskDialogType.Duplicate)"
  [disableClose]="true"
>
  <ng-template gdcoDialogContent>
    <tasks-scheduler-recurring-task
      [template]="duplicateScheduleTemplate$ | async"
      [faultCode]="selectedFaultCode$ | async"
      [recurringTask]="editOrDuplicateRecurringTask$ | async"
      [saving]="creatingSchedule$ | async"
      (save)="onSaveRecurringTask($event)"
      (cancel)="onCloseRecurringTask()"
    ></tasks-scheduler-recurring-task>
  </ng-template>
</gdco-dialog>

<!-- Column options dialog -->
<gdco-dialog
  #columnOptionsDialog
  label="Column options"
  [opened]="columnOptionsDialogOpen$ | async"
  [disableClose]="true"
  (openedChange)="onColumnOptionsDialogOpenChanged($event)"
>
  <ng-template gdcoDialogContent>
    <tasks-scheduler-column-options
      [workspace]="workspace$ | async"
      [saving]="savingColumnOptions$ | async"
      (cancel)="columnOptionsDialog.close()"
      (save)="onSaveColumnOptions($event)"
    >
    </tasks-scheduler-column-options>
  </ng-template>
</gdco-dialog>
