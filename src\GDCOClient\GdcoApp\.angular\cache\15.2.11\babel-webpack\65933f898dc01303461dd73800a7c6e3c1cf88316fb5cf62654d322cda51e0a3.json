{"ast": null, "code": "/**\r\n * @license\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n */\nexport var RecurringTaskDialogType;\n(function (RecurringTaskDialogType) {\n  RecurringTaskDialogType[RecurringTaskDialogType[\"Closed\"] = 0] = \"Closed\";\n  RecurringTaskDialogType[RecurringTaskDialogType[\"SelectTask\"] = 1] = \"SelectTask\";\n  RecurringTaskDialogType[RecurringTaskDialogType[\"New\"] = 2] = \"New\";\n  RecurringTaskDialogType[RecurringTaskDialogType[\"Edit\"] = 3] = \"Edit\";\n  RecurringTaskDialogType[RecurringTaskDialogType[\"Duplicate\"] = 4] = \"Duplicate\";\n})(RecurringTaskDialogType || (RecurringTaskDialogType = {}));\n/**\r\n * Defines the state of the workspace page.\r\n */\nexport class WorkspacePageState {}", "map": {"version": 3, "names": ["RecurringTaskDialogType", "WorkspacePageState"], "sources": ["D:\\Repo\\GDCO\\MCIO-GDCO-AppService\\src\\GDCOClient\\GdcoApp\\projects\\applications\\tasks-scheduler\\src\\app\\pages\\workspace\\workspace-page.state.ts"], "sourcesContent": ["/**\r\n * @license\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n */\r\n\r\nimport { RecurringTask } from '../../common';\r\n\r\nexport enum RecurringTaskDialogType {\r\n  Closed,\r\n  SelectTask,\r\n  New,\r\n  Edit,\r\n  Duplicate\r\n}\r\n\r\n/**\r\n * Defines the state of the workspace page.\r\n */\r\nexport class WorkspacePageState {\r\n  /** Indicates if the workspace is currently being loaded. */\r\n  loadingWorkspace: boolean;\r\n\r\n  /** Indicates if the workspace schedules are currently being loaded. */\r\n  loadingSchedules: boolean;\r\n\r\n  /** Indicates if the workspace templates are currently being loaded. */\r\n  loadingTemplates: boolean;\r\n\r\n  /** Indicates if a schedule is currently being created. */\r\n  creatingSchedule: boolean;\r\n\r\n  /** Indicates if a schedule template is currently being created. */\r\n  creatingTemplate: boolean;\r\n\r\n  /** Indicates if the selected schedules are currently being deleted. */\r\n  deletingSelectedSchedules: boolean;\r\n\r\n  /** Indicates if the selected templates are currently being deleted. */\r\n  deletingSelectedTemplates: boolean;\r\n\r\n  /** The id of the current workspace. */\r\n  workspaceId: string;\r\n\r\n  /** The ids of all schedules in the current workspace. */\r\n  scheduleIds: string[];\r\n\r\n  /** The ids of all schedule templates in the current workspace. */\r\n  templateIds: string[];\r\n\r\n  /** The ids of the schedules that are currently selected. */\r\n  selectedScheduleIds: string[];\r\n\r\n  /** The ids of the templates that are currently selected. */\r\n  selectedTemplateIds: string[];\r\n\r\n  /** The recurring task dialog that is current open. */\r\n  recurringTaskDialogType: RecurringTaskDialogType;\r\n\r\n  /** The UI model of the recurring task (schedule) that's currently being edited or duplicated. */\r\n  editOrDuplicateRecurringTask: RecurringTask;\r\n\r\n  /** The id of the schedule currently being edited. */\r\n  editScheduleId: string;\r\n\r\n  /** The id of the schedule being duplicated. */\r\n  duplicateScheduleId: string;\r\n\r\n  /** Fault code selected by the user to create a recurring task for. */\r\n  selectedFaultCode: number;\r\n\r\n  /** Campuses the schedules are currently filtered by. */\r\n  selectedCampuses: string[];\r\n\r\n  /** User the schedules are currently filtered by. */\r\n  selectedUserId: string;\r\n\r\n  /** Indicates if completed schedules are shown to the user. */\r\n  showCompletedSchedules: boolean;\r\n\r\n  /** Schedules selected on the calendar page to view the instances for. */\r\n  viewScheduleIds: string[];\r\n\r\n  /** Current date viewed in the calendar. */\r\n  viewDate: Date;\r\n\r\n  /** All schedule instance Ids currently displayed on the calendar page. */\r\n  scheduleInstanceIds: string[];\r\n\r\n  /** Id of the schedule instance currently viewed by the user. */\r\n  selectedScheduleInstanceId: string;\r\n\r\n  /** Ids of the tasks that were created by the currently viewed schedule instance. */\r\n  selectedScheduleInstanceTaskIds: string[];\r\n\r\n  /** Indicates if the dialog for viewing a schedule instance is currently open. */\r\n  scheduleInstanceDialogOpen: boolean;\r\n\r\n  /** Indicates if the tasks for the currently viewed schedule instance are being loaded. */\r\n  loadingScheduleInstanceTasks: boolean;\r\n\r\n  /** Indicates if the dialog to edit the column options on the scheudle tasks tab is open. */\r\n  columnOptionsDialogOpen: boolean;\r\n\r\n  /** Indicates if the workspace column options are currently being saved. */\r\n  savingColumnOptions: boolean;\r\n}\r\n"], "mappings": "AAAA;;;;AAOA,WAAYA,uBAMX;AAND,WAAYA,uBAAuB;EACjCA,uBAAA,CAAAA,uBAAA,0BAAM;EACNA,uBAAA,CAAAA,uBAAA,kCAAU;EACVA,uBAAA,CAAAA,uBAAA,oBAAG;EACHA,uBAAA,CAAAA,uBAAA,sBAAI;EACJA,uBAAA,CAAAA,uBAAA,gCAAS;AACX,CAAC,EANWA,uBAAuB,KAAvBA,uBAAuB;AAQnC;;;AAGA,OAAM,MAAOC,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}