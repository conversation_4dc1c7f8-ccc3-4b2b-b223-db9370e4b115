/**
 * @license
 * Copyright (c) Microsoft Corporation. All rights reserved.
 */

import { TestBed } from '@angular/core/testing';

import { Schedule, ScheduleTemplate } from '@gdco/reference-systems/gdco-service';
import { WorkspacePageStore } from './workspace-page.store';
import { RecurringTaskDialogType } from './workspace-page.state';
import { RecurringTask } from '../../common';

describe('WorkspacePageStore', () => {
  let store: WorkspacePageStore;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [WorkspacePageStore]
    });

    store = TestBed.inject(WorkspacePageStore);
  });

  it('should have a default state', () => {
    expect(store.state).toEqual(WorkspacePageStore.DEFAULT_STATE);
  });

  it('should have all observable properties initialized', () => {
    for (const key of Object.keys(WorkspacePageStore.DEFAULT_STATE)) {
      expect(store[`${key}$`]).toBeDefined();
    }
  });

  it('should initialize the workspace page', () => {
    const workspaceId = 'test';

    store.initializePageStart(workspaceId);

    expect(store.state.workspaceId).toBe(workspaceId);
    expect(store.state.scheduleIds).toEqual([]);
    expect(store.state.templateIds).toEqual([]);
  });

  it('should reset the schedule and template ids when the workspace Id changes', () => {
    const workspaceId = 'test';
    const workspaceId2 = 'other';

    store.initializePageStart(workspaceId);

    store.loadSchedulesEnd(['1', '2', '3', '4']);
    store.loadTemplatesEnd(['1', '2', '3', '4']);

    store.initializePageStart(workspaceId2);

    expect(store.state.scheduleIds).toEqual([]);
    expect(store.state.templateIds).toEqual([]);
  });

  it('should start/stop loading the workspace', () => {
    store.loadWorkspaceStart();
    expect(store.state.loadingWorkspace).toBeTrue();

    store.loadWorkspaceEnd();
    expect(store.state.loadingWorkspace).toBeFalse();
  });

  it('should start/stop loading schedules', () => {
    const scheduleIds = ['1', '2', '3', '4'];
    const addScheduleId = '5';

    store.loadSchedulesStart();
    expect(store.state.scheduleIds).toEqual([]);
    expect(store.state.loadingSchedules).toBeTrue();

    store.loadSchedulesEnd(scheduleIds);
    expect(store.state.scheduleIds).toEqual(scheduleIds);
    expect(store.state.loadingSchedules).toBeFalse();

    store.addSchedule(addScheduleId);
    expect(store.state.scheduleIds).toEqual(scheduleIds.concat(addScheduleId));
  });

  it('should start/stop loading templates', () => {
    const templateIds = ['1', '2', '3', '4'];
    const addTemplateId = '5';

    store.loadTemplatesStart();
    expect(store.state.templateIds).toEqual([]);
    expect(store.state.loadingTemplates).toBeTrue();

    store.loadTemplatesEnd(templateIds);
    expect(store.state.templateIds).toEqual(templateIds);
    expect(store.state.loadingTemplates).toBeFalse();

    store.addTemplate(addTemplateId);
    expect(store.state.templateIds).toEqual(templateIds.concat(addTemplateId));
  });

  it('should set the filters', () => {
    const campuses = ['test campus', 'another campus'];
    const userId = '1';
    const showCompletedSchedules = true;

    expect(store.state.selectedCampuses).toEqual([]);
    expect(store.state.selectedUserId).toBeNull();
    expect(store.state.showCompletedSchedules).toBeFalse();

    store.selectCampuses(campuses);
    store.selectUser(userId);
    store.showCompletedSchedules(showCompletedSchedules);

    expect(store.state.selectedCampuses).toEqual(campuses);
    expect(store.state.selectedUserId).toBe(userId);
    expect(store.state.showCompletedSchedules).toBe(showCompletedSchedules);
  });

  it('should select the schedules', () => {
    const schedules = [
      { scheduleId: '1' },
      { scheduleId: '2' },
      { scheduleId: '3' },
      { scheduleId: '4' }
    ] as Schedule[];

    store.selectSchedules(schedules);
    expect(store.state.selectedScheduleIds).toEqual(['1', '2', '3', '4']);

    store.deselectAllSchedules();
    expect(store.state.selectedScheduleIds).toEqual([]);
  });

  it('should select the templates', () => {
    const templates = [
      { scheduleTemplateId: '1' },
      { scheduleTemplateId: '2' },
      { scheduleTemplateId: '3' },
      { scheduleTemplateId: '4' }
    ] as ScheduleTemplate[];

    store.selectTemplates(templates);
    expect(store.state.selectedTemplateIds).toEqual(['1', '2', '3', '4']);

    store.deselectAllTemplates();
    expect(store.state.selectedTemplateIds).toEqual([]);
  });

  it('should start/stop deleting selected schedules', () => {
    const scheduleIds = ['1', '2', '3', '4'];
    store.loadSchedulesEnd(scheduleIds); // seed
    const deletedScheduleIds = ['1', '4'];

    store.deleteSelectedSchedulesStart();
    expect(store.state.deletingSelectedSchedules).toBeTrue();

    store.deleteSelectedSchedulesEnd(deletedScheduleIds);
    expect(store.state.scheduleIds).toEqual(['2', '3']);
    expect(store.state.deletingSelectedSchedules).toBeFalse();
  });

  it('should start/stop deleting selected viewed schedules (calendar page)', () => {
    const scheduleIds = ['1', '2', '3', '4'];
    store.loadScheduleInstancesStart(scheduleIds); // seed
    const deletedScheduleIds = ['1', '4'];

    store.deleteViewedSchedulesStart();
    expect(store.state.deletingSelectedSchedules).toBeTrue();

    store.deleteViewedSchedulesEnd(deletedScheduleIds);
    expect(store.state.viewScheduleIds).toEqual(['2', '3']);
    expect(store.state.deletingSelectedSchedules).toBeFalse();
  });

  it('should start/stop deleting selected templates', () => {
    const templateIds = ['1', '2', '3', '4'];
    store.loadTemplatesEnd(templateIds); // seed
    const deletedTemplateIds = ['1', '4'];

    store.deleteSelectedTemplatesStart();
    expect(store.state.deletingSelectedTemplates).toBeTrue();

    store.deleteSelectedTemplatesEnd(deletedTemplateIds);
    expect(store.state.templateIds).toEqual(['2', '3']);
    expect(store.state.deletingSelectedTemplates).toBeFalse();
  });

  it('should open/close recurring task dialogs', () => {
    store.openNewRecurringTaskSelectionDialog();
    expect(store.state.recurringTaskDialogType).toBe(RecurringTaskDialogType.SelectTask);
    expect(store.state.editOrDuplicateRecurringTask).toBeNull();
    expect(store.state.editScheduleId).toBeNull();
    expect(store.state.selectedFaultCode).toBeNull();

    store.openNewRecurringTaskDialog(1234);
    expect(store.state.recurringTaskDialogType).toBe(RecurringTaskDialogType.New);
    expect(store.state.editOrDuplicateRecurringTask).toBeNull();
    expect(store.state.editScheduleId).toBeNull();
    expect(store.state.selectedFaultCode).toBe(1234);

    store.openEditRecurringTaskDialog(
      {} as RecurringTask,
      { scheduleId: '1234', clientData: { faultCode: 1 } } as Schedule
    );
    expect(store.state.recurringTaskDialogType).toBe(RecurringTaskDialogType.Edit);
    expect(store.state.editOrDuplicateRecurringTask).toEqual({} as RecurringTask);
    expect(store.state.editScheduleId).toBe('1234');
    expect(store.state.selectedFaultCode).toBe(1);

    store.openDuplicateRecurringTaskDialog(
      {} as RecurringTask,
      { scheduleId: '1234', clientData: { faultCode: 1 } } as Schedule
    );
    expect(store.state.recurringTaskDialogType).toBe(RecurringTaskDialogType.Duplicate);
    expect(store.state.editOrDuplicateRecurringTask).toEqual({} as RecurringTask);
    expect(store.state.editScheduleId).toBeNull();
    expect(store.state.selectedFaultCode).toBe(1);

    store.closeRecurringTaskDialog();
    expect(store.state.recurringTaskDialogType).toBe(RecurringTaskDialogType.Closed);
    expect(store.state.editOrDuplicateRecurringTask).toBeNull();
    expect(store.state.editScheduleId).toBeNull();
    expect(store.state.selectedFaultCode).toBeNull();
  });

  it('should start/stop creating a recurring task (schedule)', () => {
    store.createNewRecurringTaskStart();
    expect(store.state.creatingSchedule).toBeTrue();

    store.createNewRecurringTaskEnd();
    expect(store.state.creatingSchedule).toBeFalse();
  });

  it('should start/stop updating a recurring task (schedule)', () => {
    store.updateRecurringTaskStart();
    expect(store.state.creatingSchedule).toBeTrue();

    store.updateRecurringTaskEnd();
    expect(store.state.creatingSchedule).toBeFalse();
  });

  it('should start/stop creating a schedule template', () => {
    store.createNewTemplateStart();
    expect(store.state.creatingTemplate).toBeTrue();

    store.createNewTemplateEnd();
    expect(store.state.creatingTemplate).toBeFalse();
  });

  it('should start/stop updating a schedule template', () => {
    store.updateTemplateStart();
    expect(store.state.creatingTemplate).toBeTrue();

    store.updateTemplateEnd();
    expect(store.state.creatingTemplate).toBeFalse();
  });

  it('should start/stop loading the schedule instances', () => {
    store.loadScheduleInstancesStart(['1', '2']);
    expect(store.state.viewScheduleIds).toEqual(['1', '2']);

    store.loadScheduleInstancesEnd(['3', '4']);
    expect(store.state.scheduleInstanceIds).toEqual(['3', '4']);
  });

  it('should open/close the schedule instance dialog', () => {
    const scheduleInstanceId = '1234';
    const taskIds = ['1', '2'];

    store.openScheduleInstanceDialog(scheduleInstanceId);
    expect(store.state.scheduleInstanceDialogOpen).toBeTrue();
    expect(store.state.selectedScheduleInstanceId).toBe(scheduleInstanceId);
    expect(store.state.selectedScheduleInstanceTaskIds).toBeNull();
    expect(store.state.loadingScheduleInstanceTasks).toBeTrue();

    store.setSelectedScheduleInstanceTaskIds(taskIds);
    expect(store.state.selectedScheduleInstanceTaskIds).toEqual(taskIds);
    expect(store.state.loadingScheduleInstanceTasks).toBeFalse();

    store.closeScheduleInstanceDialog();
    expect(store.state.scheduleInstanceDialogOpen).toBeFalse();
    expect(store.state.selectedScheduleInstanceId).toBeNull();
    expect(store.state.selectedScheduleInstanceTaskIds).toBeNull();
  });
});
