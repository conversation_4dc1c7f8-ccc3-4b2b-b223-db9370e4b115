/**
 * @license
 * Copyright (c) Microsoft Corporation. All rights reserved.
 */

import { RecurringTask } from '../../common';

export enum RecurringTaskDialogType {
  Closed,
  SelectTask,
  New,
  Edit,
  Duplicate
}

/**
 * Defines the state of the workspace page.
 */
export class WorkspacePageState {
  /** Indicates if the workspace is currently being loaded. */
  loadingWorkspace: boolean;

  /** Indicates if the workspace schedules are currently being loaded. */
  loadingSchedules: boolean;

  /** Indicates if the workspace templates are currently being loaded. */
  loadingTemplates: boolean;

  /** Indicates if a schedule is currently being created. */
  creatingSchedule: boolean;

  /** Indicates if a schedule template is currently being created. */
  creatingTemplate: boolean;

  /** Indicates if the selected schedules are currently being deleted. */
  deletingSelectedSchedules: boolean;

  /** Indicates if the selected templates are currently being deleted. */
  deletingSelectedTemplates: boolean;

  /** The id of the current workspace. */
  workspaceId: string;

  /** The ids of all schedules in the current workspace. */
  scheduleIds: string[];

  /** The ids of all schedule templates in the current workspace. */
  templateIds: string[];

  /** The ids of the schedules that are currently selected. */
  selectedScheduleIds: string[];

  /** The ids of the templates that are currently selected. */
  selectedTemplateIds: string[];

  /** The recurring task dialog that is current open. */
  recurringTaskDialogType: RecurringTaskDialogType;

  /** The UI model of the recurring task (schedule) that's currently being edited or duplicated. */
  editOrDuplicateRecurringTask: RecurringTask;

  /** The id of the schedule currently being edited. */
  editScheduleId: string;

  /** The id of the schedule being duplicated. */
  duplicateScheduleId: string;

  /** Fault code selected by the user to create a recurring task for. */
  selectedFaultCode: number;

  /** Campuses the schedules are currently filtered by. */
  selectedCampuses: string[];

  /** User the schedules are currently filtered by. */
  selectedUserId: string;

  /** Indicates if completed schedules are shown to the user. */
  showCompletedSchedules: boolean;

  /** Schedules selected on the calendar page to view the instances for. */
  viewScheduleIds: string[];

  /** Current date viewed in the calendar. */
  viewDate: Date;

  /** All schedule instance Ids currently displayed on the calendar page. */
  scheduleInstanceIds: string[];

  /** Id of the schedule instance currently viewed by the user. */
  selectedScheduleInstanceId: string;

  /** Ids of the tasks that were created by the currently viewed schedule instance. */
  selectedScheduleInstanceTaskIds: string[];

  /** Indicates if the dialog for viewing a schedule instance is currently open. */
  scheduleInstanceDialogOpen: boolean;

  /** Indicates if the tasks for the currently viewed schedule instance are being loaded. */
  loadingScheduleInstanceTasks: boolean;

  /** Indicates if the dialog to edit the column options on the scheudle tasks tab is open. */
  columnOptionsDialogOpen: boolean;

  /** Indicates if the workspace column options are currently being saved. */
  savingColumnOptions: boolean;
}
