/**
 * @license
 * Copyright (c) Microsoft Corporation. All rights reserved.
 */

import { WorkspaceDatePipe, RecurringTask } from '../../../common';
import { dateAdapterOptions } from '../date-adapter-options';
import { RecurringTaskDialogType } from '../workspace-page.state';
import { WorkspacePageViewModel } from '../workspace-page.view-model';
import { Component, OnDestroy } from '@angular/core';
import {
  MomentDateAdapter,
  MAT_MOMENT_DATE_FORMATS,
  MAT_MOMENT_DATE_ADAPTER_OPTIONS
} from '@angular/material-moment-adapter';
import { DateAdapter, MAT_DATE_LOCALE, MAT_DATE_FORMATS } from '@angular/material/core';
import { GdcoAuth } from '@gdco/auth';
import { FaultCode } from '@gdco/core-reference-systems/gdco-service';
import { Schedule } from '@gdco/reference-systems/gdco-service';
import { ActionContext, ActionPayloadContext } from '@gdco/store';

@Component({
  selector: 'tasks-scheduler-schedules',
  templateUrl: './schedules.component.html',
  styleUrls: ['./schedules.component.css'],
  providers: [
    { provide: DateAdapter, useClass: MomentDateAdapter, deps: [MAT_DATE_LOCALE, MAT_MOMENT_DATE_ADAPTER_OPTIONS] },
    { provide: MAT_DATE_FORMATS, useValue: MAT_MOMENT_DATE_FORMATS },
    {
      provide: MAT_MOMENT_DATE_ADAPTER_OPTIONS,
      useFactory: dateAdapterOptions,
      deps: [WorkspacePageViewModel]
    },
    WorkspaceDatePipe
  ]
})
export class WorkspaceSchedulesComponent implements OnDestroy {
  readonly workspace$ = this._vm.workspace$;
  readonly schedules$ = this._vm.schedules$;
  readonly filteredSchedules$ = this._vm.filteredSchedules$;
  readonly selectedSchedules$ = this._vm.selectedSchedules$;
  readonly hasCompletedSchedules$ = this._vm.hasCompletedSchedules$;
  readonly scheduleCampuses$ = this._vm.scheduleCampuses$;
  readonly scheduleCreatedByUsers$ = this._vm.scheduleCreatedByUsers$;
  readonly templateFaultCodes$ = this._vm.templateFaultCodes$;
  readonly selectedTemplate$ = this._vm.selectedTemplate$;
  readonly selectedFaultCode$ = this._vm.selectedFaultCode$;
  readonly schedulesById$ = this._vm.schedulesById$;
  readonly editScheduleTemplate$ = this._vm.editScheduleTemplate$;
  readonly duplicateScheduleTemplate$ = this._vm.duplicateScheduleTemplate$;
  readonly scheduleFieldMap$ = this._vm.scheduleFieldMap$;

  readonly loadingWorkspace$ = this._vm.store.loadingWorkspace$;
  readonly loadingSchedules$ = this._vm.store.loadingSchedules$;
  readonly selectedCampuses$ = this._vm.store.selectedCampuses$;
  readonly selectedUser$ = this._vm.store.selectedUserId$;
  readonly showCompletedSchedules$ = this._vm.store.showCompletedSchedules$;
  readonly recurringTaskDialogType$ = this._vm.store.recurringTaskDialogType$;
  readonly creatingSchedule$ = this._vm.store.creatingSchedule$;
  readonly deletingSelectedSchedules$ = this._vm.store.deletingSelectedSchedules$;
  readonly editOrDuplicateRecurringTask$ = this._vm.store.editOrDuplicateRecurringTask$;
  readonly columnOptionsDialogOpen$ = this._vm.store.columnOptionsDialogOpen$;
  readonly savingColumnOptions$ = this._vm.store.savingColumnOptions$;

  readonly RecurringTaskDialogType = RecurringTaskDialogType;
  readonly currentUserId: string;

  constructor(private _vm: WorkspacePageViewModel, private _auth: GdcoAuth) {
    this.currentUserId = this._auth.currentUser.userId;
  }

  ngOnDestroy(): void {
    this._vm.store.closeRecurringTaskDialog();
    this._vm.store.deselectAllSchedules();
  }

  onSelectedSchedulesChanged(schedules: Schedule[]): void {
    this._vm.store.selectSchedules(schedules);
  }

  onDeleteSchedule(schedule: Schedule): void {
    this._vm.deleteSchedule(new ActionPayloadContext(schedule));
  }

  onCampusSelected(campuses: string[]): void {
    this._vm.store.selectCampuses(campuses);
  }

  onUserSelected(userId: string): void {
    this._vm.store.selectUser(userId);
  }

  onShowCompletedSchedulesChange(show: boolean): void {
    this._vm.store.showCompletedSchedules(show);
  }

  onNewRecurringTask(): void {
    this._vm.store.openNewRecurringTaskSelectionDialog();
  }

  onTaskSelected(event: FaultCode): void {
    this._vm.store.openNewRecurringTaskDialog(event.code);
  }

  onEditRecurringTask(schedule: Schedule): void {
    this._vm.editRecurringTask(schedule);
  }

  onDuplicateRecurringTask(schedule: Schedule): void {
    this._vm.duplicateRecurringTask(schedule);
  }

  onCloseRecurringTask(): void {
    this._vm.store.closeRecurringTaskDialog();
  }

  onDialogOpenChanged(open: boolean, dialogType: RecurringTaskDialogType): void {
    if (!open && this._vm.store.state.recurringTaskDialogType === dialogType) {
      this._vm.store.closeRecurringTaskDialog();
    }
  }

  onEditColumnOptions(): void {
    this._vm.store.openColumnOptionsDialog();
  }

  onColumnOptionsDialogOpenChanged(opened: boolean): void {
    if (!opened) {
      this._vm.store.closeColumnOptionsDialog();
    }
  }

  onSaveColumnOptions(event: string[]): void {
    this._vm.updateWorkspaceColumnOptions(new ActionPayloadContext(event));
  }

  async onSaveRecurringTask(recurringTask: RecurringTask): Promise<void> {
    const created = await this._vm.createNewRecurringTask(new ActionPayloadContext(recurringTask));
    if (created) {
      this._vm.store.closeRecurringTaskDialog();
    }
  }

  async onSaveUpdatedRecurringTask(recurringTask: RecurringTask): Promise<void> {
    const created = await this._vm.updateRecurringTask(new ActionPayloadContext(recurringTask));
    if (created) {
      this._vm.store.closeRecurringTaskDialog();
    }
  }

  onDeleteSelectedSchedules(): void {
    this._vm.deleteSelectedSchedules(new ActionContext());
  }
}
