/**
 * @license
 * Copyright (c) Microsoft Corporation. All rights reserved.
 */

import { WorkspaceDatePipe, RecurringTask } from '../../../common';
import { dateAdapterOptions } from '../date-adapter-options';
import { RecurringTaskDialogType } from '../workspace-page.state';
import { WorkspacePageViewModel } from '../workspace-page.view-model';
import { Component, OnDestroy, OnInit } from '@angular/core';
import {
  MomentDateAdapter,
  MAT_MOMENT_DATE_FORMATS,
  MAT_MOMENT_DATE_ADAPTER_OPTIONS
} from '@angular/material-moment-adapter';
import { DateAdapter, MAT_DATE_LOCALE, MAT_DATE_FORMATS } from '@angular/material/core';
import { ActivatedRoute } from '@angular/router';
import { FaultCode } from '@gdco/core-reference-systems/gdco-service';
import { Schedule, ScheduleInstance } from '@gdco/reference-systems/gdco-service';
import { ActionPayloadContext, ActionContext } from '@gdco/store';
import { Subscription } from 'rxjs';

@Component({
  selector: 'tasks-scheduler-schedule-instances',
  templateUrl: './schedule-instances.component.html',
  styleUrls: ['./schedule-instances.component.css'],
  providers: [
    { provide: DateAdapter, useClass: MomentDateAdapter, deps: [MAT_DATE_LOCALE, MAT_MOMENT_DATE_ADAPTER_OPTIONS] },
    { provide: MAT_DATE_FORMATS, useValue: MAT_MOMENT_DATE_FORMATS },
    {
      provide: MAT_MOMENT_DATE_ADAPTER_OPTIONS,
      useFactory: dateAdapterOptions,
      deps: [WorkspacePageViewModel]
    },
    WorkspaceDatePipe
  ]
})
export class ScheduleInstancesComponent implements OnInit, OnDestroy {
  private _routeParamsSub = Subscription.EMPTY;

  readonly workspace$ = this._vm.workspace$;
  readonly mySchedules$ = this._vm.mySchedules$;
  readonly viewSchedules$ = this._vm.viewSchedules$;
  readonly scheduleInstances$ = this._vm.scheduleInstances$;
  readonly scheduleCampuses$ = this._vm.scheduleCampuses$;
  readonly filteredScheduleCampuses$ = this._vm.filteredScheduleCampuses$;
  readonly scheduleCreatedByUsers$ = this._vm.scheduleCreatedByUsers$;
  readonly hasCompletedSchedules$ = this._vm.hasCompletedSchedules$;
  readonly selectedScheduleInstance$ = this._vm.selectedScheduleInstance$;
  readonly selectedScheduleInstanceSchedule$ = this._vm.selectedScheduleInstanceSchedule$;
  readonly selectedScheduleInstanceTasks$ = this._vm.selectedScheduleInstanceTasks$;
  readonly schedulesByCampus$ = this._vm.schedulesByCampus$;
  readonly schedulesById$ = this._vm.schedulesById$;
  readonly selectedTemplate$ = this._vm.selectedTemplate$;
  readonly selectedFaultCode$ = this._vm.selectedFaultCode$;
  readonly templateFaultCodes$ = this._vm.templateFaultCodes$;
  readonly editScheduleTemplate$ = this._vm.editScheduleTemplate$;
  readonly duplicateScheduleTemplate$ = this._vm.duplicateScheduleTemplate$;

  readonly loadingSchedules$ = this._vm.store.loadingSchedules$;
  readonly selectedCampuses$ = this._vm.store.selectedCampuses$;
  readonly selectedUser$ = this._vm.store.selectedUserId$;
  readonly showCompletedSchedules$ = this._vm.store.showCompletedSchedules$;
  readonly scheduleInstanceDialogOpen$ = this._vm.store.scheduleInstanceDialogOpen$;
  readonly loadingScheduleInstanceTasks$ = this._vm.store.loadingScheduleInstanceTasks$;
  readonly deletingSelectedSchedules$ = this._vm.store.deletingSelectedSchedules$;
  readonly viewDate$ = this._vm.store.viewDate$;
  readonly recurringTaskDialogType$ = this._vm.store.recurringTaskDialogType$;
  readonly editOrDuplicateRecurringTask$ = this._vm.store.editOrDuplicateRecurringTask$;
  readonly creatingSchedule$ = this._vm.store.creatingSchedule$;

  readonly RecurringTaskDialogType = RecurringTaskDialogType;

  constructor(private _route: ActivatedRoute, private _vm: WorkspacePageViewModel) {}

  ngOnInit(): void {
    this._routeParamsSub = this._route.params.subscribe(async params => {
      if (params['id']) {
        await this._vm.initializeScheduleInstancesPageByScheduleId(new ActionPayloadContext(params['id']));
      }
    });
  }

  ngOnDestroy(): void {
    this._routeParamsSub.unsubscribe();
    this._vm.store.closeRecurringTaskDialog();
  }

  onDeleteViewedSchedules(): void {
    this._vm.deleteViewedSchedules(new ActionContext());
  }

  onCampusSelected(campuses: string[]): void {
    this._vm.store.selectCampuses(campuses);
  }

  onUserSelected(userId: string): void {
    this._vm.store.selectUser(userId);
  }

  onShowCompletedSchedulesChange(show: boolean): void {
    this._vm.store.showCompletedSchedules(show);
  }

  onInstanceSelected(instance: ScheduleInstance): void {
    this._vm.viewScheduleInstance(new ActionPayloadContext(instance));
  }

  onDialogOpenedChanged(opened: boolean): void {
    if (!opened) {
      this._vm.store.closeScheduleInstanceDialog();
    }
  }

  onNewRecurringTask(): void {
    this._vm.store.openNewRecurringTaskSelectionDialog();
  }

  onTaskSelected(event: FaultCode): void {
    this._vm.store.openNewRecurringTaskDialog(event.code);
  }

  onRecurringTaskDialogOpenChanged(open: boolean, dialogType: RecurringTaskDialogType): void {
    if (!open && this._vm.store.state.recurringTaskDialogType === dialogType) {
      this._vm.store.closeRecurringTaskDialog();
    }
  }

  onCloseRecurringTask(): void {
    this._vm.store.closeRecurringTaskDialog();
  }

  onEditSchedule(schedule: Schedule): void {
    this._vm.editRecurringTask(schedule);
  }

  onDuplicateSchedule(schedule: Schedule): void {
    this._vm.duplicateRecurringTask(schedule);
  }

  onDeleteSchedule(schedule: Schedule): void {
    this._vm.deleteSchedule(new ActionPayloadContext(schedule));
  }

  async onSaveRecurringTask(recurringTask: RecurringTask): Promise<void> {
    await this._vm.createNewRecurringTask(new ActionPayloadContext(recurringTask));

    this._vm.store.closeRecurringTaskDialog();
  }

  async onSaveUpdatedRecurringTask(recurringTask: RecurringTask): Promise<void> {
    await this._vm.updateRecurringTask(new ActionPayloadContext(recurringTask));

    this._vm.store.closeRecurringTaskDialog();
  }

  onSelectedSchedulesChanged(schedules: Schedule[]): void {
    this._vm.loadScheduleInstances(new ActionPayloadContext(schedules.map(schedule => schedule.scheduleId)));
  }

  onLoadMoreInstances(startingFrom: Date): void {
    this._vm.loadScheduleInstancesPage(new ActionPayloadContext(startingFrom));
  }
}
