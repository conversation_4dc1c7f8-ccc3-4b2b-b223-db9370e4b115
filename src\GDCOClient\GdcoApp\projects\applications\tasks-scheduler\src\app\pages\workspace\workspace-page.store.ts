/**
 * @license
 * Copyright (c) Microsoft Corporation. All rights reserved.
 */

import { Injectable } from '@angular/core';
import { gdcoUtils } from '@gdco/core';
import { SimpleStore, ModelFactory } from '@gdco/store';

import { Schedule, ScheduleTemplate } from '@gdco/reference-systems/gdco-service';
import { WorkspacePageState, RecurringTaskDialogType } from './workspace-page.state';
import { RecurringTask } from '../../common';

/**
 * Store managing the state for the workspace page. State will automatically be preserved between page views
 * to provide a smoother user experience.
 */
@Injectable({ providedIn: 'root' })
export class WorkspacePageStore extends SimpleStore<WorkspacePageState> {
  static readonly DEFAULT_STATE: Readonly<WorkspacePageState> = {
    loadingWorkspace: false,
    loadingSchedules: false,
    loadingTemplates: false,
    creatingSchedule: false,
    creatingTemplate: false,
    deletingSelectedSchedules: false,
    deletingSelectedTemplates: false,
    workspaceId: null,
    scheduleIds: [],
    templateIds: [],
    selectedScheduleIds: [],
    selectedTemplateIds: [],
    recurringTaskDialogType: RecurringTaskDialogType.Closed,
    editOrDuplicateRecurringTask: null,
    editScheduleId: null,
    duplicateScheduleId: null,
    selectedCampuses: [],
    selectedUserId: null,
    showCompletedSchedules: false,
    selectedFaultCode: null,
    viewScheduleIds: [],
    viewDate: new Date(),
    scheduleInstanceIds: null,
    selectedScheduleInstanceId: null,
    selectedScheduleInstanceTaskIds: null,
    scheduleInstanceDialogOpen: false,
    loadingScheduleInstanceTasks: false,
    columnOptionsDialogOpen: false,
    savingColumnOptions: false
  };

  /** Indicates if the workspace is currently being loaded. */
  readonly loadingWorkspace$ = this.observeProperty('loadingWorkspace');

  /** Indicates if the workspace schedules are currently being loaded. */
  readonly loadingSchedules$ = this.observeProperty('loadingSchedules');

  /** Indicates if the workspace templates are currently being loaded. */
  readonly loadingTemplates$ = this.observeProperty('loadingTemplates');

  /** Indicates if a schedule is currently being created. */
  readonly creatingSchedule$ = this.observeProperty('creatingSchedule');

  /** Indicates if a schedule template is currently being created. */
  readonly creatingTemplate$ = this.observeProperty('creatingTemplate');

  /** Indicates if the selected schedules are currently being deleted. */
  readonly deletingSelectedSchedules$ = this.observeProperty('deletingSelectedSchedules');

  /** Indicates if the selected templates are currently being deleted. */
  readonly deletingSelectedTemplates$ = this.observeProperty('deletingSelectedTemplates');

  /** The id of the current workspace. */
  readonly workspaceId$ = this.observeProperty('workspaceId');

  /** The ids of all schedules in the current workspace. */
  readonly scheduleIds$ = this.observeProperty('scheduleIds');

  /** The ids of all schedule templates in the current workspace. */
  readonly templateIds$ = this.observeProperty('templateIds');

  /** The ids of the schedules that are currently selected. */
  readonly selectedScheduleIds$ = this.observeProperty('selectedScheduleIds');

  /** The ids of the templates that are currently selected. */
  readonly selectedTemplateIds$ = this.observeProperty('selectedTemplateIds');

  /** The recurring task dialog that is current open. */
  readonly recurringTaskDialogType$ = this.observeProperty('recurringTaskDialogType');

  /** The UI model of the recurring task (schedule) that's currently being edited or duplicated. */
  readonly editOrDuplicateRecurringTask$ = this.observeProperty('editOrDuplicateRecurringTask');

  /** The id of the schedule currently being edited. */
  readonly editScheduleId$ = this.observeProperty('editScheduleId');

  /** The id of the schedule being duplicated. */
  readonly duplicateScheduleId$ = this.observeProperty('duplicateScheduleId');

  /** Fault code selected by the user to create a recurring task for. */
  readonly selectedFaultCode$ = this.observeProperty('selectedFaultCode');

  /** Campuses the schedules are currently filtered by. */
  readonly selectedCampuses$ = this.observeProperty('selectedCampuses');

  /** User the schedules are currently filtered by. */
  readonly selectedUserId$ = this.observeProperty('selectedUserId');

  /** Indicates if completed schedules are shown to the user. */
  readonly showCompletedSchedules$ = this.observeProperty('showCompletedSchedules');

  /** Schedules selected on the calendar page to view the instances for. */
  readonly viewScheduleIds$ = this.observeProperty('viewScheduleIds');

  /** Current date viewed in the calendar. */
  readonly viewDate$ = this.observeProperty('viewDate');

  /** All schedule instance Ids currently displayed on the calendar page. */
  readonly scheduleInstanceIds$ = this.observeProperty('scheduleInstanceIds');

  /** Id of the schedule instance currently viewed by the user. */
  readonly selectedScheduleInstanceId$ = this.observeProperty('selectedScheduleInstanceId');

  /** Ids of the tasks that were created by the currently viewed schedule instance. */
  readonly selectedScheduleInstanceTaskIds$ = this.observeProperty('selectedScheduleInstanceTaskIds');

  /** Indicates if the dialog for viewing a schedule instance is currently open. */
  readonly scheduleInstanceDialogOpen$ = this.observeProperty('scheduleInstanceDialogOpen');

  /** Indicates if the tasks for the currently viewed schedule instance are being loaded. */
  readonly loadingScheduleInstanceTasks$ = this.observeProperty('loadingScheduleInstanceTasks');

  /** Indicates if the dialog to edit the column options on the scheudle tasks tab is open. */
  readonly columnOptionsDialogOpen$ = this.observeProperty('columnOptionsDialogOpen');

  /** Indicates if the workspace column options are currently being saved. */
  readonly savingColumnOptions$ = this.observeProperty('savingColumnOptions');

  constructor(modelFactory: ModelFactory) {
    super(modelFactory, WorkspacePageStore.DEFAULT_STATE);
  }

  /**
   * Initializes the store with the given workspace Id.
   * @param workspaceId The id of the current workspace.
   */
  initializePageStart(workspaceId: string): void {
    this.updateState({
      workspaceId: workspaceId,
      scheduleIds: workspaceId !== this.state.workspaceId ? [] : this.state.scheduleIds,
      templateIds: workspaceId !== this.state.workspaceId ? [] : this.state.templateIds
    });
  }

  /** State mutation indicating a workspace is loading. */
  loadWorkspaceStart(): void {
    this.updateState({ loadingWorkspace: true });
  }

  /** State mutation indicating a workspace has finished loading. */
  loadWorkspaceEnd(): void {
    this.updateState({ loadingWorkspace: false });
  }

  /** State mutation indicating the workspace schedules are loading. */
  loadSchedulesStart(): void {
    this.updateState({ loadingSchedules: true });
  }

  /**
   * State mutation indicating the workspace schedules have finished loading.
   * @param scheduleIds The ids of the schedules loaded.
   */
  loadSchedulesEnd(scheduleIds: string[]): void {
    this.updateState({
      loadingSchedules: false,
      scheduleIds: scheduleIds
    });
  }

  /**
   * Selects the campuses to filter the schedules by.
   * @param selectedCampuses The names of the campuses to filter by.
   */
  selectCampuses(selectedCampuses: string[]): void {
    this.updateState({ selectedCampuses });
  }

  /**
   * Selects the user to filter the schedules by.
   * @param selectedUserId The id of the user to filter by.
   */
  selectUser(selectedUserId: string): void {
    this.updateState({ selectedUserId });
  }

  /**
   * Sets whether completed schedules should be displayed.
   * @param showCompletedSchedules True if completed schedules should be show.
   */
  showCompletedSchedules(showCompletedSchedules: boolean): void {
    this.updateState({ showCompletedSchedules });
  }

  /**
   * Selects the given schedules.
   * @param schedules The schedules to select.
   */
  selectSchedules(schedules: Schedule[]): void {
    const selectedScheduleIds = (schedules || []).map(schedule => schedule.scheduleId);

    this.updateState({ selectedScheduleIds });
  }

  /**
   * Adds a schedule id to the list of schedule ids for the current workspace.
   * @param scheduleId The id of the schedule to add.
   */
  addSchedule(scheduleId: string): void {
    const scheduleIds = this.state.scheduleIds.concat(scheduleId);

    this.updateState({ scheduleIds });
  }

  /** Resets the selected schedules. */
  deselectAllSchedules(): void {
    this.updateState({ selectedScheduleIds: [] });
  }

  /**
   * Selects the given schedule templates.
   * @param templates The templates to select.
   */
  selectTemplates(templates: ScheduleTemplate[]): void {
    const selectedTemplateIds = (templates || []).map(template => template.scheduleTemplateId);

    this.updateState({ selectedTemplateIds });
  }

  /**
   * Adds an id to the list of schedule template ids for the current workspace.
   * @param templateId The id of the schedule template to add.
   */
  addTemplate(templateId: string): void {
    const templateIds = this.state.templateIds.concat(templateId);

    this.updateState({ templateIds });
  }

  /** Resets the selected schedule templates. */
  deselectAllTemplates(): void {
    this.updateState({ selectedTemplateIds: [] });
  }

  /** State mutation indicating the workspace templates are loading. */
  loadTemplatesStart(): void {
    this.updateState({ loadingTemplates: true });
  }

  /**
   * State mutation indicating the workspace templates have finished loading.
   * @param templateIds The ids of the templates loaded.
   */
  loadTemplatesEnd(templateIds: string[]): void {
    this.updateState({
      loadingTemplates: false,
      templateIds: templateIds
    });
  }

  /** State mutation indicating the selected schedules are being deleted. */
  deleteSelectedSchedulesStart(): void {
    this.updateState({ deletingSelectedSchedules: true });
  }

  /**
   * State mutation indicating the selected schedules have finished being deleted.
   * @param deletedScheduleIds The ids of the schedules that were deleted.
   */
  deleteSelectedSchedulesEnd(deletedScheduleIds: string[]): void {
    const scheduleIds =
      deletedScheduleIds && deletedScheduleIds.length > 0
        ? this.state.scheduleIds.filter(id => !deletedScheduleIds.includes(id))
        : this.state.scheduleIds;

    this.updateState({
      deletingSelectedSchedules: false,
      scheduleIds: scheduleIds
    });
  }

  /** State mutation indicating the selected schedules on the calendar page are being deleted. */
  deleteViewedSchedulesStart(): void {
    this.updateState({ deletingSelectedSchedules: true });
  }

  /**
   * State mutation indicating the selected schedules on the calendar page have finished being deleted.
   * @param deletedScheduleIds The ids of the schedules that were deleted.
   */
  deleteViewedSchedulesEnd(deletedScheduleIds: string[]): void {
    const viewScheduleIds =
      deletedScheduleIds && deletedScheduleIds.length > 0
        ? this.state.viewScheduleIds.filter(id => !deletedScheduleIds.includes(id))
        : this.state.viewScheduleIds;

    this.updateState({
      deletingSelectedSchedules: false,
      viewScheduleIds: viewScheduleIds
    });
  }

  /** State mutation indicating the selected templates are being deleted. */
  deleteSelectedTemplatesStart(): void {
    this.updateState({ deletingSelectedTemplates: true });
  }

  /**
   * State mutation indicating the selected templates have finished being deleted.
   * @param deletedScheduleIds The ids of the templates that were deleted.
   */
  deleteSelectedTemplatesEnd(deletedTemplateIds: string[]): void {
    const templateIds =
      deletedTemplateIds && deletedTemplateIds.length > 0
        ? this.state.templateIds.filter(id => !deletedTemplateIds.includes(id))
        : this.state.templateIds;

    this.updateState({
      deletingSelectedTemplates: false,
      templateIds: templateIds
    });
  }

  removeInstances(instanceIds: string[]): void {
    const scheduleInstanceIds = this.state.scheduleInstanceIds?.filter(id => !instanceIds.includes(id));

    this.updateState({ scheduleInstanceIds });
  }

  /** Closes the recurring task dialog. */
  closeRecurringTaskDialog(): void {
    this.updateState({
      recurringTaskDialogType: RecurringTaskDialogType.Closed,
      editOrDuplicateRecurringTask: null,
      editScheduleId: null,
      duplicateScheduleId: null,
      selectedFaultCode: null
    });
  }

  /** Opens the dialog for selecting task for recurring task creation. */
  openNewRecurringTaskSelectionDialog(): void {
    this.updateState({
      recurringTaskDialogType: RecurringTaskDialogType.SelectTask,
      editOrDuplicateRecurringTask: null,
      editScheduleId: null,
      duplicateScheduleId: null,
      selectedFaultCode: null
    });
  }

  /**
   * Opens the dialog for creating a recurring task.
   * @param faultCode The fault code to create the recurring task with.
   */
  openNewRecurringTaskDialog(faultCode: number): void {
    this.updateState({
      recurringTaskDialogType: RecurringTaskDialogType.New,
      editOrDuplicateRecurringTask: null,
      editScheduleId: null,
      duplicateScheduleId: null,
      selectedFaultCode: faultCode
    });
  }

  /**
   * Opens the dialog for editing a recurring task.
   * @param recurringTask The recurring task to edit.
   * @param schedule The existing schedule associated with the recurring task.
   */
  openEditRecurringTaskDialog(recurringTask: RecurringTask, schedule: Schedule): void {
    this.updateState({
      recurringTaskDialogType: RecurringTaskDialogType.Edit,
      editOrDuplicateRecurringTask: recurringTask,
      editScheduleId: schedule.scheduleId,
      duplicateScheduleId: null,
      selectedFaultCode: schedule.clientData?.faultCode
    });
  }

  /**
   * Opens the dialog for duplicating a recurring task.
   * @param recurringTask The recurring task to duplicate.
   * @param schedule The existing schedule associated with the recurring task.
   */
  openDuplicateRecurringTaskDialog(recurringTask: RecurringTask, schedule: Schedule): void {
    this.updateState({
      recurringTaskDialogType: RecurringTaskDialogType.Duplicate,
      editOrDuplicateRecurringTask: recurringTask,
      editScheduleId: null,
      duplicateScheduleId: schedule.scheduleId,
      selectedFaultCode: schedule.clientData?.faultCode
    });
  }

  /** State mutation indicating a new recurring task (schedule) is being created. */
  createNewRecurringTaskStart(): void {
    this.updateState({ creatingSchedule: true });
  }

  /** State mutation indicating a new recurring task (schedule) has finished being created. */
  createNewRecurringTaskEnd(): void {
    this.updateState({ creatingSchedule: false });
  }

  /** State mutation indicating a recurring task (schedule) is being updated. */
  updateRecurringTaskStart(): void {
    this.updateState({ creatingSchedule: true });
  }

  /** State mutation indicating a recurring task (schedule) has finished being updated. */
  updateRecurringTaskEnd(): void {
    this.updateState({ creatingSchedule: false });
  }

  /** State mutation indicating a new schedule template is being created. */
  createNewTemplateStart(): void {
    this.updateState({ creatingTemplate: true });
  }

  /** State mutation indicating a new schedule template has finished being created. */
  createNewTemplateEnd(): void {
    this.updateState({ creatingTemplate: false });
  }

  /** State mutation indicating a schedule template is being updated. */
  updateTemplateStart(): void {
    this.updateState({ creatingTemplate: true });
  }

  /** State mutation indicating a schedule template has finished being updated. */
  updateTemplateEnd(): void {
    this.updateState({ creatingTemplate: false });
  }

  /**
   * State mutation indicating the schedule instances are loading.
   * @param viewScheduleIds The ids of the schedule the instances are being loaded for.
   */
  loadScheduleInstancesStart(viewScheduleIds: string[]): void {
    this.updateState({ viewScheduleIds });
  }

  /**
   * State mutation indicating the schedule instances have finished loading.
   * @param scheduleInstanceIds The ids of the schedule instances loaded.
   */
  loadScheduleInstancesEnd(scheduleInstanceIds: string[]): void {
    this.updateState({ scheduleInstanceIds });
  }

  /**
   * State mutation indicating the schedule instances have finished loading.
   * @param instanceIds The ids of the schedule instances loaded.
   */
  loadScheduleInstancesPageEnd(instanceIds: string[], viewDate: Date): void {
    const scheduleInstanceIds = gdcoUtils.union(this.state.scheduleInstanceIds, instanceIds);

    this.updateState({ scheduleInstanceIds, viewDate });
  }

  /**
   * Opens the dialog for viewing a schedule instance.
   * @param scheduleInstanceId The id of the schedule instance to view.
   */
  openScheduleInstanceDialog(scheduleInstanceId: string): void {
    this.updateState({
      scheduleInstanceDialogOpen: true,
      selectedScheduleInstanceId: scheduleInstanceId,
      selectedScheduleInstanceTaskIds: null,
      loadingScheduleInstanceTasks: true
    });
  }

  /**
   * Sets the Ids of the tasks that were created by the current viewed schedule instance.
   * @param taskIds The ids of the tasks created.
   */
  setSelectedScheduleInstanceTaskIds(taskIds: string[]): void {
    this.updateState({
      selectedScheduleInstanceTaskIds: taskIds,
      loadingScheduleInstanceTasks: false
    });
  }

  /** Closes the schedule instance dialog. */
  closeScheduleInstanceDialog(): void {
    this.updateState({
      scheduleInstanceDialogOpen: false,
      selectedScheduleInstanceId: null,
      selectedScheduleInstanceTaskIds: null
    });
  }

  /** Clears any schedules currently being viewed */
  clearViewedSchedules(): void {
    this.updateState({
      viewScheduleIds: [],
      scheduleInstanceIds: []
    });
  }

  /** Opens the dialog for modified the display scheduled tasks columns. */
  openColumnOptionsDialog(): void {
    this.updateState({ columnOptionsDialogOpen: true });
  }

  /** Closes the column options dialog. */
  closeColumnOptionsDialog(): void {
    this.updateState({ columnOptionsDialogOpen: false });
  }

  /** State mutation indicating the workspace's column options are being updated. */
  updateWorkspaceColumnOptionsStart(): void {
    this.updateState({ savingColumnOptions: true });
  }

  /** State mutation indicating the workspace's column options have finished updating. */
  updateWorkspaceColumnOptionsEnd(): void {
    this.updateState({ savingColumnOptions: false });
  }
}
