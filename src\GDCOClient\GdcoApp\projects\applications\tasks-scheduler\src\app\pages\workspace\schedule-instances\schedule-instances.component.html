<tasks-scheduler-instances-actions
  [selectedSchedules]="viewSchedules$ | async"
  [deleting]="deletingSelectedSchedules$ | async"
  [campuses]="scheduleCampuses$ | async"
  [users]="scheduleCreatedByUsers$ | async"
  [selectedCampuses]="selectedCampuses$ | async"
  [selectedUser]="selectedUser$ | async"
  [hasCompletedSchedules]="hasCompletedSchedules$ | async"
  [showCompletedSchedules]="showCompletedSchedules$ | async"
  (new)="onNewRecurringTask()"
  (delete)="onDeleteViewedSchedules()"
  (campusSelected)="onCampusSelected($event)"
  (userSelected)="onUserSelected($event)"
  (showCompletedSchedulesChange)="onShowCompletedSchedulesChange($event)"
></tasks-scheduler-instances-actions>

<mat-sidenav-container class="instances-sidenav-container gdco-background">
  <mat-sidenav class="instances-sidenav" opened="true" mode="side" gdcoElevation="1">
    <tasks-scheduler-instances-sidenav
      [loadingSchedules]="loadingSchedules$ | async"
      [mySchedules]="mySchedules$ | async"
      [selectedSchedules]="viewSchedules$ | async"
      [campuses]="filteredScheduleCampuses$ | async"
      [schedulesByCampus]="schedulesByCampus$ | async"
      (selectionChanged)="onSelectedSchedulesChanged($event)"
      (edit)="onEditSchedule($event)"
      (duplicate)="onDuplicateSchedule($event)"
      (delete)="onDeleteSchedule($event)"
    ></tasks-scheduler-instances-sidenav>
  </mat-sidenav>

  <mat-sidenav-content>
    <tasks-scheduler-instances-calendar
      [viewDate]="viewDate$ | async"
      [workspace]="workspace$ | async"
      [instances]="scheduleInstances$ | async"
      [schedulesById]="schedulesById$ | async"
      (instanceSelected)="onInstanceSelected($event)"
      (load)="onLoadMoreInstances($event)"
    ></tasks-scheduler-instances-calendar>
  </mat-sidenav-content>
</mat-sidenav-container>

<!-- Instance details dialog -->
<gdco-dialog
  #instanceDialog
  [label]="(selectedScheduleInstance$ | async)?.manualScheduledAt | workspaceDate | amDateFormat: 'MMMM Do, YYYY'"
  [opened]="scheduleInstanceDialogOpen$ | async"
  (openedChange)="onDialogOpenedChanged($event)"
>
  <ng-template gdcoDialogContent>
    <tasks-scheduler-instance
      [schedule]="selectedScheduleInstanceSchedule$ | async"
      [instance]="selectedScheduleInstance$ | async"
      [tasks]="selectedScheduleInstanceTasks$ | async"
      [loadingTasks]="loadingScheduleInstanceTasks$ | async"
    ></tasks-scheduler-instance>
  </ng-template>
</gdco-dialog>

<!-- New recurring task selection dialog -->
<gdco-dialog
  label="New recurring task"
  [disableClose]="true"
  [opened]="(recurringTaskDialogType$ | async) === RecurringTaskDialogType.SelectTask"
  (openedChange)="onRecurringTaskDialogOpenChanged($event, RecurringTaskDialogType.SelectTask)"
>
  <ng-template gdcoDialogContent>
    <tasks-scheduler-select-task
      [faultCodes]="templateFaultCodes$ | async"
      (next)="onTaskSelected($event)"
      (cancel)="onCloseRecurringTask()"
    >
    </tasks-scheduler-select-task>
  </ng-template>
</gdco-dialog>

<!-- New recurring task dialog -->
<gdco-dialog
  label="New recurring task"
  [opened]="(recurringTaskDialogType$ | async) === RecurringTaskDialogType.New"
  (openedChange)="onRecurringTaskDialogOpenChanged($event, RecurringTaskDialogType.New)"
  [disableClose]="true"
>
  <ng-template gdcoDialogContent>
    <tasks-scheduler-recurring-task
      [template]="selectedTemplate$ | async"
      [faultCode]="selectedFaultCode$ | async"
      [saving]="creatingSchedule$ | async"
      (save)="onSaveRecurringTask($event)"
      (cancel)="onCloseRecurringTask()"
    ></tasks-scheduler-recurring-task>
  </ng-template>
</gdco-dialog>

<!-- Edit recurring task dialog -->
<gdco-dialog
  label="Edit recurring task"
  [opened]="(recurringTaskDialogType$ | async) === RecurringTaskDialogType.Edit"
  (openedChange)="onRecurringTaskDialogOpenChanged($event, RecurringTaskDialogType.Edit)"
  [disableClose]="true"
>
  <ng-template gdcoDialogContent>
    <tasks-scheduler-recurring-task
      [template]="editScheduleTemplate$ | async"
      [faultCode]="selectedFaultCode$ | async"
      [recurringTask]="editOrDuplicateRecurringTask$ | async"
      [saving]="creatingSchedule$ | async"
      (save)="onSaveUpdatedRecurringTask($event)"
      (cancel)="onCloseRecurringTask()"
    ></tasks-scheduler-recurring-task>
  </ng-template>
</gdco-dialog>

<!-- Duplicate recurring task dialog -->
<gdco-dialog
  label="Duplicate recurring task"
  [opened]="(recurringTaskDialogType$ | async) === RecurringTaskDialogType.Duplicate"
  (openedChange)="onRecurringTaskDialogOpenChanged($event, RecurringTaskDialogType.Duplicate)"
  [disableClose]="true"
>
  <ng-template gdcoDialogContent>
    <tasks-scheduler-recurring-task
      [template]="duplicateScheduleTemplate$ | async"
      [faultCode]="selectedFaultCode$ | async"
      [recurringTask]="editOrDuplicateRecurringTask$ | async"
      [saving]="creatingSchedule$ | async"
      (save)="onSaveRecurringTask($event)"
      (cancel)="onCloseRecurringTask()"
    ></tasks-scheduler-recurring-task>
  </ng-template>
</gdco-dialog>
