<gdco-action-bar>
  <div fxLayout="row wrap" fxLayoutGap="16px">
    <button
      mat-flat-button
      color="primary"
      (click)="onNewRecurringTaskClicked()"
      gdcoTrack="NewRecurringTaskClicked"
      [gdcoTrackData]="{ view: 'list' }"
      class="template-action-button"
    >
      New recurring task
    </button>
    <button
      mat-stroked-button
      color="primary"
      [disabled]="!selectedSchedules?.length || deleting"
      (click)="onDeleteClicked()"
      gdcoTrack="DeleteSchedulesClicked"
      [gdcoTrackData]="{ count: selectedSchedules?.length }"
      class="template-action-button"
    >
      <gdco-spinner-button-content
        text="Delete"
        workingText="Deleting"
        [working]="deleting"
      ></gdco-spinner-button-content>
    </button>
    <a
      mat-stroked-button
      color="primary"
      routerLink="calendar"
      gdcoTrack="ViewCalendarClicked"
      class="template-action-button"
      ><mat-icon>event</mat-icon> View calendar</a
    >
  </div>

  <div fxLayout="row" fxLayoutGap="8px" fxLayoutAlign="start center">
    <gdco-label>Campus:</gdco-label>
    <mat-form-field class="gdco-compact">
      <mat-select
        gdcoMatSelectAccessibility
        aria-label="Select campuses"
        placeholder="All"
        multiple
        [value]="selectedCampuses"
        (selectionChange)="onCampusSelectionChanged($event)"
      >
        <mat-option
          *ngFor="let campus of campuses"
          [value]="campus"
          gdcoTrack="ScheduleCampusFilter"
          [gdcoTrackData]="{ option: campus }"
          >{{ campus }}</mat-option
        >
      </mat-select>
    </mat-form-field>
  </div>

  <div fxLayout="row" fxLayoutGap="8px" fxLayoutAlign="start center">
    <gdco-label>Created by:</gdco-label>
    <mat-form-field class="gdco-compact">
      <gdco-user-avatar *ngIf="selectedUser" matPrefix [user]="selectedUserPrincipalName()"></gdco-user-avatar>
      <mat-select
        gdcoMatSelectAccessibility
        aria-label="Created by"
        placeholder="Anyone"
        [value]="selectedUser"
        (selectionChange)="onUserSelectionChanged($event)"
      >
        <mat-option [value]="null" gdcoTrack="ScheduleCreatedByFilter" [gdcoTrackData]="{ option: 'Anyone' }"
          >Anyone</mat-option
        >
        <mat-option [value]="currentUserId" gdcoTrack="ScheduleCreatedByFilter" [gdcoTrackData]="{ option: 'Me' }"
          >Me</mat-option
        >
        <mat-option
          *ngFor="let user of users"
          [value]="user.ObjectId"
          gdcoTrack="ScheduleCreatedByFilter"
          [gdcoTrackData]="{ option: 'User' }"
        >
          <gdco-user-avatar [user]="user.UserPrincipalName"></gdco-user-avatar>
          <span>{{ user.DisplayName }}</span>
        </mat-option>
      </mat-select>
    </mat-form-field>
  </div>

  <mat-checkbox
    *ngIf="hasCompletedSchedules"
    [checked]="showCompletedSchedules"
    (change)="onShowCompletedSchedulesChanged($event)"
    >Show completed schedules</mat-checkbox
  >

  <div fxFlex></div>

  <button
    *gdcoAuth="allWorkspacesResource; action: 'Manage'"
    mat-stroked-button
    color="primary"
    (click)="onColumnOptionsButtonClicked()"
  >
    <mat-icon>view_column</mat-icon> Column options
  </button>
</gdco-action-bar>
