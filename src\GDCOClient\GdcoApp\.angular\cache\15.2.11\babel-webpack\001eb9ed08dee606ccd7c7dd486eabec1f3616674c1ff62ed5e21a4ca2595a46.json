{"ast": null, "code": "import _asyncToGenerator from \"D:/Repo/GDCO/MCIO-GDCO-AppService/src/GDCOClient/GdcoApp/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nvar __decorate = this && this.__decorate || function (decorators, target, key, desc) {\n  var c = arguments.length,\n    r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc,\n    d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n};\nvar __metadata = this && this.__metadata || function (k, v) {\n  if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(k, v);\n};\n/**\r\n * @license\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n */\nimport { FrequencyType, RecurringTaskConverter } from '../../common';\nimport { TemplateFormConverter } from './template-details/edit';\nimport { TemplateDetailsStore } from './template-details/template-details.store';\nimport { WorkspacePageStore } from './workspace-page.store';\nimport { GdcoAuth } from '@gdco/auth';\nimport { GdcoQueryBuilder, GdcoQueryOperator } from '@gdco/common';\nimport { GdcoNotifications, GdcoConfirmationDialog, GdcoAppInsights, gdcoUtils } from '@gdco/core';\nimport { DatacentersManager, DomainDataManager, TasksManager, UsersManager } from '@gdco/core-reference-systems/gdco-service';\nimport { ScheduleInstanceState, ScheduleState, ScheduleTemplateParameterDataType, SchedulerManager } from '@gdco/reference-systems/gdco-service';\nimport { Action, isActionCanceled, ActionPayloadContext, ActionType, ActionContext, awaitable, filterItems, sortItems, mapItems, distictItems, groupItemsBy } from '@gdco/store';\nimport { RecurringTaskFieldResolver } from '@tasks/scheduling';\nimport { combineLatest } from 'rxjs';\nimport { map } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./workspace-page.store\";\nimport * as i2 from \"./template-details/template-details.store\";\nimport * as i3 from \"@gdco/auth\";\nimport * as i4 from \"@gdco/core\";\nimport * as i5 from \"@gdco/reference-systems/gdco-service\";\nimport * as i6 from \"@gdco/core-reference-systems/gdco-service\";\nimport * as i7 from \"@tasks/scheduling\";\n/**\r\n * View model for the workspace page. All events and/or logic in the workspace page component should be delegated here instead.\r\n */\nexport class WorkspacePageViewModel {\n  /** Gets the current workspace. */\n  get workspace() {\n    return this._schedulerManager.workspaces.get(this.store.state.workspaceId);\n  }\n  /** Gets all schedules in the current workspace. */\n  get schedules() {\n    return this._schedulerManager.schedules.getEntities(this.store.state.scheduleIds);\n  }\n  /** Gets all templates in the current workspace. */\n  get templates() {\n    return this._schedulerManager.templates.getEntities(this.store.state.templateIds);\n  }\n  get template() {\n    return this._schedulerManager.templates.get(this.templateDetailsStore.state.templateId);\n  }\n  /** Gets all schedule instances currently displayed. */\n  get instances() {\n    return this._schedulerManager.instances.getEntities(this.store.state.scheduleInstanceIds);\n  }\n  constructor(store, templateDetailsStore, _auth, _confirmationDialog, _notifications, _appInsights, _schedulerManager, _domainDataManager, _datacentersManager, _usersManager, _tasksManager, _fieldResolver) {\n    this.store = store;\n    this.templateDetailsStore = templateDetailsStore;\n    this._auth = _auth;\n    this._confirmationDialog = _confirmationDialog;\n    this._notifications = _notifications;\n    this._appInsights = _appInsights;\n    this._schedulerManager = _schedulerManager;\n    this._domainDataManager = _domainDataManager;\n    this._datacentersManager = _datacentersManager;\n    this._usersManager = _usersManager;\n    this._tasksManager = _tasksManager;\n    this._fieldResolver = _fieldResolver;\n    this.workspace$ = this._schedulerManager.workspaces.observe(this.store.workspaceId$);\n    this.schedules$ = this._schedulerManager.schedules.observeEntities(this.store.scheduleIds$);\n    this.activeSchedules$ = this.schedules$.pipe(filterItems(schedule => schedule.state === ScheduleState.Active));\n    this.hasCompletedSchedules$ = this.schedules$.pipe(map(schedules => !!schedules?.find(schedule => schedule.state === ScheduleState.Complete)));\n    this.filteredSchedules$ = combineLatest([this.schedules$, this.store.selectedCampus$, this.store.selectedUserId$, this.store.showCompletedSchedules$, this.store.searchTerm$]).pipe(map(([schedules, selectedCampus, userId, showCompletedSchedules, searchTerm]) => {\n      return this._filterSchedules(schedules, selectedCampus, userId, showCompletedSchedules, searchTerm);\n    }));\n    this.mySchedules$ = this.filteredSchedules$.pipe(filterItems(schedule => schedule.createdBy === this._auth.currentUser.userId), sortItems((a, b) => {\n      const aDatacenter = this._fieldResolver.getFieldForSchedule(a, 'DatacenterCode') || '';\n      const bDatacenter = this._fieldResolver.getFieldForSchedule(b, 'DatacenterCode') || '';\n      // Sort by datacenter before sorting by name\n      return aDatacenter.toLowerCase().localeCompare(bDatacenter.toLowerCase());\n    }), sortItems((a, b) => a.name.toLowerCase().localeCompare(b.name.toLowerCase())));\n    this.template$ = this._schedulerManager.templates.observe(this.templateDetailsStore.templateId$);\n    this.templates$ = this._schedulerManager.templates.observeEntities(this.store.templateIds$);\n    this.selectedSchedules$ = this._schedulerManager.schedules.observeEntities(this.store.selectedScheduleIds$);\n    this.selectedTemplates$ = this._schedulerManager.templates.observeEntities(this.store.selectedTemplateIds$);\n    this.diffOriginalTemplate$ = this._schedulerManager.templateVersions.observe(combineLatest([this.templateDetailsStore.templateId$, this.templateDetailsStore.diffOriginalVersion$]).pipe(map(([templateId, version]) => this._createTemplateVersionKey(templateId, version))));\n    this.diffModifiedTemplate$ = this._schedulerManager.templateVersions.observe(combineLatest([this.templateDetailsStore.templateId$, this.templateDetailsStore.diffModifiedVersion$]).pipe(map(([templateId, version]) => this._createTemplateVersionKey(templateId, version))));\n    this.viewSchedules$ = this._schedulerManager.schedules.observeEntities(this.store.viewScheduleIds$);\n    this.scheduleInstances$ = this._schedulerManager.instances.observeEntities(this.store.scheduleInstanceIds$);\n    this.selectedScheduleInstance$ = this._schedulerManager.instances.observe(this.store.selectedScheduleInstanceId$);\n    this.selectedScheduleInstanceSchedule$ = this._schedulerManager.schedules.observe(this.selectedScheduleInstance$.pipe(map(instance => instance?.scheduleId)));\n    this.selectedScheduleInstanceTasks$ = this._tasksManager.tasks.observeEntities(this.store.selectedScheduleInstanceTaskIds$);\n    this.selectedTemplate$ = combineLatest([this.templates$, this.store.selectedFaultCode$]).pipe(map(([templates, faultCode]) => {\n      if (!templates || !faultCode) {\n        return null;\n      }\n      return templates.find(template => this._faultCodeExistsInTemplate(template, faultCode));\n    }));\n    this.selectedFaultCode$ = this._domainDataManager.faultCodes.observe(this.store.selectedFaultCode$.pipe(map(faultCode => faultCode?.toString())));\n    // We need to depend on templates$ here because the field resolver uses them internally\n    this.scheduleCampuses$ = combineLatest([this.schedules$, this.templates$]).pipe(map(([schedules]) => this._getCampusesFromSchedules(schedules)));\n    // We need to depend on templates$ here because the field resolver uses them internally\n    this.filteredScheduleCampuses$ = combineLatest([this.filteredSchedules$, this.templates$]).pipe(map(([schedules]) => this._getCampusesFromSchedules(schedules)));\n    const createdByUserIds = this.schedules$.pipe(mapItems(schedule => schedule.createdBy), distictItems());\n    this.scheduleCreatedByUsers$ = this._usersManager.users.observeEntities(createdByUserIds).pipe(sortItems((a, b) => a.DisplayName.toLowerCase().localeCompare(b.DisplayName.toLowerCase())));\n    this.faultCodes$ = this._domainDataManager.faultCodes.observeAll();\n    this.metadata$ = this._domainDataManager.metadata$;\n    this.faultCodeMap$ = this.faultCodes$.pipe(groupItemsBy('code', true));\n    this.templateFaultCodes$ = combineLatest([this.templates$, this.faultCodes$]).pipe(map(([templates, faultCodes]) => {\n      if (!templates || !faultCodes) {\n        return [];\n      }\n      return faultCodes.filter(faultCode => {\n        return templates.find(template => {\n          return this._faultCodeExistsInTemplate(template, faultCode.code) && !template.clientData?.disableUiCreation;\n        });\n      }).sort((a, b) => a.description.toLowerCase().localeCompare(b.description.toLowerCase()));\n    }));\n    // We need to depend on templates$ here because the field resolver uses them internally\n    this.schedulesByCampus$ = combineLatest([this.scheduleCampuses$, this.filteredSchedules$, this.templates$]).pipe(map(([campuses, schedules]) => {\n      if (!campuses || !schedules) {\n        return {};\n      }\n      return campuses.reduce((prev, curr) => {\n        prev[curr] = schedules.filter(schedule => {\n          const campus = this._fieldResolver.getFieldForSchedule(schedule, 'FacilityCampus');\n          return this._getCampusName(campus) === curr;\n        }).sort((a, b) => {\n          const aDatacenter = this._fieldResolver.getFieldForSchedule(a, 'DatacenterCode') || '';\n          const bDatacenter = this._fieldResolver.getFieldForSchedule(b, 'DatacenterCode') || '';\n          // Sort by datacenter before sorting by name\n          return aDatacenter.toLowerCase().localeCompare(bDatacenter.toLowerCase());\n        }).sort((a, b) => a.name.toLowerCase().localeCompare(b.name.toLowerCase()));\n        return prev;\n      }, {});\n    }));\n    this.schedulesById$ = this.schedules$.pipe(groupItemsBy('scheduleId', true));\n    this.editSchedule$ = this._schedulerManager.schedules.observe(this.store.editScheduleId$);\n    this.editScheduleTemplate$ = this._schedulerManager.templates.observe(this.editSchedule$.pipe(map(schedule => schedule?.templateId)));\n    this.duplicateSchedule$ = this._schedulerManager.schedules.observe(this.store.duplicateScheduleId$);\n    this.duplicateScheduleTemplate$ = this._schedulerManager.templates.observe(this.duplicateSchedule$.pipe(map(schedule => schedule?.templateId)));\n    // We need to depend on templates$ here because the field resolver uses them internally\n    this.scheduleFieldMap$ = combineLatest([this.schedules$, this.templates$]).pipe(map(([schedules]) => {\n      return schedules.reduce((prev, curr) => {\n        prev[curr.scheduleId] = {\n          FacilityCampus: this._fieldResolver.getFieldForSchedule(curr, 'FacilityCampus'),\n          DatacenterCode: this._fieldResolver.getFieldForSchedule(curr, 'DatacenterCode'),\n          Colocation: this._fieldResolver.getFieldForSchedule(curr, 'Colocation'),\n          Group: this._fieldResolver.getFieldForSchedule(curr, 'Group'),\n          AssignedTo: this._fieldResolver.getFieldForSchedule(curr, 'AssignedTo'),\n          FacilityTimeZone: this._fieldResolver.getFieldForSchedule(curr, 'FacilityTimeZone')\n        };\n        return prev;\n      }, {});\n    }));\n  }\n  /**\r\n   * Initializes the workspace page, loading all schedules and templates in the workspace.\r\n   * @param context.payload The id of the workspace.\r\n   */\n  initializePage(context) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      const workspaceId = context.payload;\n      _this.store.initializePageStart(workspaceId);\n      yield _this.loadWorkspace(context);\n      yield Promise.all([_this.loadSchedules(context), _this.loadTemplates(context)]);\n      yield _this.loadMetadata(context);\n    })();\n  }\n  /**\r\n   * Initializes the schedule instances page by schedule id.\r\n   * @param context.payload The schedule id to load the schedule instances page.\r\n   */\n  initializeScheduleInstancesPageByScheduleId(context) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      const scheduleId = context.payload;\n      _this2.store.clearViewedSchedules();\n      yield _this2.loadScheduleInstances(new ActionPayloadContext([scheduleId]));\n      const schedule = _this2._schedulerManager.schedules.get(scheduleId);\n      if (schedule?.state === ScheduleState.Complete) {\n        _this2.store.showCompletedSchedules(true);\n      }\n    })();\n  }\n  /**\r\n   * Loads the workspace with the given id.\r\n   * @param context.payload The id of the workspace to load.\r\n   */\n  loadWorkspace(context) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        _this3.store.loadWorkspaceStart();\n        yield _this3._schedulerManager.getWorkspaceById(context);\n      } catch (error) {\n        if (!isActionCanceled(error)) {\n          _this3._notifications.addError('Failed to load workspace.', error);\n        }\n        throw error; // Always re-throw errors\n      } finally {\n        _this3.store.loadWorkspaceEnd();\n      }\n    })();\n  }\n  /** Loads all schedules for the current workspace. */\n  loadSchedules(context) {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      let scheduleIds = [];\n      const {\n        workspaceId\n      } = _this4.store.state;\n      _this4.store.loadSchedulesStart();\n      // Verify the workspace is loaded\n      yield _this4.loadWorkspace(new ActionPayloadContext(workspaceId, context));\n      try {\n        const payload = {\n          workspaceId: workspaceId\n        };\n        const schedules = yield _this4._schedulerManager.listSchedulesByWorkspaceId(new ActionPayloadContext(payload, context));\n        // TODO: Remove this 'loadDatacenters' call after SOP schedules are updated to include campus\n        yield _this4._datacentersManager.loadDatacenters(context);\n        scheduleIds = schedules.map(schedule => schedule.scheduleId);\n      } catch (error) {\n        if (!isActionCanceled(error)) {\n          _this4._notifications.addError('Failed to load existing recurring tasks.', error);\n        }\n        throw error; // Always re-throw errors\n      } finally {\n        _this4.store.loadSchedulesEnd(scheduleIds);\n      }\n    })();\n  }\n  /** Loads all schedule templates for the current workspace. */\n  loadTemplates(context) {\n    var _this5 = this;\n    return _asyncToGenerator(function* () {\n      let templateIds = [];\n      const {\n        workspaceId\n      } = _this5.store.state;\n      _this5.store.loadTemplatesStart();\n      // Verify the workspace is loaded\n      yield _this5.loadWorkspace(new ActionPayloadContext(workspaceId, context));\n      try {\n        const schedules = yield _this5._schedulerManager.listScheduleTemplatesByWorkspaceId(new ActionPayloadContext(workspaceId, context));\n        templateIds = schedules.map(schedule => schedule.scheduleTemplateId);\n      } catch (error) {\n        if (!isActionCanceled(error)) {\n          _this5._notifications.addError('Failed to load existing templates.', error);\n        }\n        throw error; // Always re-throw errors\n      } finally {\n        _this5.store.loadTemplatesEnd(templateIds);\n      }\n    })();\n  }\n  loadTemplateVersions(context) {\n    var _this6 = this;\n    return _asyncToGenerator(function* () {\n      const templateId = context.payload;\n      const {\n        workspaceId\n      } = _this6.store.state;\n      try {\n        const payload = {\n          workspaceId: workspaceId,\n          templateId: templateId\n        };\n        const templates = yield _this6._schedulerManager.listScheduleTemplateVersionsById(new ActionPayloadContext(payload, context));\n        _this6.templateDetailsStore.diffTemplateView(_this6.template.version, _this6.template.version - 1);\n      } catch (error) {\n        if (!isActionCanceled(error)) {\n          _this6._notifications.addError('Failed to load existing templates.', error);\n        }\n        throw error; // Always re-throw errors\n      } finally {}\n    })();\n  }\n  /**\r\n   * Loads all scheduled instances for the schedules with the given Ids.\r\n   * @param context.payload A list of schedule Ids to load the instances for.\r\n   */\n  loadScheduleInstances(context) {\n    var _this7 = this;\n    return _asyncToGenerator(function* () {\n      const scheduleIds = context.payload;\n      const {\n        workspaceId,\n        viewDate\n      } = _this7.store.state;\n      let scheduleInstanceIds = [];\n      _this7.store.loadScheduleInstancesStart(scheduleIds);\n      // Verify the workspace is loaded\n      yield _this7.loadWorkspace(new ActionPayloadContext(workspaceId, context));\n      try {\n        const promises = [];\n        // Load the instances for all given schedules in parallel\n        for (const scheduleId of scheduleIds) {\n          const payload = {\n            workspaceId: workspaceId,\n            scheduleId: scheduleId\n          };\n          // Load all active instances\n          const promise = _this7._schedulerManager.listScheduleInstancesByScheduleId(new ActionPayloadContext(payload, context));\n          promises.push(promise);\n          // Don't load pages beyond the end date or before the start date\n          const schedule = _this7._schedulerManager.schedules.get(scheduleId);\n          const startDate = schedule?.recurrencePattern.startDate;\n          const endDate = schedule?.recurrencePattern.endDate;\n          if (!schedule || viewDate.getFullYear() >= startDate.getFullYear() && (!endDate || viewDate.getFullYear() <= endDate.getFullYear())) {\n            const paginPayload = {\n              workspaceId: workspaceId,\n              scheduleId: scheduleId,\n              pageDate: viewDate\n            };\n            // Load current page of instances\n            const pagingPromise = _this7._schedulerManager.listScheduleInstancesPageByScheduleId(new ActionPayloadContext(paginPayload, context));\n            promises.push(pagingPromise);\n          }\n        }\n        const results = yield Promise.all(promises);\n        scheduleInstanceIds = results.reduce((prev, curr) => {\n          return prev.concat(...curr.map(instance => instance.scheduleInstanceId));\n        }, []);\n        scheduleInstanceIds = gdcoUtils.distinct(scheduleInstanceIds);\n      } catch (error) {\n        if (!isActionCanceled(error)) {\n          _this7._notifications.addError('Failed to load schedule instances.', error);\n        }\n        throw error; // Always re-throw errors\n      } finally {\n        _this7.store.loadScheduleInstancesEnd(scheduleInstanceIds);\n      }\n    })();\n  }\n  /** Loads all scheduled instances in the current year for the given date. */\n  loadScheduleInstancesPage(context) {\n    var _this8 = this;\n    return _asyncToGenerator(function* () {\n      const pageDate = context.payload;\n      const {\n        workspaceId\n      } = _this8.store.state;\n      let scheduleInstanceIds = [];\n      // Verify the workspace is loaded\n      yield _this8.loadWorkspace(new ActionPayloadContext(workspaceId, context));\n      try {\n        const promises = [];\n        // Load the instance pages for all current viewed schedules in parallel\n        for (const scheduleId of _this8.store.state.viewScheduleIds) {\n          const schedule = _this8._schedulerManager.schedules.get(scheduleId);\n          const startDate = schedule?.recurrencePattern.startDate;\n          const endDate = schedule?.recurrencePattern.endDate;\n          // Don't load pages beyond the end date or before the start date\n          if (!schedule || pageDate.getFullYear() >= startDate.getFullYear() && (!endDate || pageDate.getFullYear() <= endDate.getFullYear())) {\n            const payload = {\n              workspaceId: workspaceId,\n              scheduleId: scheduleId,\n              pageDate: pageDate\n            };\n            const promise = _this8._schedulerManager.listScheduleInstancesPageByScheduleId(new ActionPayloadContext(payload, context));\n            promises.push(promise);\n          }\n        }\n        const results = yield Promise.all(promises);\n        scheduleInstanceIds = results.reduce((prev, curr) => {\n          return prev.concat(...curr.map(instance => instance.scheduleInstanceId));\n        }, []);\n      } catch (error) {\n        if (!isActionCanceled(error)) {\n          _this8._notifications.addError('Failed to load schedule instances.', error);\n        }\n        throw error; // Always re-throw errors\n      } finally {\n        _this8.store.loadScheduleInstancesPageEnd(scheduleInstanceIds, pageDate);\n      }\n    })();\n  }\n  /** Loads all metadata needed by the workspace. */\n  loadMetadata(context) {\n    var _this9 = this;\n    return _asyncToGenerator(function* () {\n      const schedules = _this9._schedulerManager.schedules.getEntities(_this9.store.state.scheduleIds);\n      const userIds = schedules.map(schedule => schedule.createdBy);\n      yield Promise.all([_this9._domainDataManager.loadTaskMetadata(context), _this9._domainDataManager.loadFaultCodes(context), _this9._datacentersManager.loadDatacenters(context), _this9._usersManager.getUsersByIds(new ActionPayloadContext(userIds, context))]);\n    })();\n  }\n  /**\r\n   * Deletes the given schedule.\r\n   * @param context.payload The schedule to delete.\r\n   */\n  deleteSchedule(context) {\n    var _this10 = this;\n    return _asyncToGenerator(function* () {\n      const schedule = context.payload;\n      try {\n        const dialogData = {\n          title: 'Delete recurring task',\n          text: 'Are you sure you want to delete the selected recurring task?'\n        };\n        // Require user confirmation\n        const confirmed = yield awaitable(_this10._confirmationDialog.confirm(dialogData), context.cancellationToken);\n        if (!confirmed) {\n          return false;\n        }\n        const payload = {\n          workspaceId: schedule.workspaceId,\n          scheduleId: schedule.scheduleId\n        };\n        yield _this10._schedulerManager.deleteSchedule(new ActionPayloadContext(payload, context));\n        _this10._appInsights.trackEvent('ScheduleDeleted', {\n          workspaceId: schedule.workspaceId,\n          templateId: schedule.templateId,\n          datacenter: _this10._fieldResolver.getFieldForSchedule(schedule, 'DatacenterCode'),\n          recurrenceType: schedule.recurrencePattern.recurrenceType\n        });\n        const removedInstances = _this10.instances.filter(instance => schedule.scheduleId === instance.scheduleId).map(instance => instance.scheduleInstanceId);\n        // Make sure the schedule doesn't stay selected\n        _this10.store.deselectAllSchedules();\n        _this10.store.removeInstances(removedInstances);\n      } catch (error) {\n        if (!isActionCanceled(error)) {\n          _this10._notifications.addError('Failed to delete recurring task', error);\n        }\n        throw error; // Always re-throw errors\n      }\n      return true;\n    })();\n  }\n  /** Deletes all schedules selected by the user on the schedules page. */\n  deleteSelectedSchedules(context) {\n    var _this11 = this;\n    return _asyncToGenerator(function* () {\n      const deletedIds = [];\n      const {\n        workspaceId,\n        selectedScheduleIds\n      } = _this11.store.state;\n      try {\n        const dialogData = {\n          title: 'Delete selected recurring tasks',\n          text: 'Are you sure you want to delete the selected recurring tasks?'\n        };\n        // Require user confirmation\n        const confirmed = yield awaitable(_this11._confirmationDialog.confirm(dialogData), context.cancellationToken);\n        if (!confirmed) {\n          return;\n        }\n        _this11.store.deleteSelectedSchedulesStart();\n        const promises = [];\n        // Delete schedules in parallel\n        for (const scheduleId of selectedScheduleIds) {\n          const payload = {\n            workspaceId: workspaceId,\n            scheduleId: scheduleId\n          };\n          const promise = _this11._schedulerManager.deleteSchedule(new ActionPayloadContext(payload, context));\n          promises.push(promise);\n          deletedIds.push(scheduleId);\n        }\n        yield Promise.all(promises);\n        const removedInstanceIds = _this11.instances.filter(instance => deletedIds.includes(instance.scheduleId)).map(instance => instance.scheduleInstanceId);\n        _this11.store.removeInstances(removedInstanceIds);\n        _this11._appInsights.trackEvent('BulkSchedulesDeleted', {\n          workspaceId: workspaceId,\n          count: selectedScheduleIds.length.toString(),\n          page: 'list'\n        });\n        // Make sure the schedules don't stay selected\n        _this11.store.deselectAllSchedules();\n      } catch (error) {\n        if (!isActionCanceled(error)) {\n          _this11._notifications.addError('Failed to delete selected recurring tasks', error);\n        }\n        throw error; // Always re-throw errors\n      } finally {\n        _this11.store.deleteSelectedSchedulesEnd(deletedIds);\n      }\n    })();\n  }\n  /** Deletes all schedules selected by the user on the calendar page. */\n  deleteViewedSchedules(context) {\n    var _this12 = this;\n    return _asyncToGenerator(function* () {\n      const deletedIds = [];\n      const {\n        workspaceId,\n        viewScheduleIds\n      } = _this12.store.state;\n      try {\n        const dialogData = {\n          title: 'Delete selected recurring tasks',\n          text: 'Are you sure you want to delete the selected recurring tasks?'\n        };\n        // Require user confirmation\n        const confirmed = yield awaitable(_this12._confirmationDialog.confirm(dialogData), context.cancellationToken);\n        if (!confirmed) {\n          return;\n        }\n        _this12.store.deleteViewedSchedulesStart();\n        const promises = [];\n        // Delete schedules in parallel\n        for (const scheduleId of viewScheduleIds) {\n          const payload = {\n            workspaceId: workspaceId,\n            scheduleId: scheduleId\n          };\n          const promise = _this12._schedulerManager.deleteSchedule(new ActionPayloadContext(payload, context));\n          promises.push(promise);\n          deletedIds.push(scheduleId);\n        }\n        yield Promise.all(promises);\n        const removedInstanceIds = _this12.instances.filter(instance => deletedIds.includes(instance.scheduleId)).map(instance => instance.scheduleInstanceId);\n        _this12.store.removeInstances(removedInstanceIds);\n        _this12._appInsights.trackEvent('BulkSchedulesDeleted', {\n          workspaceId: workspaceId,\n          count: viewScheduleIds.length.toString(),\n          page: 'calendar'\n        });\n      } catch (error) {\n        if (!isActionCanceled(error)) {\n          _this12._notifications.addError('Failed to delete selected recurring tasks', error);\n        }\n        throw error; // Always re-throw errors\n      } finally {\n        _this12.store.deleteViewedSchedulesEnd(deletedIds);\n      }\n    })();\n  }\n  /**\r\n   * Deletes the given schedule template. This will fail if the template is currently used by any schedules.\r\n   * @param context.payload The schedule template to delete.\r\n   */\n  deleteTemplate(context) {\n    var _this13 = this;\n    return _asyncToGenerator(function* () {\n      const template = context.payload;\n      try {\n        const dialogData = {\n          title: 'Delete template',\n          text: 'Are you sure you want to delete the selected template?'\n        };\n        // Require user confirmation\n        const confirmed = yield awaitable(_this13._confirmationDialog.confirm(dialogData), context.cancellationToken);\n        if (!confirmed) {\n          return false;\n        }\n        const payload = {\n          workspaceId: template.workspaceId,\n          templateId: template.scheduleTemplateId\n        };\n        yield _this13._schedulerManager.deleteScheduleTemplate(new ActionPayloadContext(payload, context));\n        // Make sure the schedule template doesn't stay selected\n        _this13.store.deselectAllTemplates();\n      } catch (error) {\n        if (!isActionCanceled(error)) {\n          _this13._notifications.addError('Failed to delete template', error);\n        }\n        throw error; // Always re-throw errors\n      }\n      return true;\n    })();\n  }\n  /** Deletes all schedule templates selected by the user. */\n  deleteSelectedTemplates(context) {\n    var _this14 = this;\n    return _asyncToGenerator(function* () {\n      const deletedIds = [];\n      const {\n        workspaceId,\n        selectedTemplateIds\n      } = _this14.store.state;\n      try {\n        const dialogData = {\n          title: 'Delete selected templates',\n          text: 'Are you sure you want to delete the selected templates?'\n        };\n        // Require user confirmation\n        const confirmed = yield awaitable(_this14._confirmationDialog.confirm(dialogData), context.cancellationToken);\n        if (!confirmed) {\n          return;\n        }\n        _this14.store.deleteSelectedTemplatesStart();\n        const promises = [];\n        // Delete schedule templates in parallel\n        for (const templateId of selectedTemplateIds) {\n          const payload = {\n            workspaceId: workspaceId,\n            templateId: templateId\n          };\n          const promise = _this14._schedulerManager.deleteScheduleTemplate(new ActionPayloadContext(payload, context));\n          promises.push(promise);\n          deletedIds.push(templateId);\n        }\n        yield Promise.all(promises);\n        // Make sure the schedule templates don't stay selected\n        _this14.store.deselectAllTemplates();\n      } catch (error) {\n        if (!isActionCanceled(error)) {\n          _this14._notifications.addError('Failed to delete selected templates', error);\n        }\n        throw error; // Always re-throw errors\n      } finally {\n        _this14.store.deleteSelectedTemplatesEnd(deletedIds);\n      }\n    })();\n  }\n  /**\r\n   * Creates a new schedule.\r\n   * @param context.payload The user entered data for the schedule to create.\r\n   */\n  createNewRecurringTask(context) {\n    var _this15 = this;\n    return _asyncToGenerator(function* () {\n      const recurringTask = context.payload;\n      const {\n        workspaceId,\n        selectedFaultCode,\n        templateIds\n      } = _this15.store.state;\n      try {\n        const result = yield _this15._checkDayOfMonth(recurringTask);\n        if (!result) {\n          return false;\n        }\n        _this15.store.createNewRecurringTaskStart();\n        const templates = _this15._schedulerManager.templates.getEntities(templateIds);\n        const template = templates.find(t => _this15._faultCodeExistsInTemplate(t, selectedFaultCode));\n        const faultCode = _this15._domainDataManager.faultCodesEntityStore.get(selectedFaultCode.toString());\n        const schedule = {\n          ...new RecurringTaskConverter().convertBack(recurringTask, null, _this15.workspace.clientData?.useUtc),\n          workspaceId: workspaceId,\n          name: recurringTask.name || faultCode.description,\n          templateId: template.scheduleTemplateId,\n          clientData: {\n            faultCode: selectedFaultCode\n          }\n        };\n        // Temporarily hardcode the selected fault code as a parameter\n        if (template.parameterDefinitions.find(parameter => parameter.name === 'faultCode')) {\n          const predefinedParameters = schedule.templateParameterRetrievalSetting?.predefinedParameters;\n          if (predefinedParameters && predefinedParameters[0]) {\n            if (!predefinedParameters[0]['faultCode']) {\n              predefinedParameters[0]['faultCode'] = selectedFaultCode;\n            }\n          }\n        }\n        // Update executionTimeOffset if it was set in the template\n        if (template.clientData?.executionTimeOffsetInDays) {\n          schedule.recurrencePattern.executionTimeOffset = `${template.clientData.executionTimeOffsetInDays.toString()}.00:00:00`;\n        }\n        const newSchedule = yield _this15._schedulerManager.createSchedule(new ActionPayloadContext(schedule, context));\n        _this15._appInsights.trackEvent('ScheduleCreated', {\n          workspaceId: workspaceId,\n          templateId: template.scheduleTemplateId,\n          datacenter: _this15._fieldResolver.getFieldForSchedule(newSchedule, 'DatacenterCode'),\n          selectedFaultCode: selectedFaultCode?.toString(),\n          recurrenceType: schedule.recurrencePattern.recurrenceType,\n          maxNumOfOccurrences: schedule.recurrencePattern.maxNumOfOccurrences > 0 ? schedule.recurrencePattern.maxNumOfOccurrences.toString() : undefined,\n          daysOfWeek: JSON.stringify(schedule.recurrencePattern.daysOfWeek)\n        });\n        _this15.store.addSchedule(newSchedule.scheduleId);\n      } catch (error) {\n        if (!isActionCanceled(error)) {\n          _this15._notifications.addError('Failed to create recurring task', error);\n        }\n        throw error; // Always re-throw errors\n      } finally {\n        _this15.store.createNewRecurringTaskEnd();\n      }\n      return true;\n    })();\n  }\n  /**\r\n   * Updates an existing schedule.\r\n   * @param context.payload The user entered data to update the schedule from.\r\n   */\n  updateRecurringTask(context) {\n    var _this16 = this;\n    return _asyncToGenerator(function* () {\n      const recurringTask = context.payload;\n      const {\n        workspaceId,\n        editScheduleId,\n        selectedFaultCode\n      } = _this16.store.state;\n      try {\n        const result = yield _this16._checkDayOfMonth(recurringTask);\n        if (!result) {\n          return false;\n        }\n        _this16.store.updateRecurringTaskStart();\n        const payload = {\n          workspaceId: workspaceId,\n          scheduleId: editScheduleId\n        };\n        const existingSchedule = yield _this16._schedulerManager.getScheduleById(new ActionPayloadContext(payload, context));\n        const template = _this16._schedulerManager.templates.get(existingSchedule.templateId);\n        const schedule = new RecurringTaskConverter().convertBack(recurringTask, existingSchedule, _this16.workspace.clientData?.useUtc);\n        schedule.clientData = {\n          ...existingSchedule.clientData\n        };\n        // Temporarily hardcode the selected fault code as a parameter\n        if (template.parameterDefinitions.find(parameter => parameter.name === 'faultCode')) {\n          const predefinedParameters = schedule.templateParameterRetrievalSetting?.predefinedParameters;\n          if (predefinedParameters && predefinedParameters[0]) {\n            if (!predefinedParameters[0]['faultCode']) {\n              predefinedParameters[0]['faultCode'] = selectedFaultCode;\n            }\n          }\n        }\n        // Update executionTimeOffset if it was set in the template\n        if (template.clientData?.executionTimeOffsetInDays) {\n          schedule.recurrencePattern.executionTimeOffset = `${template.clientData.executionTimeOffsetInDays.toString()}.00:00:00`;\n        }\n        yield _this16._schedulerManager.updateSchedule(new ActionPayloadContext(schedule, context));\n        _this16._appInsights.trackEvent('ScheduleUpdated', {\n          workspaceId: workspaceId,\n          templateId: template.scheduleTemplateId,\n          datacenter: _this16._fieldResolver.getFieldForSchedule(schedule, 'DatacenterCode'),\n          recurrenceType: schedule.recurrencePattern.recurrenceType\n        });\n        if (_this16.store.state.viewScheduleIds.includes(schedule.scheduleId)) {\n          yield _this16.loadScheduleInstances(new ActionPayloadContext(_this16.store.state.viewScheduleIds, context));\n        }\n      } catch (error) {\n        if (!isActionCanceled(error)) {\n          _this16._notifications.addError('Failed to update scheduled task', error);\n        }\n        throw error; // Always re-throw errors\n      } finally {\n        _this16.store.updateRecurringTaskEnd();\n      }\n      return true;\n    })();\n  }\n  /**\r\n   * Creates a new schedule template from the given user input.\r\n   * @returns The id of the newly created template.\r\n   */\n  createScheduleTemplate(context) {\n    var _this17 = this;\n    return _asyncToGenerator(function* () {\n      const form = context.payload;\n      const {\n        workspaceId\n      } = _this17.store.state;\n      try {\n        _this17.templateDetailsStore.createScheduleTemplateStart();\n        const scheduleTemplate = new TemplateFormConverter().convertBack(form, workspaceId);\n        if (scheduleTemplate.parameterDefinitions) {\n          const faultCodeExists = scheduleTemplate.parameterDefinitions.find(param => param.name === 'faultCode');\n          if (!faultCodeExists) {\n            scheduleTemplate.parameterDefinitions.push({\n              name: 'faultCode',\n              dataType: ScheduleTemplateParameterDataType.Number\n            });\n          }\n        }\n        const newTemplate = yield _this17._schedulerManager.createScheduleTemplate(new ActionPayloadContext(scheduleTemplate, context));\n        _this17.store.addTemplate(newTemplate.scheduleTemplateId);\n        return newTemplate.scheduleTemplateId;\n      } catch (error) {\n        if (!isActionCanceled(error)) {\n          _this17._notifications.addError('Failed to create template', error);\n        }\n        throw error; // Always re-throw errors\n      } finally {\n        _this17.templateDetailsStore.createScheduleTemplateEnd();\n      }\n    })();\n  }\n  /** Previews the creation of a task with an unsaved schedule template and given parameters. */\n  previewUnsavedTemplate(context) {\n    var _this18 = this;\n    return _asyncToGenerator(function* () {\n      const {\n        template,\n        parameters\n      } = context.payload;\n      let task;\n      try {\n        _this18.templateDetailsStore.previewUnsavedTemplateStart();\n        if (template.parameterDefinitions) {\n          const faultCodeExists = template.parameterDefinitions.find(param => param.name === 'faultCode');\n          if (!faultCodeExists) {\n            template.parameterDefinitions.push({\n              name: 'faultCode',\n              dataType: ScheduleTemplateParameterDataType.Number\n            });\n          }\n        }\n        const payload = {\n          template: template,\n          parameters: parameters\n        };\n        task = yield _this18._schedulerManager.previewUnsavedScheduleTemplate(new ActionPayloadContext(payload, context));\n      } catch (error) {\n        if (!isActionCanceled(error)) {\n          _this18._notifications.addError('Failed to preview template', error);\n        }\n        throw error; // Always re-throw errors\n      } finally {\n        _this18.templateDetailsStore.previewUnsavedTemplateEnd(task);\n      }\n    })();\n  }\n  /**\r\n   * Copies a template from a different workspace or environment.\r\n   * @param context.payload The existing template to copy into the current workspace.\r\n   */\n  copyExistingTemplate(context) {\n    var _this19 = this;\n    return _asyncToGenerator(function* () {\n      const template = context.payload;\n      const {\n        workspaceId\n      } = _this19.store.state;\n      try {\n        _this19.store.createNewTemplateStart();\n        template.workspaceId = workspaceId;\n        const newTemplate = yield _this19._schedulerManager.createScheduleTemplate(new ActionPayloadContext(template, context));\n        _this19.store.addTemplate(newTemplate.scheduleTemplateId);\n      } catch (error) {\n        if (!isActionCanceled(error)) {\n          _this19._notifications.addError('Failed to create template', error);\n        }\n        throw error; // Always re-throw errors\n      } finally {\n        _this19.store.createNewTemplateEnd();\n      }\n    })();\n  }\n  /**\r\n   * Updates an existing template.\r\n   * @param context.payload The user entered data to update the template from.\r\n   */\n  updateScheduleTemplate(context) {\n    var _this20 = this;\n    return _asyncToGenerator(function* () {\n      const template = context.payload;\n      try {\n        _this20.templateDetailsStore.createScheduleTemplateStart();\n        const scheduleTemplate = new TemplateFormConverter().convertBack(template, _this20.store.state.workspaceId, _this20.templateDetailsStore.state.templateId);\n        if (scheduleTemplate.parameterDefinitions) {\n          const faultCodeExists = scheduleTemplate.parameterDefinitions.find(param => param.name === 'faultCode');\n          if (!faultCodeExists) {\n            scheduleTemplate.parameterDefinitions.push({\n              name: 'faultCode',\n              dataType: ScheduleTemplateParameterDataType.Number\n            });\n          }\n        }\n        yield _this20._schedulerManager.updateScheduleTemplate(new ActionPayloadContext(scheduleTemplate, context));\n        _this20.templateDetailsStore.standardView();\n      } catch (error) {\n        if (!isActionCanceled(error)) {\n          _this20._notifications.addError('Failed to save template', error);\n        }\n        throw error; // Always re-throw errors\n      } finally {\n        _this20.templateDetailsStore.createScheduleTemplateEnd();\n      }\n    })();\n  }\n  /**\r\n   * Begins editing the given schedule.\r\n   * @param schedule The schedule to edit.\r\n   */\n  editRecurringTask(schedule) {\n    const recurringTask = new RecurringTaskConverter().convert(schedule);\n    this.store.openEditRecurringTaskDialog(recurringTask, schedule);\n  }\n  /**\r\n   * Begins duplicating the given schedule.\r\n   * @param schedule The schedule to duplicate.\r\n   */\n  duplicateRecurringTask(schedule) {\n    const recurringTask = new RecurringTaskConverter().convert(schedule);\n    this.store.openDuplicateRecurringTaskDialog(recurringTask, schedule);\n  }\n  /**\r\n   * Views the details of the given schedule instance and loads all tasks created by the instance.\r\n   * @param context.payload The instance details to view.\r\n   */\n  viewScheduleInstance(context) {\n    var _this21 = this;\n    return _asyncToGenerator(function* () {\n      const scheduleInstance = context.payload;\n      try {\n        _this21.store.openScheduleInstanceDialog(scheduleInstance.scheduleInstanceId);\n        let taskIds = [];\n        if (scheduleInstance.state === ScheduleInstanceState.Succeeded || scheduleInstance.state === ScheduleInstanceState.PartiallySucceeded) {\n          const queryBuilder = new GdcoQueryBuilder().startWith('CreatedByScheduleWorkspaceId', GdcoQueryOperator.equals, scheduleInstance.workspaceId).and('CreatedByScheduleId', GdcoQueryOperator.equals, scheduleInstance.scheduleId).and('CreatedByScheduleInstanceId', GdcoQueryOperator.equals, scheduleInstance.scheduleInstanceId);\n          const payload = {\n            query: queryBuilder,\n            skip: 0\n          };\n          const tasks = yield _this21._tasksManager.searchTasks(new ActionPayloadContext(payload, context));\n          taskIds = tasks.results.map(task => task.Id);\n        }\n        _this21.store.setSelectedScheduleInstanceTaskIds(taskIds);\n      } catch (error) {\n        if (!isActionCanceled(error)) {\n          _this21._notifications.addError('Failed to load scheduled task details', error);\n        }\n        throw error;\n      }\n    })();\n  }\n  /**\r\n   * Updates the columns displayed in a workspace.\r\n   * @param context.payload The new set of columns that should be displayed.\r\n   */\n  updateWorkspaceColumnOptions(context) {\n    var _this22 = this;\n    return _asyncToGenerator(function* () {\n      const options = context.payload;\n      try {\n        const confirmationData = {\n          title: 'Are you sure?',\n          text: 'These changes apply to all users of the current workspace. Are you sure you want to make these changes?'\n        };\n        // Require user confirmation\n        const confirmed = yield awaitable(_this22._confirmationDialog.confirm(confirmationData), context.cancellationToken);\n        if (!confirmed) {\n          return;\n        }\n        _this22.store.updateWorkspaceColumnOptionsStart();\n        const updatedWorkspace = {\n          ..._this22.workspace,\n          clientData: {\n            ..._this22.workspace.clientData,\n            columns: options\n          }\n        };\n        yield _this22._schedulerManager.updateWorkspace(new ActionPayloadContext(updatedWorkspace, context));\n        _this22.store.closeColumnOptionsDialog();\n      } catch (error) {\n        if (!isActionCanceled(error)) {\n          _this22._notifications.addError('Failed to save new column options', error);\n        }\n        throw error;\n      } finally {\n        _this22.store.updateWorkspaceColumnOptionsEnd();\n      }\n    })();\n  }\n  _faultCodeExistsInTemplate(template, faultCode) {\n    if (!template || !template.clientData) {\n      return false;\n    }\n    return template.clientData.faultCodes?.includes(faultCode);\n  }\n  _getCampusesFromSchedules(schedules) {\n    if (!schedules) {\n      return [];\n    }\n    return schedules.map(schedule => this._fieldResolver.getFieldForSchedule(schedule, 'FacilityCampus')).map(campus => this._getCampusName(campus)).filter((value, index, arr) => arr.indexOf(value) === index).sort();\n  }\n  _filterSchedules(schedules, selectedCampus, userId, showCompletedSchedules, searchTerm) {\n    if (!schedules) {\n      return [];\n    }\n    let filteredSchedules = schedules;\n    if (selectedCampus) {\n      filteredSchedules = filteredSchedules.filter(schedule => {\n        const campus = this._fieldResolver.getFieldForSchedule(schedule, 'FacilityCampus');\n        return campus === selectedCampus;\n      });\n    }\n    if (userId) {\n      filteredSchedules = filteredSchedules.filter(schedule => schedule.createdBy === userId);\n    }\n    if (!showCompletedSchedules) {\n      filteredSchedules = filteredSchedules.filter(schedule => schedule.state === ScheduleState.Active);\n    }\n    if (searchTerm && searchTerm.trim()) {\n      const searchTermLower = searchTerm.trim().toLowerCase();\n      filteredSchedules = filteredSchedules.filter(schedule => {\n        this._schedulerMatched(searchTermLower, schedule);\n        //   if (schedule.name?.toLowerCase().includes(searchTermLower)) {\n        //     return true;\n        //   }\n        //   const facilityCampus = this._fieldResolver.getFieldForSchedule(schedule, 'FacilityCampus');\n        //   if (facilityCampus?.toLowerCase().includes(searchTermLower)) {\n        //     return true;\n        //   }\n        //   const datacenterCode = this._fieldResolver.getFieldForSchedule(schedule, 'DatacenterCode');\n        //   if (datacenterCode?.toLowerCase().includes(searchTermLower)) {\n        //     return true;\n        //   }\n        //   const facilityTimeZone = this._fieldResolver.getFieldForSchedule(schedule, 'FacilityTimeZone');\n        //   if (facilityTimeZone?.toLowerCase().includes(searchTermLower)) {\n        //     return true;\n        //   }\n        //   const description = this._fieldResolver.getFieldForSchedule(schedule, 'Description');\n        //   if (description?.toLowerCase().includes(searchTermLower)) {\n        //     return true;\n        //   }\n        //  const faultCode = this._fieldResolver.getFieldForSchedule(schedule, 'FaultCode');\n        //   if (faultCode?.toString().includes(searchTermLower)) {\n        //     return true;\n        //   }\n        //   return false;\n      });\n    }\n    return filteredSchedules;\n  }\n  _schedulerMatched(searchTerm, schedule) {\n    const searchTermLower = searchTerm.trim().toLowerCase();\n    return Object.values(schedule).some(value => {\n      if (typeof value === 'string') {\n        return value.toLowerCase().includes(searchTermLower);\n      } else if (typeof value === 'number') {\n        return value.toString().includes(searchTermLower);\n      }\n      return false;\n    });\n  }\n  _checkDayOfMonth(recurringTask) {\n    var _this23 = this;\n    return _asyncToGenerator(function* () {\n      if (recurringTask.frequency.frequencyType === FrequencyType.Monthly) {\n        if (recurringTask.frequency.recurrence.day > 28) {\n          const confirmationData = {\n            title: 'Notice',\n            text: `Some months have fewer than ${recurringTask.frequency.recurrence.day} days. For these months, the occurrence will fall on the last day of the month.`,\n            width: 500,\n            acceptButtonText: 'OK',\n            rejectButtonText: 'Cancel'\n          };\n          return yield awaitable(_this23._confirmationDialog.confirm(confirmationData), null);\n        }\n      }\n      return true;\n    })();\n  }\n  _getCampusName(campus) {\n    return campus || 'Unknown';\n  }\n  _createTemplateVersionKey(templateId, version) {\n    if (!templateId || !version) {\n      return null;\n    }\n    return `${templateId}:${version}`;\n  }\n}\nWorkspacePageViewModel.ɵfac = function WorkspacePageViewModel_Factory(t) {\n  return new (t || WorkspacePageViewModel)(i0.ɵɵinject(i1.WorkspacePageStore), i0.ɵɵinject(i2.TemplateDetailsStore), i0.ɵɵinject(i3.GdcoAuth), i0.ɵɵinject(i4.GdcoConfirmationDialog), i0.ɵɵinject(i4.GdcoNotifications), i0.ɵɵinject(i4.GdcoAppInsights), i0.ɵɵinject(i5.SchedulerManager), i0.ɵɵinject(i6.DomainDataManager), i0.ɵɵinject(i6.DatacentersManager), i0.ɵɵinject(i6.UsersManager), i0.ɵɵinject(i6.TasksManager), i0.ɵɵinject(i7.RecurringTaskFieldResolver));\n};\nWorkspacePageViewModel.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n  token: WorkspacePageViewModel,\n  factory: WorkspacePageViewModel.ɵfac,\n  providedIn: 'root'\n});\n__decorate([Action({\n  type: ActionType.CancelInProgress\n}), __metadata(\"design:type\", Function), __metadata(\"design:paramtypes\", [ActionPayloadContext]), __metadata(\"design:returntype\", Promise)], WorkspacePageViewModel.prototype, \"initializePage\", null);\n__decorate([Action({\n  type: ActionType.CancelInProgress\n}), __metadata(\"design:type\", Function), __metadata(\"design:paramtypes\", [ActionPayloadContext]), __metadata(\"design:returntype\", Promise)], WorkspacePageViewModel.prototype, \"initializeScheduleInstancesPageByScheduleId\", null);\n__decorate([Action({\n  type: ActionType.ReuseInProgress\n}), __metadata(\"design:type\", Function), __metadata(\"design:paramtypes\", [ActionPayloadContext]), __metadata(\"design:returntype\", Promise)], WorkspacePageViewModel.prototype, \"loadWorkspace\", null);\n__decorate([Action({\n  type: ActionType.ReuseInProgress\n}), __metadata(\"design:type\", Function), __metadata(\"design:paramtypes\", [ActionContext]), __metadata(\"design:returntype\", Promise)], WorkspacePageViewModel.prototype, \"loadSchedules\", null);\n__decorate([Action({\n  type: ActionType.ReuseInProgress\n}), __metadata(\"design:type\", Function), __metadata(\"design:paramtypes\", [ActionContext]), __metadata(\"design:returntype\", Promise)], WorkspacePageViewModel.prototype, \"loadTemplates\", null);\n__decorate([Action(), __metadata(\"design:type\", Function), __metadata(\"design:paramtypes\", [ActionPayloadContext]), __metadata(\"design:returntype\", Promise)], WorkspacePageViewModel.prototype, \"loadTemplateVersions\", null);\n__decorate([Action({\n  type: ActionType.CancelInProgress\n}), __metadata(\"design:type\", Function), __metadata(\"design:paramtypes\", [ActionPayloadContext]), __metadata(\"design:returntype\", Promise)], WorkspacePageViewModel.prototype, \"loadScheduleInstances\", null);\n__decorate([Action({\n  type: ActionType.ReuseInProgress\n}), __metadata(\"design:type\", Function), __metadata(\"design:paramtypes\", [ActionPayloadContext]), __metadata(\"design:returntype\", Promise)], WorkspacePageViewModel.prototype, \"loadScheduleInstancesPage\", null);\n__decorate([Action({\n  type: ActionType.ReuseInProgress\n}), __metadata(\"design:type\", Function), __metadata(\"design:paramtypes\", [ActionContext]), __metadata(\"design:returntype\", Promise)], WorkspacePageViewModel.prototype, \"loadMetadata\", null);\n__decorate([Action(), __metadata(\"design:type\", Function), __metadata(\"design:paramtypes\", [ActionPayloadContext]), __metadata(\"design:returntype\", Promise)], WorkspacePageViewModel.prototype, \"deleteSchedule\", null);\n__decorate([Action(), __metadata(\"design:type\", Function), __metadata(\"design:paramtypes\", [ActionContext]), __metadata(\"design:returntype\", Promise)], WorkspacePageViewModel.prototype, \"deleteSelectedSchedules\", null);\n__decorate([Action(), __metadata(\"design:type\", Function), __metadata(\"design:paramtypes\", [ActionContext]), __metadata(\"design:returntype\", Promise)], WorkspacePageViewModel.prototype, \"deleteViewedSchedules\", null);\n__decorate([Action(), __metadata(\"design:type\", Function), __metadata(\"design:paramtypes\", [ActionPayloadContext]), __metadata(\"design:returntype\", Promise)], WorkspacePageViewModel.prototype, \"deleteTemplate\", null);\n__decorate([Action(), __metadata(\"design:type\", Function), __metadata(\"design:paramtypes\", [ActionContext]), __metadata(\"design:returntype\", Promise)], WorkspacePageViewModel.prototype, \"deleteSelectedTemplates\", null);\n__decorate([Action(), __metadata(\"design:type\", Function), __metadata(\"design:paramtypes\", [ActionPayloadContext]), __metadata(\"design:returntype\", Promise)], WorkspacePageViewModel.prototype, \"createNewRecurringTask\", null);\n__decorate([Action(), __metadata(\"design:type\", Function), __metadata(\"design:paramtypes\", [ActionPayloadContext]), __metadata(\"design:returntype\", Promise)], WorkspacePageViewModel.prototype, \"updateRecurringTask\", null);\n__decorate([Action(), __metadata(\"design:type\", Function), __metadata(\"design:paramtypes\", [ActionPayloadContext]), __metadata(\"design:returntype\", Promise)], WorkspacePageViewModel.prototype, \"createScheduleTemplate\", null);\n__decorate([Action(), __metadata(\"design:type\", Function), __metadata(\"design:paramtypes\", [ActionPayloadContext]), __metadata(\"design:returntype\", Promise)], WorkspacePageViewModel.prototype, \"previewUnsavedTemplate\", null);\n__decorate([Action(), __metadata(\"design:type\", Function), __metadata(\"design:paramtypes\", [ActionPayloadContext]), __metadata(\"design:returntype\", Promise)], WorkspacePageViewModel.prototype, \"copyExistingTemplate\", null);\n__decorate([Action(), __metadata(\"design:type\", Function), __metadata(\"design:paramtypes\", [ActionPayloadContext]), __metadata(\"design:returntype\", Promise)], WorkspacePageViewModel.prototype, \"updateScheduleTemplate\", null);\n__decorate([Action({\n  type: ActionType.CancelInProgress\n}), __metadata(\"design:type\", Function), __metadata(\"design:paramtypes\", [ActionPayloadContext]), __metadata(\"design:returntype\", Promise)], WorkspacePageViewModel.prototype, \"viewScheduleInstance\", null);\n__decorate([Action(), __metadata(\"design:type\", Function), __metadata(\"design:paramtypes\", [ActionPayloadContext]), __metadata(\"design:returntype\", Promise)], WorkspacePageViewModel.prototype, \"updateWorkspaceColumnOptions\", null);", "map": {"version": 3, "names": ["FrequencyType", "RecurringTaskConverter", "TemplateFormConverter", "TemplateDetailsStore", "WorkspacePageStore", "GdcoAuth", "GdcoQueryBuilder", "GdcoQueryOperator", "GdcoNotifications", "GdcoConfirmationDialog", "GdcoAppInsights", "gdcoUtils", "DatacentersManager", "DomainDataManager", "TasksManager", "UsersManager", "ScheduleInstanceState", "ScheduleState", "ScheduleTemplateParameterDataType", "Scheduler<PERSON>anager", "Action", "isActionCanceled", "ActionPayloadContext", "ActionType", "ActionContext", "awaitable", "filterItems", "sortItems", "mapItems", "distictItems", "groupItemsBy", "RecurringTaskFieldResolver", "combineLatest", "map", "WorkspacePageViewModel", "workspace", "_scheduler<PERSON><PERSON>ger", "workspaces", "get", "store", "state", "workspaceId", "schedules", "getEntities", "scheduleIds", "templates", "templateIds", "template", "templateDetailsStore", "templateId", "instances", "scheduleInstanceIds", "constructor", "_auth", "_confirmationDialog", "_notifications", "_appInsights", "_domainDataManager", "_datacentersManager", "_usersManager", "_tasksManager", "_fieldResolver", "workspace$", "observe", "workspaceId$", "schedules$", "observeEntities", "scheduleIds$", "activeSchedules$", "pipe", "schedule", "Active", "hasCompletedSchedules$", "find", "Complete", "filteredSchedules$", "selectedCampus$", "selectedUserId$", "showCompletedSchedules$", "searchTerm$", "selectedCampus", "userId", "showCompletedSchedules", "searchTerm", "_filterSchedules", "mySchedules$", "created<PERSON>y", "currentUser", "a", "b", "aDatacenter", "getFieldForSchedule", "bDatacenter", "toLowerCase", "localeCompare", "name", "template$", "templateId$", "templates$", "templateIds$", "selectedSchedules$", "selectedScheduleIds$", "selectedTemplates$", "selectedTemplateIds$", "diffOriginalTemplate$", "templateVersions", "diffOriginalVersion$", "version", "_createTemplateVersionKey", "diffModifiedTemplate$", "diffModifiedVersion$", "viewSchedules$", "viewScheduleIds$", "scheduleInstances$", "scheduleInstanceIds$", "selectedScheduleInstance$", "selectedScheduleInstanceId$", "selectedScheduleInstanceSchedule$", "instance", "scheduleId", "selectedScheduleInstanceTasks$", "tasks", "selectedScheduleInstanceTaskIds$", "selectedTemplate$", "selectedFaultCode$", "faultCode", "_faultCodeExistsInTemplate", "faultCodes", "toString", "scheduleCampuses$", "_getCampusesFromSchedules", "filteredScheduleCampuses$", "createdByUserIds", "scheduleCreatedByUsers$", "users", "DisplayName", "faultCodes$", "observeAll", "metadata$", "faultCodeMap$", "templateFaultCodes$", "filter", "code", "clientData", "disableUiCreation", "sort", "description", "schedulesByCampus$", "campuses", "reduce", "prev", "curr", "campus", "_getCampusName", "schedulesById$", "editSchedule$", "editScheduleId$", "editScheduleTemplate$", "duplicateSchedule$", "duplicateScheduleId$", "duplicateScheduleTemplate$", "scheduleFieldMap$", "FacilityCampus", "DatacenterCode", "Colocation", "Group", "AssignedTo", "FacilityTimeZone", "initializePage", "context", "_this", "_asyncToGenerator", "payload", "initializePageStart", "loadWorkspace", "Promise", "all", "loadSchedules", "loadTemplates", "loadMetadata", "initializeScheduleInstancesPageByScheduleId", "_this2", "clearViewedSchedules", "loadScheduleInstances", "_this3", "loadWorkspaceStart", "getWorkspaceById", "error", "addError", "loadWorkspaceEnd", "_this4", "loadSchedulesStart", "listSchedulesByWorkspaceId", "loadDatacenters", "loadSchedulesEnd", "_this5", "loadTemplatesStart", "listScheduleTemplatesByWorkspaceId", "scheduleTemplateId", "loadTemplatesEnd", "loadTemplateVersions", "_this6", "listScheduleTemplateVersionsById", "diffTemplateView", "_this7", "viewDate", "loadScheduleInstancesStart", "promises", "promise", "listScheduleInstancesByScheduleId", "push", "startDate", "recurrencePattern", "endDate", "getFullYear", "paginPayload", "pageDate", "pagingPromise", "listScheduleInstancesPageByScheduleId", "results", "concat", "scheduleInstanceId", "distinct", "loadScheduleInstancesEnd", "loadScheduleInstancesPage", "_this8", "viewScheduleIds", "loadScheduleInstancesPageEnd", "_this9", "userIds", "loadTaskMetadata", "loadFaultCodes", "getUsersByIds", "deleteSchedule", "_this10", "dialogData", "title", "text", "confirmed", "confirm", "cancellationToken", "trackEvent", "datacenter", "recurrenceType", "removedInstances", "deselectAllSchedules", "removeInstances", "deleteSelectedSchedules", "_this11", "deletedIds", "selectedScheduleIds", "deleteSelectedSchedulesStart", "removedInstanceIds", "includes", "count", "length", "page", "deleteSelectedSchedulesEnd", "deleteViewedSchedules", "_this12", "deleteViewedSchedulesStart", "deleteViewedSchedulesEnd", "deleteTemplate", "_this13", "deleteScheduleTemplate", "deselectAllTemplates", "deleteSelectedTemplates", "_this14", "selectedTemplateIds", "deleteSelectedTemplatesStart", "deleteSelectedTemplatesEnd", "createNewRecurringTask", "_this15", "recurringTask", "selectedFaultCode", "result", "_checkDayOfMonth", "createNewRecurringTaskStart", "t", "faultCodesEntityStore", "convertBack", "useUtc", "parameterDefinitions", "parameter", "predefinedParameters", "templateParameterRetrievalSetting", "executionTimeOffsetInDays", "executionTimeOffset", "newSchedule", "createSchedule", "maxNumOfOccurrences", "undefined", "daysOfWeek", "JSON", "stringify", "addSchedule", "createNewRecurringTaskEnd", "updateRecurringTask", "_this16", "editScheduleId", "updateRecurringTaskStart", "existingSchedule", "getScheduleById", "updateSchedule", "updateRecurringTaskEnd", "createScheduleTemplate", "_this17", "form", "createScheduleTemplateStart", "scheduleTemplate", "faultCodeExists", "param", "dataType", "Number", "newTemplate", "addTemplate", "createScheduleTemplateEnd", "previewUnsavedTemplate", "_this18", "parameters", "task", "previewUnsavedTemplateStart", "previewUnsavedScheduleTemplate", "previewUnsavedTemplateEnd", "copyExistingTemplate", "_this19", "createNewTemplateStart", "createNewTemplateEnd", "updateScheduleTemplate", "_this20", "standardView", "editRecurringTask", "convert", "openEditRecurringTaskDialog", "duplicateRecurringTask", "openDuplicateRecurringTaskDialog", "viewScheduleInstance", "_this21", "scheduleInstance", "openScheduleInstanceDialog", "taskIds", "Succeeded", "PartiallySucceeded", "queryBuilder", "startWith", "equals", "and", "query", "skip", "searchTasks", "Id", "setSelectedScheduleInstanceTaskIds", "updateWorkspaceColumnOptions", "_this22", "options", "confirmationData", "updateWorkspaceColumnOptionsStart", "updatedWorkspace", "columns", "updateWorkspace", "closeColumnOptionsDialog", "updateWorkspaceColumnOptionsEnd", "value", "index", "arr", "indexOf", "filteredSchedules", "trim", "searchTermLower", "_schedulerMatched", "Object", "values", "some", "_this23", "frequency", "frequencyType", "Monthly", "recurrence", "day", "width", "acceptButtonText", "rejectButtonText", "i0", "ɵɵinject", "i1", "i2", "i3", "i4", "i5", "i6", "i7", "factory", "ɵfac", "providedIn", "__decorate", "type", "CancelInProgress", "ReuseInProgress"], "sources": ["D:\\Repo\\GDCO\\MCIO-GDCO-AppService\\src\\GDCOClient\\GdcoApp\\projects\\applications\\tasks-scheduler\\src\\app\\pages\\workspace\\workspace-page.view-model.ts"], "sourcesContent": ["/**\r\n * @license\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n */\r\n\r\nimport { FrequencyType, RecurringTask, RecurringTaskConverter, PreviewTemplate } from '../../common';\r\nimport { TemplateForm, TemplateFormConverter } from './template-details/edit';\r\nimport { TemplateDetailsStore } from './template-details/template-details.store';\r\nimport { WorkspacePageStore } from './workspace-page.store';\r\nimport { Injectable } from '@angular/core';\r\nimport { GdcoAuth } from '@gdco/auth';\r\nimport { GdcoQueryBuilder, GdcoQueryOperator } from '@gdco/common';\r\nimport {\r\n  GdcoNotifications,\r\n  GdcoConfirmationDialog,\r\n  GdcoConfirmationData,\r\n  GdcoAppInsights,\r\n  gdcoUtils\r\n} from '@gdco/core';\r\nimport {\r\n  AzureGraphObject,\r\n  DatacentersManager,\r\n  DomainDataManager,\r\n  FaultCode,\r\n  Metadata,\r\n  SearchTasksPayload,\r\n  Task,\r\n  TasksManager,\r\n  UsersManager\r\n} from '@gdco/core-reference-systems/gdco-service';\r\nimport {\r\n  DeleteSchedulePayload,\r\n  DeleteScheduleTemplatePayload,\r\n  GetScheduleByIdPayload,\r\n  ListScheduleInstancesByScheduleIdPayload,\r\n  ListScheduleInstancesPageByScheduleIdPayload,\r\n  ListScheduleTemplateVersionsByIdPayload,\r\n  ListSchedulesByWorkspaceIdPayload,\r\n  PreviewUnsavedScheduleTemplatePayload,\r\n  Schedule,\r\n  ScheduleInstance,\r\n  ScheduleInstanceState,\r\n  ScheduleState,\r\n  ScheduleTemplate,\r\n  ScheduleTemplateParameterDataType,\r\n  ScheduleWorkspace,\r\n  SchedulerManager\r\n} from '@gdco/reference-systems/gdco-service';\r\nimport {\r\n  Action,\r\n  isActionCanceled,\r\n  ActionPayloadContext,\r\n  ActionType,\r\n  ActionContext,\r\n  awaitable,\r\n  filterItems,\r\n  sortItems,\r\n  mapItems,\r\n  distictItems,\r\n  groupItemsBy\r\n} from '@gdco/store';\r\nimport { RecurringTaskFieldResolver } from '@tasks/scheduling';\r\nimport { Observable, combineLatest } from 'rxjs';\r\nimport { map } from 'rxjs/operators';\r\n\r\n/**\r\n * View model for the workspace page. All events and/or logic in the workspace page component should be delegated here instead.\r\n */\r\n@Injectable({ providedIn: 'root' })\r\nexport class WorkspacePageViewModel {\r\n  readonly workspace$: Observable<ScheduleWorkspace>;\r\n  readonly schedules$: Observable<Schedule[]>;\r\n  readonly activeSchedules$: Observable<Schedule[]>;\r\n  readonly mySchedules$: Observable<Schedule[]>;\r\n  readonly templates$: Observable<ScheduleTemplate[]>;\r\n  readonly template$: Observable<ScheduleTemplate>;\r\n  readonly diffModifiedTemplate$: Observable<ScheduleTemplate>;\r\n  readonly diffOriginalTemplate$: Observable<ScheduleTemplate>;\r\n  readonly selectedSchedules$: Observable<Schedule[]>;\r\n  readonly selectedTemplates$: Observable<ScheduleTemplate[]>;\r\n  readonly selectedTemplate$: Observable<ScheduleTemplate>;\r\n  readonly selectedFaultCode$: Observable<FaultCode>;\r\n  readonly filteredSchedules$: Observable<Schedule[]>;\r\n  readonly scheduleCampuses$: Observable<string[]>;\r\n  readonly filteredScheduleCampuses$: Observable<string[]>;\r\n  readonly scheduleCreatedByUsers$: Observable<AzureGraphObject[]>;\r\n  readonly faultCodes$: Observable<FaultCode[]>;\r\n  readonly faultCodeMap$: Observable<{ [code: number]: FaultCode }>;\r\n  readonly templateFaultCodes$: Observable<FaultCode[]>;\r\n  readonly metadata$: Observable<Metadata[]>;\r\n  readonly viewSchedules$: Observable<Schedule[]>;\r\n  readonly scheduleInstances$: Observable<ScheduleInstance[]>;\r\n  readonly selectedScheduleInstance$: Observable<ScheduleInstance>;\r\n  readonly selectedScheduleInstanceSchedule$: Observable<Schedule>;\r\n  readonly selectedScheduleInstanceTasks$: Observable<Task[]>;\r\n  readonly schedulesByCampus$: Observable<{ [name: string]: Schedule[] }>;\r\n  readonly schedulesById$: Observable<{ [id: string]: Schedule }>;\r\n  readonly hasCompletedSchedules$: Observable<boolean>;\r\n  readonly editSchedule$: Observable<Schedule>;\r\n  readonly duplicateSchedule$: Observable<Schedule>;\r\n  readonly editScheduleTemplate$: Observable<ScheduleTemplate>;\r\n  readonly duplicateScheduleTemplate$: Observable<ScheduleTemplate>;\r\n  readonly scheduleFieldMap$: Observable<{ [id: string]: { [name: string]: any } }>;\r\n\r\n  /** Gets the current workspace. */\r\n  get workspace(): ScheduleWorkspace {\r\n    return this._schedulerManager.workspaces.get(this.store.state.workspaceId);\r\n  }\r\n\r\n  /** Gets all schedules in the current workspace. */\r\n  get schedules(): Schedule[] {\r\n    return this._schedulerManager.schedules.getEntities(this.store.state.scheduleIds);\r\n  }\r\n\r\n  /** Gets all templates in the current workspace. */\r\n  get templates(): ScheduleTemplate[] {\r\n    return this._schedulerManager.templates.getEntities(this.store.state.templateIds);\r\n  }\r\n\r\n  get template(): ScheduleTemplate {\r\n    return this._schedulerManager.templates.get(this.templateDetailsStore.state.templateId);\r\n  }\r\n\r\n  /** Gets all schedule instances currently displayed. */\r\n  get instances(): ScheduleInstance[] {\r\n    return this._schedulerManager.instances.getEntities(this.store.state.scheduleInstanceIds);\r\n  }\r\n\r\n  constructor(\r\n    public readonly store: WorkspacePageStore,\r\n    public readonly templateDetailsStore: TemplateDetailsStore,\r\n    private _auth: GdcoAuth,\r\n    private _confirmationDialog: GdcoConfirmationDialog,\r\n    private _notifications: GdcoNotifications,\r\n    private _appInsights: GdcoAppInsights,\r\n    private _schedulerManager: SchedulerManager,\r\n    private _domainDataManager: DomainDataManager,\r\n    private _datacentersManager: DatacentersManager,\r\n    private _usersManager: UsersManager,\r\n    private _tasksManager: TasksManager,\r\n    private _fieldResolver: RecurringTaskFieldResolver\r\n  ) {\r\n    this.workspace$ = this._schedulerManager.workspaces.observe(this.store.workspaceId$);\r\n    this.schedules$ = this._schedulerManager.schedules.observeEntities(this.store.scheduleIds$);\r\n    this.activeSchedules$ = this.schedules$.pipe(filterItems(schedule => schedule.state === ScheduleState.Active));\r\n\r\n    this.hasCompletedSchedules$ = this.schedules$.pipe(\r\n      map(schedules => !!schedules?.find(schedule => schedule.state === ScheduleState.Complete))\r\n    );\r\n\r\n    this.filteredSchedules$ = combineLatest([\r\n      this.schedules$,\r\n      this.store.selectedCampus$,\r\n      this.store.selectedUserId$,\r\n      this.store.showCompletedSchedules$,\r\n      this.store.searchTerm$\r\n    ]).pipe(\r\n      map(([schedules, selectedCampus, userId, showCompletedSchedules, searchTerm]) => {\r\n        return this._filterSchedules(schedules, selectedCampus, userId, showCompletedSchedules, searchTerm);\r\n      })\r\n    );\r\n\r\n    this.mySchedules$ = this.filteredSchedules$.pipe(\r\n      filterItems(schedule => schedule.createdBy === this._auth.currentUser.userId),\r\n      sortItems((a, b) => {\r\n        const aDatacenter = this._fieldResolver.getFieldForSchedule(a, 'DatacenterCode') || '';\r\n        const bDatacenter = this._fieldResolver.getFieldForSchedule(b, 'DatacenterCode') || '';\r\n\r\n        // Sort by datacenter before sorting by name\r\n        return aDatacenter.toLowerCase().localeCompare(bDatacenter.toLowerCase());\r\n      }),\r\n      sortItems((a, b) => a.name.toLowerCase().localeCompare(b.name.toLowerCase()))\r\n    );\r\n\r\n    this.template$ = this._schedulerManager.templates.observe(this.templateDetailsStore.templateId$);\r\n    this.templates$ = this._schedulerManager.templates.observeEntities(this.store.templateIds$);\r\n    this.selectedSchedules$ = this._schedulerManager.schedules.observeEntities(this.store.selectedScheduleIds$);\r\n    this.selectedTemplates$ = this._schedulerManager.templates.observeEntities(this.store.selectedTemplateIds$);\r\n\r\n    this.diffOriginalTemplate$ = this._schedulerManager.templateVersions.observe(\r\n      combineLatest([this.templateDetailsStore.templateId$, this.templateDetailsStore.diffOriginalVersion$]).pipe(\r\n        map(([templateId, version]) => this._createTemplateVersionKey(templateId, version))\r\n      )\r\n    );\r\n    this.diffModifiedTemplate$ = this._schedulerManager.templateVersions.observe(\r\n      combineLatest([this.templateDetailsStore.templateId$, this.templateDetailsStore.diffModifiedVersion$]).pipe(\r\n        map(([templateId, version]) => this._createTemplateVersionKey(templateId, version))\r\n      )\r\n    );\r\n\r\n    this.viewSchedules$ = this._schedulerManager.schedules.observeEntities(this.store.viewScheduleIds$);\r\n    this.scheduleInstances$ = this._schedulerManager.instances.observeEntities(this.store.scheduleInstanceIds$);\r\n\r\n    this.selectedScheduleInstance$ = this._schedulerManager.instances.observe(this.store.selectedScheduleInstanceId$);\r\n    this.selectedScheduleInstanceSchedule$ = this._schedulerManager.schedules.observe(\r\n      this.selectedScheduleInstance$.pipe(map(instance => instance?.scheduleId))\r\n    );\r\n    this.selectedScheduleInstanceTasks$ = this._tasksManager.tasks.observeEntities(\r\n      this.store.selectedScheduleInstanceTaskIds$\r\n    );\r\n\r\n    this.selectedTemplate$ = combineLatest([this.templates$, this.store.selectedFaultCode$]).pipe(\r\n      map(([templates, faultCode]) => {\r\n        if (!templates || !faultCode) {\r\n          return null;\r\n        }\r\n\r\n        return templates.find(template => this._faultCodeExistsInTemplate(template, faultCode));\r\n      })\r\n    );\r\n\r\n    this.selectedFaultCode$ = this._domainDataManager.faultCodes.observe(\r\n      this.store.selectedFaultCode$.pipe(map(faultCode => faultCode?.toString()))\r\n    );\r\n\r\n    // We need to depend on templates$ here because the field resolver uses them internally\r\n    this.scheduleCampuses$ = combineLatest([this.schedules$, this.templates$]).pipe(\r\n      map(([schedules]) => this._getCampusesFromSchedules(schedules))\r\n    );\r\n\r\n    // We need to depend on templates$ here because the field resolver uses them internally\r\n    this.filteredScheduleCampuses$ = combineLatest([this.filteredSchedules$, this.templates$]).pipe(\r\n      map(([schedules]) => this._getCampusesFromSchedules(schedules))\r\n    );\r\n\r\n    const createdByUserIds = this.schedules$.pipe(\r\n      mapItems(schedule => schedule.createdBy),\r\n      distictItems()\r\n    );\r\n\r\n    this.scheduleCreatedByUsers$ = this._usersManager.users\r\n      .observeEntities(createdByUserIds)\r\n      .pipe(sortItems((a, b) => a.DisplayName.toLowerCase().localeCompare(b.DisplayName.toLowerCase())));\r\n\r\n    this.faultCodes$ = this._domainDataManager.faultCodes.observeAll();\r\n    this.metadata$ = this._domainDataManager.metadata$;\r\n\r\n    this.faultCodeMap$ = this.faultCodes$.pipe(groupItemsBy('code', true));\r\n\r\n    this.templateFaultCodes$ = combineLatest([this.templates$, this.faultCodes$]).pipe(\r\n      map(([templates, faultCodes]) => {\r\n        if (!templates || !faultCodes) {\r\n          return [];\r\n        }\r\n\r\n        return faultCodes\r\n          .filter(faultCode => {\r\n            return templates.find(template => {\r\n              return (\r\n                this._faultCodeExistsInTemplate(template, faultCode.code) && !template.clientData?.disableUiCreation\r\n              );\r\n            });\r\n          })\r\n          .sort((a, b) => a.description.toLowerCase().localeCompare(b.description.toLowerCase()));\r\n      })\r\n    );\r\n\r\n    // We need to depend on templates$ here because the field resolver uses them internally\r\n    this.schedulesByCampus$ = combineLatest([this.scheduleCampuses$, this.filteredSchedules$, this.templates$]).pipe(\r\n      map(([campuses, schedules]) => {\r\n        if (!campuses || !schedules) {\r\n          return {};\r\n        }\r\n\r\n        return campuses.reduce<{ [campus: string]: Schedule[] }>((prev, curr) => {\r\n          prev[curr] = schedules\r\n            .filter(schedule => {\r\n              const campus = this._fieldResolver.getFieldForSchedule(schedule, 'FacilityCampus');\r\n\r\n              return this._getCampusName(campus) === curr;\r\n            })\r\n            .sort((a, b) => {\r\n              const aDatacenter = this._fieldResolver.getFieldForSchedule(a, 'DatacenterCode') || '';\r\n              const bDatacenter = this._fieldResolver.getFieldForSchedule(b, 'DatacenterCode') || '';\r\n\r\n              // Sort by datacenter before sorting by name\r\n              return aDatacenter.toLowerCase().localeCompare(bDatacenter.toLowerCase());\r\n            })\r\n            .sort((a, b) => a.name.toLowerCase().localeCompare(b.name.toLowerCase()));\r\n\r\n          return prev;\r\n        }, {});\r\n      })\r\n    );\r\n\r\n    this.schedulesById$ = this.schedules$.pipe(groupItemsBy('scheduleId', true));\r\n\r\n    this.editSchedule$ = this._schedulerManager.schedules.observe(this.store.editScheduleId$);\r\n    this.editScheduleTemplate$ = this._schedulerManager.templates.observe(\r\n      this.editSchedule$.pipe(map(schedule => schedule?.templateId))\r\n    );\r\n\r\n    this.duplicateSchedule$ = this._schedulerManager.schedules.observe(this.store.duplicateScheduleId$);\r\n    this.duplicateScheduleTemplate$ = this._schedulerManager.templates.observe(\r\n      this.duplicateSchedule$.pipe(map(schedule => schedule?.templateId))\r\n    );\r\n\r\n    // We need to depend on templates$ here because the field resolver uses them internally\r\n    this.scheduleFieldMap$ = combineLatest([this.schedules$, this.templates$]).pipe(\r\n      map(([schedules]) => {\r\n        return schedules.reduce<{ [id: string]: { [name: string]: any } }>((prev, curr) => {\r\n          prev[curr.scheduleId] = {\r\n            FacilityCampus: this._fieldResolver.getFieldForSchedule(curr, 'FacilityCampus'),\r\n            DatacenterCode: this._fieldResolver.getFieldForSchedule(curr, 'DatacenterCode'),\r\n            Colocation: this._fieldResolver.getFieldForSchedule(curr, 'Colocation'),\r\n            Group: this._fieldResolver.getFieldForSchedule(curr, 'Group'),\r\n            AssignedTo: this._fieldResolver.getFieldForSchedule(curr, 'AssignedTo'),\r\n            FacilityTimeZone: this._fieldResolver.getFieldForSchedule(curr, 'FacilityTimeZone')\r\n          };\r\n\r\n          return prev;\r\n        }, {});\r\n      })\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Initializes the workspace page, loading all schedules and templates in the workspace.\r\n   * @param context.payload The id of the workspace.\r\n   */\r\n  @Action({ type: ActionType.CancelInProgress })\r\n  async initializePage(context: ActionPayloadContext<string>): Promise<void> {\r\n    const workspaceId = context.payload;\r\n\r\n    this.store.initializePageStart(workspaceId);\r\n\r\n    await this.loadWorkspace(context);\r\n    await Promise.all([this.loadSchedules(context), this.loadTemplates(context)]);\r\n\r\n    await this.loadMetadata(context);\r\n  }\r\n\r\n  /**\r\n   * Initializes the schedule instances page by schedule id.\r\n   * @param context.payload The schedule id to load the schedule instances page.\r\n   */\r\n  @Action({ type: ActionType.CancelInProgress })\r\n  async initializeScheduleInstancesPageByScheduleId(context: ActionPayloadContext<string>): Promise<void> {\r\n    const scheduleId = context.payload;\r\n\r\n    this.store.clearViewedSchedules();\r\n    await this.loadScheduleInstances(new ActionPayloadContext([scheduleId]));\r\n\r\n    const schedule = this._schedulerManager.schedules.get(scheduleId);\r\n    if (schedule?.state === ScheduleState.Complete) {\r\n      this.store.showCompletedSchedules(true);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Loads the workspace with the given id.\r\n   * @param context.payload The id of the workspace to load.\r\n   */\r\n  @Action({ type: ActionType.ReuseInProgress })\r\n  async loadWorkspace(context: ActionPayloadContext<string>): Promise<void> {\r\n    try {\r\n      this.store.loadWorkspaceStart();\r\n\r\n      await this._schedulerManager.getWorkspaceById(context);\r\n    } catch (error) {\r\n      if (!isActionCanceled(error)) {\r\n        this._notifications.addError('Failed to load workspace.', error);\r\n      }\r\n\r\n      throw error; // Always re-throw errors\r\n    } finally {\r\n      this.store.loadWorkspaceEnd();\r\n    }\r\n  }\r\n\r\n  /** Loads all schedules for the current workspace. */\r\n  @Action({ type: ActionType.ReuseInProgress })\r\n  async loadSchedules(context: ActionContext): Promise<void> {\r\n    let scheduleIds: string[] = [];\r\n    const { workspaceId } = this.store.state;\r\n\r\n    this.store.loadSchedulesStart();\r\n\r\n    // Verify the workspace is loaded\r\n    await this.loadWorkspace(new ActionPayloadContext(workspaceId, context));\r\n\r\n    try {\r\n      const payload: ListSchedulesByWorkspaceIdPayload = {\r\n        workspaceId: workspaceId\r\n      };\r\n\r\n      const schedules = await this._schedulerManager.listSchedulesByWorkspaceId(\r\n        new ActionPayloadContext(payload, context)\r\n      );\r\n\r\n      // TODO: Remove this 'loadDatacenters' call after SOP schedules are updated to include campus\r\n      await this._datacentersManager.loadDatacenters(context);\r\n\r\n      scheduleIds = schedules.map(schedule => schedule.scheduleId);\r\n    } catch (error) {\r\n      if (!isActionCanceled(error)) {\r\n        this._notifications.addError('Failed to load existing recurring tasks.', error);\r\n      }\r\n\r\n      throw error; // Always re-throw errors\r\n    } finally {\r\n      this.store.loadSchedulesEnd(scheduleIds);\r\n    }\r\n  }\r\n\r\n  /** Loads all schedule templates for the current workspace. */\r\n  @Action({ type: ActionType.ReuseInProgress })\r\n  async loadTemplates(context: ActionContext): Promise<void> {\r\n    let templateIds: string[] = [];\r\n    const { workspaceId } = this.store.state;\r\n\r\n    this.store.loadTemplatesStart();\r\n\r\n    // Verify the workspace is loaded\r\n    await this.loadWorkspace(new ActionPayloadContext(workspaceId, context));\r\n\r\n    try {\r\n      const schedules = await this._schedulerManager.listScheduleTemplatesByWorkspaceId(\r\n        new ActionPayloadContext(workspaceId, context)\r\n      );\r\n\r\n      templateIds = schedules.map(schedule => schedule.scheduleTemplateId);\r\n    } catch (error) {\r\n      if (!isActionCanceled(error)) {\r\n        this._notifications.addError('Failed to load existing templates.', error);\r\n      }\r\n\r\n      throw error; // Always re-throw errors\r\n    } finally {\r\n      this.store.loadTemplatesEnd(templateIds);\r\n    }\r\n  }\r\n\r\n  @Action()\r\n  async loadTemplateVersions(context: ActionPayloadContext<string>): Promise<void> {\r\n    const templateId = context.payload;\r\n    const { workspaceId } = this.store.state;\r\n\r\n    try {\r\n      const payload: ListScheduleTemplateVersionsByIdPayload = {\r\n        workspaceId: workspaceId,\r\n        templateId: templateId\r\n      };\r\n\r\n      const templates = await this._schedulerManager.listScheduleTemplateVersionsById(\r\n        new ActionPayloadContext(payload, context)\r\n      );\r\n\r\n      this.templateDetailsStore.diffTemplateView(this.template.version, this.template.version - 1);\r\n    } catch (error) {\r\n      if (!isActionCanceled(error)) {\r\n        this._notifications.addError('Failed to load existing templates.', error);\r\n      }\r\n\r\n      throw error; // Always re-throw errors\r\n    } finally {\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Loads all scheduled instances for the schedules with the given Ids.\r\n   * @param context.payload A list of schedule Ids to load the instances for.\r\n   */\r\n  @Action({ type: ActionType.CancelInProgress })\r\n  async loadScheduleInstances(context: ActionPayloadContext<string[]>): Promise<void> {\r\n    const scheduleIds = context.payload;\r\n    const { workspaceId, viewDate } = this.store.state;\r\n    let scheduleInstanceIds: string[] = [];\r\n\r\n    this.store.loadScheduleInstancesStart(scheduleIds);\r\n\r\n    // Verify the workspace is loaded\r\n    await this.loadWorkspace(new ActionPayloadContext(workspaceId, context));\r\n\r\n    try {\r\n      const promises: Promise<ScheduleInstance[]>[] = [];\r\n\r\n      // Load the instances for all given schedules in parallel\r\n      for (const scheduleId of scheduleIds) {\r\n        const payload: ListScheduleInstancesByScheduleIdPayload = {\r\n          workspaceId: workspaceId,\r\n          scheduleId: scheduleId\r\n        };\r\n\r\n        // Load all active instances\r\n        const promise = this._schedulerManager.listScheduleInstancesByScheduleId(\r\n          new ActionPayloadContext(payload, context)\r\n        );\r\n\r\n        promises.push(promise);\r\n\r\n        // Don't load pages beyond the end date or before the start date\r\n        const schedule = this._schedulerManager.schedules.get(scheduleId);\r\n        const startDate = schedule?.recurrencePattern.startDate;\r\n        const endDate = schedule?.recurrencePattern.endDate;\r\n        if (\r\n          !schedule ||\r\n          (viewDate.getFullYear() >= startDate.getFullYear() &&\r\n            (!endDate || viewDate.getFullYear() <= endDate.getFullYear()))\r\n        ) {\r\n          const paginPayload: ListScheduleInstancesPageByScheduleIdPayload = {\r\n            workspaceId: workspaceId,\r\n            scheduleId: scheduleId,\r\n            pageDate: viewDate\r\n          };\r\n\r\n          // Load current page of instances\r\n          const pagingPromise = this._schedulerManager.listScheduleInstancesPageByScheduleId(\r\n            new ActionPayloadContext(paginPayload, context)\r\n          );\r\n\r\n          promises.push(pagingPromise);\r\n        }\r\n      }\r\n\r\n      const results = await Promise.all(promises);\r\n\r\n      scheduleInstanceIds = results.reduce<string[]>((prev, curr) => {\r\n        return prev.concat(...curr.map(instance => instance.scheduleInstanceId));\r\n      }, []);\r\n\r\n      scheduleInstanceIds = gdcoUtils.distinct(scheduleInstanceIds);\r\n    } catch (error) {\r\n      if (!isActionCanceled(error)) {\r\n        this._notifications.addError('Failed to load schedule instances.', error);\r\n      }\r\n\r\n      throw error; // Always re-throw errors\r\n    } finally {\r\n      this.store.loadScheduleInstancesEnd(scheduleInstanceIds);\r\n    }\r\n  }\r\n\r\n  /** Loads all scheduled instances in the current year for the given date. */\r\n  @Action({ type: ActionType.ReuseInProgress })\r\n  async loadScheduleInstancesPage(context: ActionPayloadContext<Date>): Promise<void> {\r\n    const pageDate = context.payload;\r\n    const { workspaceId } = this.store.state;\r\n    let scheduleInstanceIds: string[] = [];\r\n\r\n    // Verify the workspace is loaded\r\n    await this.loadWorkspace(new ActionPayloadContext(workspaceId, context));\r\n\r\n    try {\r\n      const promises: Promise<ScheduleInstance[]>[] = [];\r\n\r\n      // Load the instance pages for all current viewed schedules in parallel\r\n      for (const scheduleId of this.store.state.viewScheduleIds) {\r\n        const schedule = this._schedulerManager.schedules.get(scheduleId);\r\n        const startDate = schedule?.recurrencePattern.startDate;\r\n        const endDate = schedule?.recurrencePattern.endDate;\r\n\r\n        // Don't load pages beyond the end date or before the start date\r\n        if (\r\n          !schedule ||\r\n          (pageDate.getFullYear() >= startDate.getFullYear() &&\r\n            (!endDate || pageDate.getFullYear() <= endDate.getFullYear()))\r\n        ) {\r\n          const payload: ListScheduleInstancesPageByScheduleIdPayload = {\r\n            workspaceId: workspaceId,\r\n            scheduleId: scheduleId,\r\n            pageDate: pageDate\r\n          };\r\n\r\n          const promise = this._schedulerManager.listScheduleInstancesPageByScheduleId(\r\n            new ActionPayloadContext(payload, context)\r\n          );\r\n\r\n          promises.push(promise);\r\n        }\r\n      }\r\n\r\n      const results = await Promise.all(promises);\r\n\r\n      scheduleInstanceIds = results.reduce<string[]>((prev, curr) => {\r\n        return prev.concat(...curr.map(instance => instance.scheduleInstanceId));\r\n      }, []);\r\n    } catch (error) {\r\n      if (!isActionCanceled(error)) {\r\n        this._notifications.addError('Failed to load schedule instances.', error);\r\n      }\r\n\r\n      throw error; // Always re-throw errors\r\n    } finally {\r\n      this.store.loadScheduleInstancesPageEnd(scheduleInstanceIds, pageDate);\r\n    }\r\n  }\r\n\r\n  /** Loads all metadata needed by the workspace. */\r\n  @Action({ type: ActionType.ReuseInProgress })\r\n  async loadMetadata(context: ActionContext): Promise<void> {\r\n    const schedules = this._schedulerManager.schedules.getEntities(this.store.state.scheduleIds);\r\n    const userIds = schedules.map(schedule => schedule.createdBy);\r\n\r\n    await Promise.all([\r\n      this._domainDataManager.loadTaskMetadata(context),\r\n      this._domainDataManager.loadFaultCodes(context),\r\n      this._datacentersManager.loadDatacenters(context),\r\n      this._usersManager.getUsersByIds(new ActionPayloadContext(userIds, context))\r\n    ]);\r\n  }\r\n\r\n  /**\r\n   * Deletes the given schedule.\r\n   * @param context.payload The schedule to delete.\r\n   */\r\n  @Action()\r\n  async deleteSchedule(context: ActionPayloadContext<Schedule>): Promise<boolean> {\r\n    const schedule = context.payload;\r\n\r\n    try {\r\n      const dialogData: GdcoConfirmationData = {\r\n        title: 'Delete recurring task',\r\n        text: 'Are you sure you want to delete the selected recurring task?'\r\n      };\r\n\r\n      // Require user confirmation\r\n      const confirmed = await awaitable(this._confirmationDialog.confirm(dialogData), context.cancellationToken);\r\n      if (!confirmed) {\r\n        return false;\r\n      }\r\n\r\n      const payload: DeleteSchedulePayload = {\r\n        workspaceId: schedule.workspaceId,\r\n        scheduleId: schedule.scheduleId\r\n      };\r\n\r\n      await this._schedulerManager.deleteSchedule(new ActionPayloadContext(payload, context));\r\n\r\n      this._appInsights.trackEvent('ScheduleDeleted', {\r\n        workspaceId: schedule.workspaceId,\r\n        templateId: schedule.templateId,\r\n        datacenter: this._fieldResolver.getFieldForSchedule(schedule, 'DatacenterCode'),\r\n        recurrenceType: schedule.recurrencePattern.recurrenceType\r\n      });\r\n\r\n      const removedInstances = this.instances\r\n        .filter(instance => schedule.scheduleId === instance.scheduleId)\r\n        .map(instance => instance.scheduleInstanceId);\r\n\r\n      // Make sure the schedule doesn't stay selected\r\n      this.store.deselectAllSchedules();\r\n      this.store.removeInstances(removedInstances);\r\n    } catch (error) {\r\n      if (!isActionCanceled(error)) {\r\n        this._notifications.addError('Failed to delete recurring task', error);\r\n      }\r\n\r\n      throw error; // Always re-throw errors\r\n    }\r\n\r\n    return true;\r\n  }\r\n\r\n  /** Deletes all schedules selected by the user on the schedules page. */\r\n  @Action()\r\n  async deleteSelectedSchedules(context: ActionContext): Promise<void> {\r\n    const deletedIds: string[] = [];\r\n    const { workspaceId, selectedScheduleIds } = this.store.state;\r\n\r\n    try {\r\n      const dialogData: GdcoConfirmationData = {\r\n        title: 'Delete selected recurring tasks',\r\n        text: 'Are you sure you want to delete the selected recurring tasks?'\r\n      };\r\n\r\n      // Require user confirmation\r\n      const confirmed = await awaitable(this._confirmationDialog.confirm(dialogData), context.cancellationToken);\r\n      if (!confirmed) {\r\n        return;\r\n      }\r\n\r\n      this.store.deleteSelectedSchedulesStart();\r\n\r\n      const promises: Promise<any>[] = [];\r\n\r\n      // Delete schedules in parallel\r\n      for (const scheduleId of selectedScheduleIds) {\r\n        const payload: DeleteSchedulePayload = {\r\n          workspaceId: workspaceId,\r\n          scheduleId: scheduleId\r\n        };\r\n\r\n        const promise = this._schedulerManager.deleteSchedule(new ActionPayloadContext(payload, context));\r\n        promises.push(promise);\r\n\r\n        deletedIds.push(scheduleId);\r\n      }\r\n\r\n      await Promise.all(promises);\r\n\r\n      const removedInstanceIds = this.instances\r\n        .filter(instance => deletedIds.includes(instance.scheduleId))\r\n        .map(instance => instance.scheduleInstanceId);\r\n\r\n      this.store.removeInstances(removedInstanceIds);\r\n\r\n      this._appInsights.trackEvent('BulkSchedulesDeleted', {\r\n        workspaceId: workspaceId,\r\n        count: selectedScheduleIds.length.toString(),\r\n        page: 'list'\r\n      });\r\n\r\n      // Make sure the schedules don't stay selected\r\n      this.store.deselectAllSchedules();\r\n    } catch (error) {\r\n      if (!isActionCanceled(error)) {\r\n        this._notifications.addError('Failed to delete selected recurring tasks', error);\r\n      }\r\n\r\n      throw error; // Always re-throw errors\r\n    } finally {\r\n      this.store.deleteSelectedSchedulesEnd(deletedIds);\r\n    }\r\n  }\r\n\r\n  /** Deletes all schedules selected by the user on the calendar page. */\r\n  @Action()\r\n  async deleteViewedSchedules(context: ActionContext): Promise<void> {\r\n    const deletedIds: string[] = [];\r\n    const { workspaceId, viewScheduleIds } = this.store.state;\r\n\r\n    try {\r\n      const dialogData: GdcoConfirmationData = {\r\n        title: 'Delete selected recurring tasks',\r\n        text: 'Are you sure you want to delete the selected recurring tasks?'\r\n      };\r\n\r\n      // Require user confirmation\r\n      const confirmed = await awaitable(this._confirmationDialog.confirm(dialogData), context.cancellationToken);\r\n      if (!confirmed) {\r\n        return;\r\n      }\r\n\r\n      this.store.deleteViewedSchedulesStart();\r\n\r\n      const promises: Promise<any>[] = [];\r\n\r\n      // Delete schedules in parallel\r\n      for (const scheduleId of viewScheduleIds) {\r\n        const payload: DeleteSchedulePayload = {\r\n          workspaceId: workspaceId,\r\n          scheduleId: scheduleId\r\n        };\r\n\r\n        const promise = this._schedulerManager.deleteSchedule(new ActionPayloadContext(payload, context));\r\n        promises.push(promise);\r\n\r\n        deletedIds.push(scheduleId);\r\n      }\r\n\r\n      await Promise.all(promises);\r\n\r\n      const removedInstanceIds = this.instances\r\n        .filter(instance => deletedIds.includes(instance.scheduleId))\r\n        .map(instance => instance.scheduleInstanceId);\r\n\r\n      this.store.removeInstances(removedInstanceIds);\r\n\r\n      this._appInsights.trackEvent('BulkSchedulesDeleted', {\r\n        workspaceId: workspaceId,\r\n        count: viewScheduleIds.length.toString(),\r\n        page: 'calendar'\r\n      });\r\n    } catch (error) {\r\n      if (!isActionCanceled(error)) {\r\n        this._notifications.addError('Failed to delete selected recurring tasks', error);\r\n      }\r\n\r\n      throw error; // Always re-throw errors\r\n    } finally {\r\n      this.store.deleteViewedSchedulesEnd(deletedIds);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Deletes the given schedule template. This will fail if the template is currently used by any schedules.\r\n   * @param context.payload The schedule template to delete.\r\n   */\r\n  @Action()\r\n  async deleteTemplate(context: ActionPayloadContext<ScheduleTemplate>): Promise<boolean> {\r\n    const template = context.payload;\r\n\r\n    try {\r\n      const dialogData: GdcoConfirmationData = {\r\n        title: 'Delete template',\r\n        text: 'Are you sure you want to delete the selected template?'\r\n      };\r\n\r\n      // Require user confirmation\r\n      const confirmed = await awaitable(this._confirmationDialog.confirm(dialogData), context.cancellationToken);\r\n      if (!confirmed) {\r\n        return false;\r\n      }\r\n\r\n      const payload: DeleteScheduleTemplatePayload = {\r\n        workspaceId: template.workspaceId,\r\n        templateId: template.scheduleTemplateId\r\n      };\r\n\r\n      await this._schedulerManager.deleteScheduleTemplate(new ActionPayloadContext(payload, context));\r\n\r\n      // Make sure the schedule template doesn't stay selected\r\n      this.store.deselectAllTemplates();\r\n    } catch (error) {\r\n      if (!isActionCanceled(error)) {\r\n        this._notifications.addError('Failed to delete template', error);\r\n      }\r\n\r\n      throw error; // Always re-throw errors\r\n    }\r\n\r\n    return true;\r\n  }\r\n\r\n  /** Deletes all schedule templates selected by the user. */\r\n  @Action()\r\n  async deleteSelectedTemplates(context: ActionContext): Promise<void> {\r\n    const deletedIds: string[] = [];\r\n    const { workspaceId, selectedTemplateIds } = this.store.state;\r\n\r\n    try {\r\n      const dialogData: GdcoConfirmationData = {\r\n        title: 'Delete selected templates',\r\n        text: 'Are you sure you want to delete the selected templates?'\r\n      };\r\n\r\n      // Require user confirmation\r\n      const confirmed = await awaitable(this._confirmationDialog.confirm(dialogData), context.cancellationToken);\r\n      if (!confirmed) {\r\n        return;\r\n      }\r\n\r\n      this.store.deleteSelectedTemplatesStart();\r\n\r\n      const promises: Promise<any>[] = [];\r\n\r\n      // Delete schedule templates in parallel\r\n      for (const templateId of selectedTemplateIds) {\r\n        const payload: DeleteScheduleTemplatePayload = {\r\n          workspaceId: workspaceId,\r\n          templateId: templateId\r\n        };\r\n\r\n        const promise = this._schedulerManager.deleteScheduleTemplate(new ActionPayloadContext(payload, context));\r\n        promises.push(promise);\r\n\r\n        deletedIds.push(templateId);\r\n      }\r\n\r\n      await Promise.all(promises);\r\n\r\n      // Make sure the schedule templates don't stay selected\r\n      this.store.deselectAllTemplates();\r\n    } catch (error) {\r\n      if (!isActionCanceled(error)) {\r\n        this._notifications.addError('Failed to delete selected templates', error);\r\n      }\r\n\r\n      throw error; // Always re-throw errors\r\n    } finally {\r\n      this.store.deleteSelectedTemplatesEnd(deletedIds);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Creates a new schedule.\r\n   * @param context.payload The user entered data for the schedule to create.\r\n   */\r\n  @Action()\r\n  async createNewRecurringTask(context: ActionPayloadContext<RecurringTask>): Promise<boolean> {\r\n    const recurringTask = context.payload;\r\n    const { workspaceId, selectedFaultCode, templateIds } = this.store.state;\r\n\r\n    try {\r\n      const result = await this._checkDayOfMonth(recurringTask);\r\n      if (!result) {\r\n        return false;\r\n      }\r\n\r\n      this.store.createNewRecurringTaskStart();\r\n\r\n      const templates = this._schedulerManager.templates.getEntities(templateIds);\r\n      const template = templates.find(t => this._faultCodeExistsInTemplate(t, selectedFaultCode));\r\n\r\n      const faultCode = this._domainDataManager.faultCodesEntityStore.get(selectedFaultCode.toString());\r\n\r\n      const schedule: Partial<Schedule> = {\r\n        ...new RecurringTaskConverter().convertBack(recurringTask, null, this.workspace.clientData?.useUtc),\r\n        workspaceId: workspaceId,\r\n        name: recurringTask.name || faultCode.description,\r\n        templateId: template.scheduleTemplateId,\r\n        clientData: {\r\n          faultCode: selectedFaultCode\r\n        }\r\n      };\r\n\r\n      // Temporarily hardcode the selected fault code as a parameter\r\n      if (template.parameterDefinitions.find(parameter => parameter.name === 'faultCode')) {\r\n        const predefinedParameters = schedule.templateParameterRetrievalSetting?.predefinedParameters;\r\n        if (predefinedParameters && predefinedParameters[0]) {\r\n          if (!predefinedParameters[0]['faultCode']) {\r\n            predefinedParameters[0]['faultCode'] = selectedFaultCode;\r\n          }\r\n        }\r\n      }\r\n\r\n      // Update executionTimeOffset if it was set in the template\r\n      if (template.clientData?.executionTimeOffsetInDays) {\r\n        schedule.recurrencePattern.executionTimeOffset = `${template.clientData.executionTimeOffsetInDays.toString()}.00:00:00`;\r\n      }\r\n\r\n      const newSchedule = await this._schedulerManager.createSchedule(new ActionPayloadContext(schedule, context));\r\n\r\n      this._appInsights.trackEvent('ScheduleCreated', {\r\n        workspaceId: workspaceId,\r\n        templateId: template.scheduleTemplateId,\r\n        datacenter: this._fieldResolver.getFieldForSchedule(newSchedule, 'DatacenterCode'),\r\n        selectedFaultCode: selectedFaultCode?.toString(),\r\n        recurrenceType: schedule.recurrencePattern.recurrenceType,\r\n        maxNumOfOccurrences:\r\n          schedule.recurrencePattern.maxNumOfOccurrences > 0\r\n            ? schedule.recurrencePattern.maxNumOfOccurrences.toString()\r\n            : undefined,\r\n        daysOfWeek: JSON.stringify(schedule.recurrencePattern.daysOfWeek)\r\n      });\r\n\r\n      this.store.addSchedule(newSchedule.scheduleId);\r\n    } catch (error) {\r\n      if (!isActionCanceled(error)) {\r\n        this._notifications.addError('Failed to create recurring task', error);\r\n      }\r\n\r\n      throw error; // Always re-throw errors\r\n    } finally {\r\n      this.store.createNewRecurringTaskEnd();\r\n    }\r\n\r\n    return true;\r\n  }\r\n\r\n  /**\r\n   * Updates an existing schedule.\r\n   * @param context.payload The user entered data to update the schedule from.\r\n   */\r\n  @Action()\r\n  async updateRecurringTask(context: ActionPayloadContext<RecurringTask>): Promise<boolean> {\r\n    const recurringTask = context.payload;\r\n    const { workspaceId, editScheduleId, selectedFaultCode } = this.store.state;\r\n\r\n    try {\r\n      const result = await this._checkDayOfMonth(recurringTask);\r\n      if (!result) {\r\n        return false;\r\n      }\r\n\r\n      this.store.updateRecurringTaskStart();\r\n\r\n      const payload: GetScheduleByIdPayload = {\r\n        workspaceId: workspaceId,\r\n        scheduleId: editScheduleId\r\n      };\r\n\r\n      const existingSchedule = await this._schedulerManager.getScheduleById(new ActionPayloadContext(payload, context));\r\n\r\n      const template = this._schedulerManager.templates.get(existingSchedule.templateId);\r\n\r\n      const schedule = new RecurringTaskConverter().convertBack(\r\n        recurringTask,\r\n        existingSchedule,\r\n        this.workspace.clientData?.useUtc\r\n      );\r\n\r\n      schedule.clientData = {\r\n        ...existingSchedule.clientData\r\n      };\r\n\r\n      // Temporarily hardcode the selected fault code as a parameter\r\n      if (template.parameterDefinitions.find(parameter => parameter.name === 'faultCode')) {\r\n        const predefinedParameters = schedule.templateParameterRetrievalSetting?.predefinedParameters;\r\n        if (predefinedParameters && predefinedParameters[0]) {\r\n          if (!predefinedParameters[0]['faultCode']) {\r\n            predefinedParameters[0]['faultCode'] = selectedFaultCode;\r\n          }\r\n        }\r\n      }\r\n\r\n      // Update executionTimeOffset if it was set in the template\r\n      if (template.clientData?.executionTimeOffsetInDays) {\r\n        schedule.recurrencePattern.executionTimeOffset = `${template.clientData.executionTimeOffsetInDays.toString()}.00:00:00`;\r\n      }\r\n\r\n      await this._schedulerManager.updateSchedule(new ActionPayloadContext(schedule, context));\r\n\r\n      this._appInsights.trackEvent('ScheduleUpdated', {\r\n        workspaceId: workspaceId,\r\n        templateId: template.scheduleTemplateId,\r\n        datacenter: this._fieldResolver.getFieldForSchedule(schedule, 'DatacenterCode'),\r\n        recurrenceType: schedule.recurrencePattern.recurrenceType\r\n      });\r\n\r\n      if (this.store.state.viewScheduleIds.includes(schedule.scheduleId)) {\r\n        await this.loadScheduleInstances(new ActionPayloadContext(this.store.state.viewScheduleIds, context));\r\n      }\r\n    } catch (error) {\r\n      if (!isActionCanceled(error)) {\r\n        this._notifications.addError('Failed to update scheduled task', error);\r\n      }\r\n\r\n      throw error; // Always re-throw errors\r\n    } finally {\r\n      this.store.updateRecurringTaskEnd();\r\n    }\r\n\r\n    return true;\r\n  }\r\n\r\n  /**\r\n   * Creates a new schedule template from the given user input.\r\n   * @returns The id of the newly created template.\r\n   */\r\n  @Action()\r\n  async createScheduleTemplate(context: ActionPayloadContext<TemplateForm>): Promise<string> {\r\n    const form = context.payload;\r\n    const { workspaceId } = this.store.state;\r\n\r\n    try {\r\n      this.templateDetailsStore.createScheduleTemplateStart();\r\n\r\n      const scheduleTemplate = new TemplateFormConverter().convertBack(form, workspaceId);\r\n\r\n      if (scheduleTemplate.parameterDefinitions) {\r\n        const faultCodeExists = scheduleTemplate.parameterDefinitions.find(param => param.name === 'faultCode');\r\n        if (!faultCodeExists) {\r\n          scheduleTemplate.parameterDefinitions.push({\r\n            name: 'faultCode',\r\n            dataType: ScheduleTemplateParameterDataType.Number\r\n          });\r\n        }\r\n      }\r\n\r\n      const newTemplate = await this._schedulerManager.createScheduleTemplate(\r\n        new ActionPayloadContext(scheduleTemplate, context)\r\n      );\r\n\r\n      this.store.addTemplate(newTemplate.scheduleTemplateId);\r\n\r\n      return newTemplate.scheduleTemplateId;\r\n    } catch (error) {\r\n      if (!isActionCanceled(error)) {\r\n        this._notifications.addError('Failed to create template', error);\r\n      }\r\n\r\n      throw error; // Always re-throw errors\r\n    } finally {\r\n      this.templateDetailsStore.createScheduleTemplateEnd();\r\n    }\r\n  }\r\n\r\n  /** Previews the creation of a task with an unsaved schedule template and given parameters. */\r\n  @Action()\r\n  async previewUnsavedTemplate(context: ActionPayloadContext<PreviewTemplate>): Promise<void> {\r\n    const { template, parameters } = context.payload;\r\n\r\n    let task: Partial<Task>;\r\n\r\n    try {\r\n      this.templateDetailsStore.previewUnsavedTemplateStart();\r\n\r\n      if (template.parameterDefinitions) {\r\n        const faultCodeExists = template.parameterDefinitions.find(param => param.name === 'faultCode');\r\n        if (!faultCodeExists) {\r\n          template.parameterDefinitions.push({\r\n            name: 'faultCode',\r\n            dataType: ScheduleTemplateParameterDataType.Number\r\n          });\r\n        }\r\n      }\r\n\r\n      const payload: PreviewUnsavedScheduleTemplatePayload = {\r\n        template: template,\r\n        parameters: parameters\r\n      };\r\n\r\n      task = (await this._schedulerManager.previewUnsavedScheduleTemplate(\r\n        new ActionPayloadContext(payload, context)\r\n      )) as Task;\r\n    } catch (error) {\r\n      if (!isActionCanceled(error)) {\r\n        this._notifications.addError('Failed to preview template', error);\r\n      }\r\n\r\n      throw error; // Always re-throw errors\r\n    } finally {\r\n      this.templateDetailsStore.previewUnsavedTemplateEnd(task);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Copies a template from a different workspace or environment.\r\n   * @param context.payload The existing template to copy into the current workspace.\r\n   */\r\n  @Action()\r\n  async copyExistingTemplate(context: ActionPayloadContext<ScheduleTemplate>): Promise<void> {\r\n    const template = context.payload;\r\n    const { workspaceId } = this.store.state;\r\n\r\n    try {\r\n      this.store.createNewTemplateStart();\r\n\r\n      template.workspaceId = workspaceId;\r\n\r\n      const newTemplate = await this._schedulerManager.createScheduleTemplate(\r\n        new ActionPayloadContext(template, context)\r\n      );\r\n\r\n      this.store.addTemplate(newTemplate.scheduleTemplateId);\r\n    } catch (error) {\r\n      if (!isActionCanceled(error)) {\r\n        this._notifications.addError('Failed to create template', error);\r\n      }\r\n\r\n      throw error; // Always re-throw errors\r\n    } finally {\r\n      this.store.createNewTemplateEnd();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Updates an existing template.\r\n   * @param context.payload The user entered data to update the template from.\r\n   */\r\n  @Action()\r\n  async updateScheduleTemplate(context: ActionPayloadContext<TemplateForm>): Promise<void> {\r\n    const template = context.payload;\r\n\r\n    try {\r\n      this.templateDetailsStore.createScheduleTemplateStart();\r\n\r\n      const scheduleTemplate = new TemplateFormConverter().convertBack(\r\n        template,\r\n        this.store.state.workspaceId,\r\n        this.templateDetailsStore.state.templateId\r\n      );\r\n\r\n      if (scheduleTemplate.parameterDefinitions) {\r\n        const faultCodeExists = scheduleTemplate.parameterDefinitions.find(param => param.name === 'faultCode');\r\n        if (!faultCodeExists) {\r\n          scheduleTemplate.parameterDefinitions.push({\r\n            name: 'faultCode',\r\n            dataType: ScheduleTemplateParameterDataType.Number\r\n          });\r\n        }\r\n      }\r\n\r\n      await this._schedulerManager.updateScheduleTemplate(new ActionPayloadContext(scheduleTemplate, context));\r\n\r\n      this.templateDetailsStore.standardView();\r\n    } catch (error) {\r\n      if (!isActionCanceled(error)) {\r\n        this._notifications.addError('Failed to save template', error);\r\n      }\r\n\r\n      throw error; // Always re-throw errors\r\n    } finally {\r\n      this.templateDetailsStore.createScheduleTemplateEnd();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Begins editing the given schedule.\r\n   * @param schedule The schedule to edit.\r\n   */\r\n  editRecurringTask(schedule: Schedule): void {\r\n    const recurringTask = new RecurringTaskConverter().convert(schedule);\r\n\r\n    this.store.openEditRecurringTaskDialog(recurringTask, schedule);\r\n  }\r\n\r\n  /**\r\n   * Begins duplicating the given schedule.\r\n   * @param schedule The schedule to duplicate.\r\n   */\r\n  duplicateRecurringTask(schedule: Schedule): void {\r\n    const recurringTask = new RecurringTaskConverter().convert(schedule);\r\n\r\n    this.store.openDuplicateRecurringTaskDialog(recurringTask, schedule);\r\n  }\r\n\r\n  /**\r\n   * Views the details of the given schedule instance and loads all tasks created by the instance.\r\n   * @param context.payload The instance details to view.\r\n   */\r\n  @Action({ type: ActionType.CancelInProgress })\r\n  async viewScheduleInstance(context: ActionPayloadContext<ScheduleInstance>): Promise<void> {\r\n    const scheduleInstance = context.payload;\r\n\r\n    try {\r\n      this.store.openScheduleInstanceDialog(scheduleInstance.scheduleInstanceId);\r\n\r\n      let taskIds: string[] = [];\r\n      if (\r\n        scheduleInstance.state === ScheduleInstanceState.Succeeded ||\r\n        scheduleInstance.state === ScheduleInstanceState.PartiallySucceeded\r\n      ) {\r\n        const queryBuilder = new GdcoQueryBuilder()\r\n          .startWith('CreatedByScheduleWorkspaceId', GdcoQueryOperator.equals, scheduleInstance.workspaceId)\r\n          .and('CreatedByScheduleId', GdcoQueryOperator.equals, scheduleInstance.scheduleId)\r\n          .and('CreatedByScheduleInstanceId', GdcoQueryOperator.equals, scheduleInstance.scheduleInstanceId);\r\n\r\n        const payload: SearchTasksPayload = {\r\n          query: queryBuilder,\r\n          skip: 0\r\n        };\r\n\r\n        const tasks = await this._tasksManager.searchTasks(new ActionPayloadContext(payload, context));\r\n\r\n        taskIds = tasks.results.map(task => task.Id);\r\n      }\r\n\r\n      this.store.setSelectedScheduleInstanceTaskIds(taskIds);\r\n    } catch (error) {\r\n      if (!isActionCanceled(error)) {\r\n        this._notifications.addError('Failed to load scheduled task details', error);\r\n      }\r\n\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Updates the columns displayed in a workspace.\r\n   * @param context.payload The new set of columns that should be displayed.\r\n   */\r\n  @Action()\r\n  async updateWorkspaceColumnOptions(context: ActionPayloadContext<string[]>): Promise<void> {\r\n    const options = context.payload;\r\n\r\n    try {\r\n      const confirmationData: GdcoConfirmationData = {\r\n        title: 'Are you sure?',\r\n        text: 'These changes apply to all users of the current workspace. Are you sure you want to make these changes?'\r\n      };\r\n\r\n      // Require user confirmation\r\n      const confirmed = await awaitable(this._confirmationDialog.confirm(confirmationData), context.cancellationToken);\r\n      if (!confirmed) {\r\n        return;\r\n      }\r\n\r\n      this.store.updateWorkspaceColumnOptionsStart();\r\n\r\n      const updatedWorkspace: ScheduleWorkspace = {\r\n        ...this.workspace,\r\n        clientData: {\r\n          ...this.workspace.clientData,\r\n          columns: options\r\n        }\r\n      };\r\n\r\n      await this._schedulerManager.updateWorkspace(new ActionPayloadContext(updatedWorkspace, context));\r\n\r\n      this.store.closeColumnOptionsDialog();\r\n    } catch (error) {\r\n      if (!isActionCanceled(error)) {\r\n        this._notifications.addError('Failed to save new column options', error);\r\n      }\r\n\r\n      throw error;\r\n    } finally {\r\n      this.store.updateWorkspaceColumnOptionsEnd();\r\n    }\r\n  }\r\n\r\n  private _faultCodeExistsInTemplate(template: ScheduleTemplate, faultCode: number): boolean {\r\n    if (!template || !template.clientData) {\r\n      return false;\r\n    }\r\n\r\n    return template.clientData.faultCodes?.includes(faultCode);\r\n  }\r\n\r\n  private _getCampusesFromSchedules(schedules: Schedule[]): string[] {\r\n    if (!schedules) {\r\n      return [];\r\n    }\r\n\r\n    return schedules\r\n      .map(schedule => this._fieldResolver.getFieldForSchedule(schedule, 'FacilityCampus'))\r\n      .map(campus => this._getCampusName(campus))\r\n      .filter((value, index, arr) => arr.indexOf(value) === index)\r\n      .sort();\r\n  }\r\n\r\n  private _filterSchedules(\r\n    schedules: Schedule[],\r\n    selectedCampus: string,\r\n    userId: string,\r\n    showCompletedSchedules: boolean,\r\n    searchTerm: string\r\n  ): Schedule[] {\r\n    if (!schedules) {\r\n      return [];\r\n    }\r\n\r\n    let filteredSchedules = schedules;\r\n    if (selectedCampus) {\r\n      filteredSchedules = filteredSchedules.filter(schedule => {\r\n        const campus = this._fieldResolver.getFieldForSchedule(schedule, 'FacilityCampus');\r\n\r\n        return campus === selectedCampus;\r\n      });\r\n    }\r\n\r\n    if (userId) {\r\n      filteredSchedules = filteredSchedules.filter(schedule => schedule.createdBy === userId);\r\n    }\r\n\r\n    if (!showCompletedSchedules) {\r\n      filteredSchedules = filteredSchedules.filter(schedule => schedule.state === ScheduleState.Active);\r\n    }\r\n\r\n    if (searchTerm && searchTerm.trim()) {\r\n      const searchTermLower = searchTerm.trim().toLowerCase();\r\n      filteredSchedules = filteredSchedules.filter(schedule => {\r\n        this._schedulerMatched(searchTermLower, schedule);\r\n        //   if (schedule.name?.toLowerCase().includes(searchTermLower)) {\r\n        //     return true;\r\n        //   }\r\n        //   const facilityCampus = this._fieldResolver.getFieldForSchedule(schedule, 'FacilityCampus');\r\n        //   if (facilityCampus?.toLowerCase().includes(searchTermLower)) {\r\n        //     return true;\r\n        //   }\r\n\r\n        //   const datacenterCode = this._fieldResolver.getFieldForSchedule(schedule, 'DatacenterCode');\r\n        //   if (datacenterCode?.toLowerCase().includes(searchTermLower)) {\r\n        //     return true;\r\n        //   }\r\n\r\n        //   const facilityTimeZone = this._fieldResolver.getFieldForSchedule(schedule, 'FacilityTimeZone');\r\n        //   if (facilityTimeZone?.toLowerCase().includes(searchTermLower)) {\r\n        //     return true;\r\n        //   }\r\n\r\n        //   const description = this._fieldResolver.getFieldForSchedule(schedule, 'Description');\r\n        //   if (description?.toLowerCase().includes(searchTermLower)) {\r\n        //     return true;\r\n        //   }\r\n\r\n        //  const faultCode = this._fieldResolver.getFieldForSchedule(schedule, 'FaultCode');\r\n        //   if (faultCode?.toString().includes(searchTermLower)) {\r\n        //     return true;\r\n        //   }\r\n\r\n        //   return false;\r\n      });\r\n    }\r\n\r\n    return filteredSchedules;\r\n  }\r\n\r\n  private _schedulerMatched(searchTerm: string, schedule: Schedule): boolean {\r\n    const searchTermLower = searchTerm.trim().toLowerCase();\r\n    return Object.values(schedule).some(value => {\r\n      if (typeof value === 'string') {\r\n        return value.toLowerCase().includes(searchTermLower);\r\n      } else if (typeof value === 'number') {\r\n        return value.toString().includes(searchTermLower);\r\n      }\r\n      return false;\r\n    });\r\n  }\r\n\r\n  private async _checkDayOfMonth(recurringTask: RecurringTask): Promise<boolean> {\r\n    if (recurringTask.frequency.frequencyType === FrequencyType.Monthly) {\r\n      if (recurringTask.frequency.recurrence.day > 28) {\r\n        const confirmationData: GdcoConfirmationData = {\r\n          title: 'Notice',\r\n          text: `Some months have fewer than ${recurringTask.frequency.recurrence.day} days. For these months, the occurrence will fall on the last day of the month.`,\r\n          width: 500,\r\n          acceptButtonText: 'OK',\r\n          rejectButtonText: 'Cancel'\r\n        };\r\n\r\n        return await awaitable(this._confirmationDialog.confirm(confirmationData), null);\r\n      }\r\n    }\r\n\r\n    return true;\r\n  }\r\n\r\n  private _getCampusName(campus?: string | null): string {\r\n    return campus || 'Unknown';\r\n  }\r\n\r\n  private _createTemplateVersionKey(templateId: string, version: number): string {\r\n    if (!templateId || !version) {\r\n      return null;\r\n    }\r\n\r\n    return `${templateId}:${version}`;\r\n  }\r\n}\r\n"], "mappings": ";;;;;;;;;;;AAAA;;;;AAKA,SAASA,aAAa,EAAiBC,sBAAsB,QAAyB,cAAc;AACpG,SAAuBC,qBAAqB,QAAQ,yBAAyB;AAC7E,SAASC,oBAAoB,QAAQ,2CAA2C;AAChF,SAASC,kBAAkB,QAAQ,wBAAwB;AAE3D,SAASC,QAAQ,QAAQ,YAAY;AACrC,SAASC,gBAAgB,EAAEC,iBAAiB,QAAQ,cAAc;AAClE,SACEC,iBAAiB,EACjBC,sBAAsB,EAEtBC,eAAe,EACfC,SAAS,QACJ,YAAY;AACnB,SAEEC,kBAAkB,EAClBC,iBAAiB,EAKjBC,YAAY,EACZC,YAAY,QACP,2CAA2C;AAClD,SAWEC,qBAAqB,EACrBC,aAAa,EAEbC,iCAAiC,EAEjCC,gBAAgB,QACX,sCAAsC;AAC7C,SACEC,MAAM,EACNC,gBAAgB,EAChBC,oBAAoB,EACpBC,UAAU,EACVC,aAAa,EACbC,SAAS,EACTC,WAAW,EACXC,SAAS,EACTC,QAAQ,EACRC,YAAY,EACZC,YAAY,QACP,aAAa;AACpB,SAASC,0BAA0B,QAAQ,mBAAmB;AAC9D,SAAqBC,aAAa,QAAQ,MAAM;AAChD,SAASC,GAAG,QAAQ,gBAAgB;;;;;;;;;AAEpC;;;AAIA,OAAM,MAAOC,sBAAsB;EAmCjC;EACA,IAAIC,SAASA,CAAA;IACX,OAAO,IAAI,CAACC,iBAAiB,CAACC,UAAU,CAACC,GAAG,CAAC,IAAI,CAACC,KAAK,CAACC,KAAK,CAACC,WAAW,CAAC;EAC5E;EAEA;EACA,IAAIC,SAASA,CAAA;IACX,OAAO,IAAI,CAACN,iBAAiB,CAACM,SAAS,CAACC,WAAW,CAAC,IAAI,CAACJ,KAAK,CAACC,KAAK,CAACI,WAAW,CAAC;EACnF;EAEA;EACA,IAAIC,SAASA,CAAA;IACX,OAAO,IAAI,CAACT,iBAAiB,CAACS,SAAS,CAACF,WAAW,CAAC,IAAI,CAACJ,KAAK,CAACC,KAAK,CAACM,WAAW,CAAC;EACnF;EAEA,IAAIC,QAAQA,CAAA;IACV,OAAO,IAAI,CAACX,iBAAiB,CAACS,SAAS,CAACP,GAAG,CAAC,IAAI,CAACU,oBAAoB,CAACR,KAAK,CAACS,UAAU,CAAC;EACzF;EAEA;EACA,IAAIC,SAASA,CAAA;IACX,OAAO,IAAI,CAACd,iBAAiB,CAACc,SAAS,CAACP,WAAW,CAAC,IAAI,CAACJ,KAAK,CAACC,KAAK,CAACW,mBAAmB,CAAC;EAC3F;EAEAC,YACkBb,KAAyB,EACzBS,oBAA0C,EAClDK,KAAe,EACfC,mBAA2C,EAC3CC,cAAiC,EACjCC,YAA6B,EAC7BpB,iBAAmC,EACnCqB,kBAAqC,EACrCC,mBAAuC,EACvCC,aAA2B,EAC3BC,aAA2B,EAC3BC,cAA0C;IAXlC,KAAAtB,KAAK,GAALA,KAAK;IACL,KAAAS,oBAAoB,GAApBA,oBAAoB;IAC5B,KAAAK,KAAK,GAALA,KAAK;IACL,KAAAC,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAApB,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAqB,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,cAAc,GAAdA,cAAc;IAEtB,IAAI,CAACC,UAAU,GAAG,IAAI,CAAC1B,iBAAiB,CAACC,UAAU,CAAC0B,OAAO,CAAC,IAAI,CAACxB,KAAK,CAACyB,YAAY,CAAC;IACpF,IAAI,CAACC,UAAU,GAAG,IAAI,CAAC7B,iBAAiB,CAACM,SAAS,CAACwB,eAAe,CAAC,IAAI,CAAC3B,KAAK,CAAC4B,YAAY,CAAC;IAC3F,IAAI,CAACC,gBAAgB,GAAG,IAAI,CAACH,UAAU,CAACI,IAAI,CAAC3C,WAAW,CAAC4C,QAAQ,IAAIA,QAAQ,CAAC9B,KAAK,KAAKvB,aAAa,CAACsD,MAAM,CAAC,CAAC;IAE9G,IAAI,CAACC,sBAAsB,GAAG,IAAI,CAACP,UAAU,CAACI,IAAI,CAChDpC,GAAG,CAACS,SAAS,IAAI,CAAC,CAACA,SAAS,EAAE+B,IAAI,CAACH,QAAQ,IAAIA,QAAQ,CAAC9B,KAAK,KAAKvB,aAAa,CAACyD,QAAQ,CAAC,CAAC,CAC3F;IAED,IAAI,CAACC,kBAAkB,GAAG3C,aAAa,CAAC,CACtC,IAAI,CAACiC,UAAU,EACf,IAAI,CAAC1B,KAAK,CAACqC,eAAe,EAC1B,IAAI,CAACrC,KAAK,CAACsC,eAAe,EAC1B,IAAI,CAACtC,KAAK,CAACuC,uBAAuB,EAClC,IAAI,CAACvC,KAAK,CAACwC,WAAW,CACvB,CAAC,CAACV,IAAI,CACLpC,GAAG,CAAC,CAAC,CAACS,SAAS,EAAEsC,cAAc,EAAEC,MAAM,EAAEC,sBAAsB,EAAEC,UAAU,CAAC,KAAI;MAC9E,OAAO,IAAI,CAACC,gBAAgB,CAAC1C,SAAS,EAAEsC,cAAc,EAAEC,MAAM,EAAEC,sBAAsB,EAAEC,UAAU,CAAC;IACrG,CAAC,CAAC,CACH;IAED,IAAI,CAACE,YAAY,GAAG,IAAI,CAACV,kBAAkB,CAACN,IAAI,CAC9C3C,WAAW,CAAC4C,QAAQ,IAAIA,QAAQ,CAACgB,SAAS,KAAK,IAAI,CAACjC,KAAK,CAACkC,WAAW,CAACN,MAAM,CAAC,EAC7EtD,SAAS,CAAC,CAAC6D,CAAC,EAAEC,CAAC,KAAI;MACjB,MAAMC,WAAW,GAAG,IAAI,CAAC7B,cAAc,CAAC8B,mBAAmB,CAACH,CAAC,EAAE,gBAAgB,CAAC,IAAI,EAAE;MACtF,MAAMI,WAAW,GAAG,IAAI,CAAC/B,cAAc,CAAC8B,mBAAmB,CAACF,CAAC,EAAE,gBAAgB,CAAC,IAAI,EAAE;MAEtF;MACA,OAAOC,WAAW,CAACG,WAAW,EAAE,CAACC,aAAa,CAACF,WAAW,CAACC,WAAW,EAAE,CAAC;IAC3E,CAAC,CAAC,EACFlE,SAAS,CAAC,CAAC6D,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACO,IAAI,CAACF,WAAW,EAAE,CAACC,aAAa,CAACL,CAAC,CAACM,IAAI,CAACF,WAAW,EAAE,CAAC,CAAC,CAC9E;IAED,IAAI,CAACG,SAAS,GAAG,IAAI,CAAC5D,iBAAiB,CAACS,SAAS,CAACkB,OAAO,CAAC,IAAI,CAACf,oBAAoB,CAACiD,WAAW,CAAC;IAChG,IAAI,CAACC,UAAU,GAAG,IAAI,CAAC9D,iBAAiB,CAACS,SAAS,CAACqB,eAAe,CAAC,IAAI,CAAC3B,KAAK,CAAC4D,YAAY,CAAC;IAC3F,IAAI,CAACC,kBAAkB,GAAG,IAAI,CAAChE,iBAAiB,CAACM,SAAS,CAACwB,eAAe,CAAC,IAAI,CAAC3B,KAAK,CAAC8D,oBAAoB,CAAC;IAC3G,IAAI,CAACC,kBAAkB,GAAG,IAAI,CAAClE,iBAAiB,CAACS,SAAS,CAACqB,eAAe,CAAC,IAAI,CAAC3B,KAAK,CAACgE,oBAAoB,CAAC;IAE3G,IAAI,CAACC,qBAAqB,GAAG,IAAI,CAACpE,iBAAiB,CAACqE,gBAAgB,CAAC1C,OAAO,CAC1E/B,aAAa,CAAC,CAAC,IAAI,CAACgB,oBAAoB,CAACiD,WAAW,EAAE,IAAI,CAACjD,oBAAoB,CAAC0D,oBAAoB,CAAC,CAAC,CAACrC,IAAI,CACzGpC,GAAG,CAAC,CAAC,CAACgB,UAAU,EAAE0D,OAAO,CAAC,KAAK,IAAI,CAACC,yBAAyB,CAAC3D,UAAU,EAAE0D,OAAO,CAAC,CAAC,CACpF,CACF;IACD,IAAI,CAACE,qBAAqB,GAAG,IAAI,CAACzE,iBAAiB,CAACqE,gBAAgB,CAAC1C,OAAO,CAC1E/B,aAAa,CAAC,CAAC,IAAI,CAACgB,oBAAoB,CAACiD,WAAW,EAAE,IAAI,CAACjD,oBAAoB,CAAC8D,oBAAoB,CAAC,CAAC,CAACzC,IAAI,CACzGpC,GAAG,CAAC,CAAC,CAACgB,UAAU,EAAE0D,OAAO,CAAC,KAAK,IAAI,CAACC,yBAAyB,CAAC3D,UAAU,EAAE0D,OAAO,CAAC,CAAC,CACpF,CACF;IAED,IAAI,CAACI,cAAc,GAAG,IAAI,CAAC3E,iBAAiB,CAACM,SAAS,CAACwB,eAAe,CAAC,IAAI,CAAC3B,KAAK,CAACyE,gBAAgB,CAAC;IACnG,IAAI,CAACC,kBAAkB,GAAG,IAAI,CAAC7E,iBAAiB,CAACc,SAAS,CAACgB,eAAe,CAAC,IAAI,CAAC3B,KAAK,CAAC2E,oBAAoB,CAAC;IAE3G,IAAI,CAACC,yBAAyB,GAAG,IAAI,CAAC/E,iBAAiB,CAACc,SAAS,CAACa,OAAO,CAAC,IAAI,CAACxB,KAAK,CAAC6E,2BAA2B,CAAC;IACjH,IAAI,CAACC,iCAAiC,GAAG,IAAI,CAACjF,iBAAiB,CAACM,SAAS,CAACqB,OAAO,CAC/E,IAAI,CAACoD,yBAAyB,CAAC9C,IAAI,CAACpC,GAAG,CAACqF,QAAQ,IAAIA,QAAQ,EAAEC,UAAU,CAAC,CAAC,CAC3E;IACD,IAAI,CAACC,8BAA8B,GAAG,IAAI,CAAC5D,aAAa,CAAC6D,KAAK,CAACvD,eAAe,CAC5E,IAAI,CAAC3B,KAAK,CAACmF,gCAAgC,CAC5C;IAED,IAAI,CAACC,iBAAiB,GAAG3F,aAAa,CAAC,CAAC,IAAI,CAACkE,UAAU,EAAE,IAAI,CAAC3D,KAAK,CAACqF,kBAAkB,CAAC,CAAC,CAACvD,IAAI,CAC3FpC,GAAG,CAAC,CAAC,CAACY,SAAS,EAAEgF,SAAS,CAAC,KAAI;MAC7B,IAAI,CAAChF,SAAS,IAAI,CAACgF,SAAS,EAAE;QAC5B,OAAO,IAAI;;MAGb,OAAOhF,SAAS,CAAC4B,IAAI,CAAC1B,QAAQ,IAAI,IAAI,CAAC+E,0BAA0B,CAAC/E,QAAQ,EAAE8E,SAAS,CAAC,CAAC;IACzF,CAAC,CAAC,CACH;IAED,IAAI,CAACD,kBAAkB,GAAG,IAAI,CAACnE,kBAAkB,CAACsE,UAAU,CAAChE,OAAO,CAClE,IAAI,CAACxB,KAAK,CAACqF,kBAAkB,CAACvD,IAAI,CAACpC,GAAG,CAAC4F,SAAS,IAAIA,SAAS,EAAEG,QAAQ,EAAE,CAAC,CAAC,CAC5E;IAED;IACA,IAAI,CAACC,iBAAiB,GAAGjG,aAAa,CAAC,CAAC,IAAI,CAACiC,UAAU,EAAE,IAAI,CAACiC,UAAU,CAAC,CAAC,CAAC7B,IAAI,CAC7EpC,GAAG,CAAC,CAAC,CAACS,SAAS,CAAC,KAAK,IAAI,CAACwF,yBAAyB,CAACxF,SAAS,CAAC,CAAC,CAChE;IAED;IACA,IAAI,CAACyF,yBAAyB,GAAGnG,aAAa,CAAC,CAAC,IAAI,CAAC2C,kBAAkB,EAAE,IAAI,CAACuB,UAAU,CAAC,CAAC,CAAC7B,IAAI,CAC7FpC,GAAG,CAAC,CAAC,CAACS,SAAS,CAAC,KAAK,IAAI,CAACwF,yBAAyB,CAACxF,SAAS,CAAC,CAAC,CAChE;IAED,MAAM0F,gBAAgB,GAAG,IAAI,CAACnE,UAAU,CAACI,IAAI,CAC3CzC,QAAQ,CAAC0C,QAAQ,IAAIA,QAAQ,CAACgB,SAAS,CAAC,EACxCzD,YAAY,EAAE,CACf;IAED,IAAI,CAACwG,uBAAuB,GAAG,IAAI,CAAC1E,aAAa,CAAC2E,KAAK,CACpDpE,eAAe,CAACkE,gBAAgB,CAAC,CACjC/D,IAAI,CAAC1C,SAAS,CAAC,CAAC6D,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAAC+C,WAAW,CAAC1C,WAAW,EAAE,CAACC,aAAa,CAACL,CAAC,CAAC8C,WAAW,CAAC1C,WAAW,EAAE,CAAC,CAAC,CAAC;IAEpG,IAAI,CAAC2C,WAAW,GAAG,IAAI,CAAC/E,kBAAkB,CAACsE,UAAU,CAACU,UAAU,EAAE;IAClE,IAAI,CAACC,SAAS,GAAG,IAAI,CAACjF,kBAAkB,CAACiF,SAAS;IAElD,IAAI,CAACC,aAAa,GAAG,IAAI,CAACH,WAAW,CAACnE,IAAI,CAACvC,YAAY,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;IAEtE,IAAI,CAAC8G,mBAAmB,GAAG5G,aAAa,CAAC,CAAC,IAAI,CAACkE,UAAU,EAAE,IAAI,CAACsC,WAAW,CAAC,CAAC,CAACnE,IAAI,CAChFpC,GAAG,CAAC,CAAC,CAACY,SAAS,EAAEkF,UAAU,CAAC,KAAI;MAC9B,IAAI,CAAClF,SAAS,IAAI,CAACkF,UAAU,EAAE;QAC7B,OAAO,EAAE;;MAGX,OAAOA,UAAU,CACdc,MAAM,CAAChB,SAAS,IAAG;QAClB,OAAOhF,SAAS,CAAC4B,IAAI,CAAC1B,QAAQ,IAAG;UAC/B,OACE,IAAI,CAAC+E,0BAA0B,CAAC/E,QAAQ,EAAE8E,SAAS,CAACiB,IAAI,CAAC,IAAI,CAAC/F,QAAQ,CAACgG,UAAU,EAAEC,iBAAiB;QAExG,CAAC,CAAC;MACJ,CAAC,CAAC,CACDC,IAAI,CAAC,CAACzD,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAAC0D,WAAW,CAACrD,WAAW,EAAE,CAACC,aAAa,CAACL,CAAC,CAACyD,WAAW,CAACrD,WAAW,EAAE,CAAC,CAAC;IAC3F,CAAC,CAAC,CACH;IAED;IACA,IAAI,CAACsD,kBAAkB,GAAGnH,aAAa,CAAC,CAAC,IAAI,CAACiG,iBAAiB,EAAE,IAAI,CAACtD,kBAAkB,EAAE,IAAI,CAACuB,UAAU,CAAC,CAAC,CAAC7B,IAAI,CAC9GpC,GAAG,CAAC,CAAC,CAACmH,QAAQ,EAAE1G,SAAS,CAAC,KAAI;MAC5B,IAAI,CAAC0G,QAAQ,IAAI,CAAC1G,SAAS,EAAE;QAC3B,OAAO,EAAE;;MAGX,OAAO0G,QAAQ,CAACC,MAAM,CAAmC,CAACC,IAAI,EAAEC,IAAI,KAAI;QACtED,IAAI,CAACC,IAAI,CAAC,GAAG7G,SAAS,CACnBmG,MAAM,CAACvE,QAAQ,IAAG;UACjB,MAAMkF,MAAM,GAAG,IAAI,CAAC3F,cAAc,CAAC8B,mBAAmB,CAACrB,QAAQ,EAAE,gBAAgB,CAAC;UAElF,OAAO,IAAI,CAACmF,cAAc,CAACD,MAAM,CAAC,KAAKD,IAAI;QAC7C,CAAC,CAAC,CACDN,IAAI,CAAC,CAACzD,CAAC,EAAEC,CAAC,KAAI;UACb,MAAMC,WAAW,GAAG,IAAI,CAAC7B,cAAc,CAAC8B,mBAAmB,CAACH,CAAC,EAAE,gBAAgB,CAAC,IAAI,EAAE;UACtF,MAAMI,WAAW,GAAG,IAAI,CAAC/B,cAAc,CAAC8B,mBAAmB,CAACF,CAAC,EAAE,gBAAgB,CAAC,IAAI,EAAE;UAEtF;UACA,OAAOC,WAAW,CAACG,WAAW,EAAE,CAACC,aAAa,CAACF,WAAW,CAACC,WAAW,EAAE,CAAC;QAC3E,CAAC,CAAC,CACDoD,IAAI,CAAC,CAACzD,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACO,IAAI,CAACF,WAAW,EAAE,CAACC,aAAa,CAACL,CAAC,CAACM,IAAI,CAACF,WAAW,EAAE,CAAC,CAAC;QAE3E,OAAOyD,IAAI;MACb,CAAC,EAAE,EAAE,CAAC;IACR,CAAC,CAAC,CACH;IAED,IAAI,CAACI,cAAc,GAAG,IAAI,CAACzF,UAAU,CAACI,IAAI,CAACvC,YAAY,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;IAE5E,IAAI,CAAC6H,aAAa,GAAG,IAAI,CAACvH,iBAAiB,CAACM,SAAS,CAACqB,OAAO,CAAC,IAAI,CAACxB,KAAK,CAACqH,eAAe,CAAC;IACzF,IAAI,CAACC,qBAAqB,GAAG,IAAI,CAACzH,iBAAiB,CAACS,SAAS,CAACkB,OAAO,CACnE,IAAI,CAAC4F,aAAa,CAACtF,IAAI,CAACpC,GAAG,CAACqC,QAAQ,IAAIA,QAAQ,EAAErB,UAAU,CAAC,CAAC,CAC/D;IAED,IAAI,CAAC6G,kBAAkB,GAAG,IAAI,CAAC1H,iBAAiB,CAACM,SAAS,CAACqB,OAAO,CAAC,IAAI,CAACxB,KAAK,CAACwH,oBAAoB,CAAC;IACnG,IAAI,CAACC,0BAA0B,GAAG,IAAI,CAAC5H,iBAAiB,CAACS,SAAS,CAACkB,OAAO,CACxE,IAAI,CAAC+F,kBAAkB,CAACzF,IAAI,CAACpC,GAAG,CAACqC,QAAQ,IAAIA,QAAQ,EAAErB,UAAU,CAAC,CAAC,CACpE;IAED;IACA,IAAI,CAACgH,iBAAiB,GAAGjI,aAAa,CAAC,CAAC,IAAI,CAACiC,UAAU,EAAE,IAAI,CAACiC,UAAU,CAAC,CAAC,CAAC7B,IAAI,CAC7EpC,GAAG,CAAC,CAAC,CAACS,SAAS,CAAC,KAAI;MAClB,OAAOA,SAAS,CAAC2G,MAAM,CAA4C,CAACC,IAAI,EAAEC,IAAI,KAAI;QAChFD,IAAI,CAACC,IAAI,CAAChC,UAAU,CAAC,GAAG;UACtB2C,cAAc,EAAE,IAAI,CAACrG,cAAc,CAAC8B,mBAAmB,CAAC4D,IAAI,EAAE,gBAAgB,CAAC;UAC/EY,cAAc,EAAE,IAAI,CAACtG,cAAc,CAAC8B,mBAAmB,CAAC4D,IAAI,EAAE,gBAAgB,CAAC;UAC/Ea,UAAU,EAAE,IAAI,CAACvG,cAAc,CAAC8B,mBAAmB,CAAC4D,IAAI,EAAE,YAAY,CAAC;UACvEc,KAAK,EAAE,IAAI,CAACxG,cAAc,CAAC8B,mBAAmB,CAAC4D,IAAI,EAAE,OAAO,CAAC;UAC7De,UAAU,EAAE,IAAI,CAACzG,cAAc,CAAC8B,mBAAmB,CAAC4D,IAAI,EAAE,YAAY,CAAC;UACvEgB,gBAAgB,EAAE,IAAI,CAAC1G,cAAc,CAAC8B,mBAAmB,CAAC4D,IAAI,EAAE,kBAAkB;SACnF;QAED,OAAOD,IAAI;MACb,CAAC,EAAE,EAAE,CAAC;IACR,CAAC,CAAC,CACH;EACH;EAEA;;;;EAKMkB,cAAcA,CAACC,OAAqC;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACxD,MAAMlI,WAAW,GAAGgI,OAAO,CAACG,OAAO;MAEnCF,KAAI,CAACnI,KAAK,CAACsI,mBAAmB,CAACpI,WAAW,CAAC;MAE3C,MAAMiI,KAAI,CAACI,aAAa,CAACL,OAAO,CAAC;MACjC,MAAMM,OAAO,CAACC,GAAG,CAAC,CAACN,KAAI,CAACO,aAAa,CAACR,OAAO,CAAC,EAAEC,KAAI,CAACQ,aAAa,CAACT,OAAO,CAAC,CAAC,CAAC;MAE7E,MAAMC,KAAI,CAACS,YAAY,CAACV,OAAO,CAAC;IAAC;EACnC;EAEA;;;;EAKMW,2CAA2CA,CAACX,OAAqC;IAAA,IAAAY,MAAA;IAAA,OAAAV,iBAAA;MACrF,MAAMpD,UAAU,GAAGkD,OAAO,CAACG,OAAO;MAElCS,MAAI,CAAC9I,KAAK,CAAC+I,oBAAoB,EAAE;MACjC,MAAMD,MAAI,CAACE,qBAAqB,CAAC,IAAIjK,oBAAoB,CAAC,CAACiG,UAAU,CAAC,CAAC,CAAC;MAExE,MAAMjD,QAAQ,GAAG+G,MAAI,CAACjJ,iBAAiB,CAACM,SAAS,CAACJ,GAAG,CAACiF,UAAU,CAAC;MACjE,IAAIjD,QAAQ,EAAE9B,KAAK,KAAKvB,aAAa,CAACyD,QAAQ,EAAE;QAC9C2G,MAAI,CAAC9I,KAAK,CAAC2C,sBAAsB,CAAC,IAAI,CAAC;;IACxC;EACH;EAEA;;;;EAKM4F,aAAaA,CAACL,OAAqC;IAAA,IAAAe,MAAA;IAAA,OAAAb,iBAAA;MACvD,IAAI;QACFa,MAAI,CAACjJ,KAAK,CAACkJ,kBAAkB,EAAE;QAE/B,MAAMD,MAAI,CAACpJ,iBAAiB,CAACsJ,gBAAgB,CAACjB,OAAO,CAAC;OACvD,CAAC,OAAOkB,KAAK,EAAE;QACd,IAAI,CAACtK,gBAAgB,CAACsK,KAAK,CAAC,EAAE;UAC5BH,MAAI,CAACjI,cAAc,CAACqI,QAAQ,CAAC,2BAA2B,EAAED,KAAK,CAAC;;QAGlE,MAAMA,KAAK,CAAC,CAAC;OACd,SAAS;QACRH,MAAI,CAACjJ,KAAK,CAACsJ,gBAAgB,EAAE;;IAC9B;EACH;EAEA;EAEMZ,aAAaA,CAACR,OAAsB;IAAA,IAAAqB,MAAA;IAAA,OAAAnB,iBAAA;MACxC,IAAI/H,WAAW,GAAa,EAAE;MAC9B,MAAM;QAAEH;MAAW,CAAE,GAAGqJ,MAAI,CAACvJ,KAAK,CAACC,KAAK;MAExCsJ,MAAI,CAACvJ,KAAK,CAACwJ,kBAAkB,EAAE;MAE/B;MACA,MAAMD,MAAI,CAAChB,aAAa,CAAC,IAAIxJ,oBAAoB,CAACmB,WAAW,EAAEgI,OAAO,CAAC,CAAC;MAExE,IAAI;QACF,MAAMG,OAAO,GAAsC;UACjDnI,WAAW,EAAEA;SACd;QAED,MAAMC,SAAS,SAASoJ,MAAI,CAAC1J,iBAAiB,CAAC4J,0BAA0B,CACvE,IAAI1K,oBAAoB,CAACsJ,OAAO,EAAEH,OAAO,CAAC,CAC3C;QAED;QACA,MAAMqB,MAAI,CAACpI,mBAAmB,CAACuI,eAAe,CAACxB,OAAO,CAAC;QAEvD7H,WAAW,GAAGF,SAAS,CAACT,GAAG,CAACqC,QAAQ,IAAIA,QAAQ,CAACiD,UAAU,CAAC;OAC7D,CAAC,OAAOoE,KAAK,EAAE;QACd,IAAI,CAACtK,gBAAgB,CAACsK,KAAK,CAAC,EAAE;UAC5BG,MAAI,CAACvI,cAAc,CAACqI,QAAQ,CAAC,0CAA0C,EAAED,KAAK,CAAC;;QAGjF,MAAMA,KAAK,CAAC,CAAC;OACd,SAAS;QACRG,MAAI,CAACvJ,KAAK,CAAC2J,gBAAgB,CAACtJ,WAAW,CAAC;;IACzC;EACH;EAEA;EAEMsI,aAAaA,CAACT,OAAsB;IAAA,IAAA0B,MAAA;IAAA,OAAAxB,iBAAA;MACxC,IAAI7H,WAAW,GAAa,EAAE;MAC9B,MAAM;QAAEL;MAAW,CAAE,GAAG0J,MAAI,CAAC5J,KAAK,CAACC,KAAK;MAExC2J,MAAI,CAAC5J,KAAK,CAAC6J,kBAAkB,EAAE;MAE/B;MACA,MAAMD,MAAI,CAACrB,aAAa,CAAC,IAAIxJ,oBAAoB,CAACmB,WAAW,EAAEgI,OAAO,CAAC,CAAC;MAExE,IAAI;QACF,MAAM/H,SAAS,SAASyJ,MAAI,CAAC/J,iBAAiB,CAACiK,kCAAkC,CAC/E,IAAI/K,oBAAoB,CAACmB,WAAW,EAAEgI,OAAO,CAAC,CAC/C;QAED3H,WAAW,GAAGJ,SAAS,CAACT,GAAG,CAACqC,QAAQ,IAAIA,QAAQ,CAACgI,kBAAkB,CAAC;OACrE,CAAC,OAAOX,KAAK,EAAE;QACd,IAAI,CAACtK,gBAAgB,CAACsK,KAAK,CAAC,EAAE;UAC5BQ,MAAI,CAAC5I,cAAc,CAACqI,QAAQ,CAAC,oCAAoC,EAAED,KAAK,CAAC;;QAG3E,MAAMA,KAAK,CAAC,CAAC;OACd,SAAS;QACRQ,MAAI,CAAC5J,KAAK,CAACgK,gBAAgB,CAACzJ,WAAW,CAAC;;IACzC;EACH;EAGM0J,oBAAoBA,CAAC/B,OAAqC;IAAA,IAAAgC,MAAA;IAAA,OAAA9B,iBAAA;MAC9D,MAAM1H,UAAU,GAAGwH,OAAO,CAACG,OAAO;MAClC,MAAM;QAAEnI;MAAW,CAAE,GAAGgK,MAAI,CAAClK,KAAK,CAACC,KAAK;MAExC,IAAI;QACF,MAAMoI,OAAO,GAA4C;UACvDnI,WAAW,EAAEA,WAAW;UACxBQ,UAAU,EAAEA;SACb;QAED,MAAMJ,SAAS,SAAS4J,MAAI,CAACrK,iBAAiB,CAACsK,gCAAgC,CAC7E,IAAIpL,oBAAoB,CAACsJ,OAAO,EAAEH,OAAO,CAAC,CAC3C;QAEDgC,MAAI,CAACzJ,oBAAoB,CAAC2J,gBAAgB,CAACF,MAAI,CAAC1J,QAAQ,CAAC4D,OAAO,EAAE8F,MAAI,CAAC1J,QAAQ,CAAC4D,OAAO,GAAG,CAAC,CAAC;OAC7F,CAAC,OAAOgF,KAAK,EAAE;QACd,IAAI,CAACtK,gBAAgB,CAACsK,KAAK,CAAC,EAAE;UAC5Bc,MAAI,CAAClJ,cAAc,CAACqI,QAAQ,CAAC,oCAAoC,EAAED,KAAK,CAAC;;QAG3E,MAAMA,KAAK,CAAC,CAAC;OACd,SAAS,C;IACT;EACH;EAEA;;;;EAKMJ,qBAAqBA,CAACd,OAAuC;IAAA,IAAAmC,MAAA;IAAA,OAAAjC,iBAAA;MACjE,MAAM/H,WAAW,GAAG6H,OAAO,CAACG,OAAO;MACnC,MAAM;QAAEnI,WAAW;QAAEoK;MAAQ,CAAE,GAAGD,MAAI,CAACrK,KAAK,CAACC,KAAK;MAClD,IAAIW,mBAAmB,GAAa,EAAE;MAEtCyJ,MAAI,CAACrK,KAAK,CAACuK,0BAA0B,CAAClK,WAAW,CAAC;MAElD;MACA,MAAMgK,MAAI,CAAC9B,aAAa,CAAC,IAAIxJ,oBAAoB,CAACmB,WAAW,EAAEgI,OAAO,CAAC,CAAC;MAExE,IAAI;QACF,MAAMsC,QAAQ,GAAkC,EAAE;QAElD;QACA,KAAK,MAAMxF,UAAU,IAAI3E,WAAW,EAAE;UACpC,MAAMgI,OAAO,GAA6C;YACxDnI,WAAW,EAAEA,WAAW;YACxB8E,UAAU,EAAEA;WACb;UAED;UACA,MAAMyF,OAAO,GAAGJ,MAAI,CAACxK,iBAAiB,CAAC6K,iCAAiC,CACtE,IAAI3L,oBAAoB,CAACsJ,OAAO,EAAEH,OAAO,CAAC,CAC3C;UAEDsC,QAAQ,CAACG,IAAI,CAACF,OAAO,CAAC;UAEtB;UACA,MAAM1I,QAAQ,GAAGsI,MAAI,CAACxK,iBAAiB,CAACM,SAAS,CAACJ,GAAG,CAACiF,UAAU,CAAC;UACjE,MAAM4F,SAAS,GAAG7I,QAAQ,EAAE8I,iBAAiB,CAACD,SAAS;UACvD,MAAME,OAAO,GAAG/I,QAAQ,EAAE8I,iBAAiB,CAACC,OAAO;UACnD,IACE,CAAC/I,QAAQ,IACRuI,QAAQ,CAACS,WAAW,EAAE,IAAIH,SAAS,CAACG,WAAW,EAAE,KAC/C,CAACD,OAAO,IAAIR,QAAQ,CAACS,WAAW,EAAE,IAAID,OAAO,CAACC,WAAW,EAAE,CAAE,EAChE;YACA,MAAMC,YAAY,GAAiD;cACjE9K,WAAW,EAAEA,WAAW;cACxB8E,UAAU,EAAEA,UAAU;cACtBiG,QAAQ,EAAEX;aACX;YAED;YACA,MAAMY,aAAa,GAAGb,MAAI,CAACxK,iBAAiB,CAACsL,qCAAqC,CAChF,IAAIpM,oBAAoB,CAACiM,YAAY,EAAE9C,OAAO,CAAC,CAChD;YAEDsC,QAAQ,CAACG,IAAI,CAACO,aAAa,CAAC;;;QAIhC,MAAME,OAAO,SAAS5C,OAAO,CAACC,GAAG,CAAC+B,QAAQ,CAAC;QAE3C5J,mBAAmB,GAAGwK,OAAO,CAACtE,MAAM,CAAW,CAACC,IAAI,EAAEC,IAAI,KAAI;UAC5D,OAAOD,IAAI,CAACsE,MAAM,CAAC,GAAGrE,IAAI,CAACtH,GAAG,CAACqF,QAAQ,IAAIA,QAAQ,CAACuG,kBAAkB,CAAC,CAAC;QAC1E,CAAC,EAAE,EAAE,CAAC;QAEN1K,mBAAmB,GAAGxC,SAAS,CAACmN,QAAQ,CAAC3K,mBAAmB,CAAC;OAC9D,CAAC,OAAOwI,KAAK,EAAE;QACd,IAAI,CAACtK,gBAAgB,CAACsK,KAAK,CAAC,EAAE;UAC5BiB,MAAI,CAACrJ,cAAc,CAACqI,QAAQ,CAAC,oCAAoC,EAAED,KAAK,CAAC;;QAG3E,MAAMA,KAAK,CAAC,CAAC;OACd,SAAS;QACRiB,MAAI,CAACrK,KAAK,CAACwL,wBAAwB,CAAC5K,mBAAmB,CAAC;;IACzD;EACH;EAEA;EAEM6K,yBAAyBA,CAACvD,OAAmC;IAAA,IAAAwD,MAAA;IAAA,OAAAtD,iBAAA;MACjE,MAAM6C,QAAQ,GAAG/C,OAAO,CAACG,OAAO;MAChC,MAAM;QAAEnI;MAAW,CAAE,GAAGwL,MAAI,CAAC1L,KAAK,CAACC,KAAK;MACxC,IAAIW,mBAAmB,GAAa,EAAE;MAEtC;MACA,MAAM8K,MAAI,CAACnD,aAAa,CAAC,IAAIxJ,oBAAoB,CAACmB,WAAW,EAAEgI,OAAO,CAAC,CAAC;MAExE,IAAI;QACF,MAAMsC,QAAQ,GAAkC,EAAE;QAElD;QACA,KAAK,MAAMxF,UAAU,IAAI0G,MAAI,CAAC1L,KAAK,CAACC,KAAK,CAAC0L,eAAe,EAAE;UACzD,MAAM5J,QAAQ,GAAG2J,MAAI,CAAC7L,iBAAiB,CAACM,SAAS,CAACJ,GAAG,CAACiF,UAAU,CAAC;UACjE,MAAM4F,SAAS,GAAG7I,QAAQ,EAAE8I,iBAAiB,CAACD,SAAS;UACvD,MAAME,OAAO,GAAG/I,QAAQ,EAAE8I,iBAAiB,CAACC,OAAO;UAEnD;UACA,IACE,CAAC/I,QAAQ,IACRkJ,QAAQ,CAACF,WAAW,EAAE,IAAIH,SAAS,CAACG,WAAW,EAAE,KAC/C,CAACD,OAAO,IAAIG,QAAQ,CAACF,WAAW,EAAE,IAAID,OAAO,CAACC,WAAW,EAAE,CAAE,EAChE;YACA,MAAM1C,OAAO,GAAiD;cAC5DnI,WAAW,EAAEA,WAAW;cACxB8E,UAAU,EAAEA,UAAU;cACtBiG,QAAQ,EAAEA;aACX;YAED,MAAMR,OAAO,GAAGiB,MAAI,CAAC7L,iBAAiB,CAACsL,qCAAqC,CAC1E,IAAIpM,oBAAoB,CAACsJ,OAAO,EAAEH,OAAO,CAAC,CAC3C;YAEDsC,QAAQ,CAACG,IAAI,CAACF,OAAO,CAAC;;;QAI1B,MAAMW,OAAO,SAAS5C,OAAO,CAACC,GAAG,CAAC+B,QAAQ,CAAC;QAE3C5J,mBAAmB,GAAGwK,OAAO,CAACtE,MAAM,CAAW,CAACC,IAAI,EAAEC,IAAI,KAAI;UAC5D,OAAOD,IAAI,CAACsE,MAAM,CAAC,GAAGrE,IAAI,CAACtH,GAAG,CAACqF,QAAQ,IAAIA,QAAQ,CAACuG,kBAAkB,CAAC,CAAC;QAC1E,CAAC,EAAE,EAAE,CAAC;OACP,CAAC,OAAOlC,KAAK,EAAE;QACd,IAAI,CAACtK,gBAAgB,CAACsK,KAAK,CAAC,EAAE;UAC5BsC,MAAI,CAAC1K,cAAc,CAACqI,QAAQ,CAAC,oCAAoC,EAAED,KAAK,CAAC;;QAG3E,MAAMA,KAAK,CAAC,CAAC;OACd,SAAS;QACRsC,MAAI,CAAC1L,KAAK,CAAC4L,4BAA4B,CAAChL,mBAAmB,EAAEqK,QAAQ,CAAC;;IACvE;EACH;EAEA;EAEMrC,YAAYA,CAACV,OAAsB;IAAA,IAAA2D,MAAA;IAAA,OAAAzD,iBAAA;MACvC,MAAMjI,SAAS,GAAG0L,MAAI,CAAChM,iBAAiB,CAACM,SAAS,CAACC,WAAW,CAACyL,MAAI,CAAC7L,KAAK,CAACC,KAAK,CAACI,WAAW,CAAC;MAC5F,MAAMyL,OAAO,GAAG3L,SAAS,CAACT,GAAG,CAACqC,QAAQ,IAAIA,QAAQ,CAACgB,SAAS,CAAC;MAE7D,MAAMyF,OAAO,CAACC,GAAG,CAAC,CAChBoD,MAAI,CAAC3K,kBAAkB,CAAC6K,gBAAgB,CAAC7D,OAAO,CAAC,EACjD2D,MAAI,CAAC3K,kBAAkB,CAAC8K,cAAc,CAAC9D,OAAO,CAAC,EAC/C2D,MAAI,CAAC1K,mBAAmB,CAACuI,eAAe,CAACxB,OAAO,CAAC,EACjD2D,MAAI,CAACzK,aAAa,CAAC6K,aAAa,CAAC,IAAIlN,oBAAoB,CAAC+M,OAAO,EAAE5D,OAAO,CAAC,CAAC,CAC7E,CAAC;IAAC;EACL;EAEA;;;;EAKMgE,cAAcA,CAAChE,OAAuC;IAAA,IAAAiE,OAAA;IAAA,OAAA/D,iBAAA;MAC1D,MAAMrG,QAAQ,GAAGmG,OAAO,CAACG,OAAO;MAEhC,IAAI;QACF,MAAM+D,UAAU,GAAyB;UACvCC,KAAK,EAAE,uBAAuB;UAC9BC,IAAI,EAAE;SACP;QAED;QACA,MAAMC,SAAS,SAASrN,SAAS,CAACiN,OAAI,CAACpL,mBAAmB,CAACyL,OAAO,CAACJ,UAAU,CAAC,EAAElE,OAAO,CAACuE,iBAAiB,CAAC;QAC1G,IAAI,CAACF,SAAS,EAAE;UACd,OAAO,KAAK;;QAGd,MAAMlE,OAAO,GAA0B;UACrCnI,WAAW,EAAE6B,QAAQ,CAAC7B,WAAW;UACjC8E,UAAU,EAAEjD,QAAQ,CAACiD;SACtB;QAED,MAAMmH,OAAI,CAACtM,iBAAiB,CAACqM,cAAc,CAAC,IAAInN,oBAAoB,CAACsJ,OAAO,EAAEH,OAAO,CAAC,CAAC;QAEvFiE,OAAI,CAAClL,YAAY,CAACyL,UAAU,CAAC,iBAAiB,EAAE;UAC9CxM,WAAW,EAAE6B,QAAQ,CAAC7B,WAAW;UACjCQ,UAAU,EAAEqB,QAAQ,CAACrB,UAAU;UAC/BiM,UAAU,EAAER,OAAI,CAAC7K,cAAc,CAAC8B,mBAAmB,CAACrB,QAAQ,EAAE,gBAAgB,CAAC;UAC/E6K,cAAc,EAAE7K,QAAQ,CAAC8I,iBAAiB,CAAC+B;SAC5C,CAAC;QAEF,MAAMC,gBAAgB,GAAGV,OAAI,CAACxL,SAAS,CACpC2F,MAAM,CAACvB,QAAQ,IAAIhD,QAAQ,CAACiD,UAAU,KAAKD,QAAQ,CAACC,UAAU,CAAC,CAC/DtF,GAAG,CAACqF,QAAQ,IAAIA,QAAQ,CAACuG,kBAAkB,CAAC;QAE/C;QACAa,OAAI,CAACnM,KAAK,CAAC8M,oBAAoB,EAAE;QACjCX,OAAI,CAACnM,KAAK,CAAC+M,eAAe,CAACF,gBAAgB,CAAC;OAC7C,CAAC,OAAOzD,KAAK,EAAE;QACd,IAAI,CAACtK,gBAAgB,CAACsK,KAAK,CAAC,EAAE;UAC5B+C,OAAI,CAACnL,cAAc,CAACqI,QAAQ,CAAC,iCAAiC,EAAED,KAAK,CAAC;;QAGxE,MAAMA,KAAK,CAAC,CAAC;;MAGf,OAAO,IAAI;IAAC;EACd;EAEA;EAEM4D,uBAAuBA,CAAC9E,OAAsB;IAAA,IAAA+E,OAAA;IAAA,OAAA7E,iBAAA;MAClD,MAAM8E,UAAU,GAAa,EAAE;MAC/B,MAAM;QAAEhN,WAAW;QAAEiN;MAAmB,CAAE,GAAGF,OAAI,CAACjN,KAAK,CAACC,KAAK;MAE7D,IAAI;QACF,MAAMmM,UAAU,GAAyB;UACvCC,KAAK,EAAE,iCAAiC;UACxCC,IAAI,EAAE;SACP;QAED;QACA,MAAMC,SAAS,SAASrN,SAAS,CAAC+N,OAAI,CAAClM,mBAAmB,CAACyL,OAAO,CAACJ,UAAU,CAAC,EAAElE,OAAO,CAACuE,iBAAiB,CAAC;QAC1G,IAAI,CAACF,SAAS,EAAE;UACd;;QAGFU,OAAI,CAACjN,KAAK,CAACoN,4BAA4B,EAAE;QAEzC,MAAM5C,QAAQ,GAAmB,EAAE;QAEnC;QACA,KAAK,MAAMxF,UAAU,IAAImI,mBAAmB,EAAE;UAC5C,MAAM9E,OAAO,GAA0B;YACrCnI,WAAW,EAAEA,WAAW;YACxB8E,UAAU,EAAEA;WACb;UAED,MAAMyF,OAAO,GAAGwC,OAAI,CAACpN,iBAAiB,CAACqM,cAAc,CAAC,IAAInN,oBAAoB,CAACsJ,OAAO,EAAEH,OAAO,CAAC,CAAC;UACjGsC,QAAQ,CAACG,IAAI,CAACF,OAAO,CAAC;UAEtByC,UAAU,CAACvC,IAAI,CAAC3F,UAAU,CAAC;;QAG7B,MAAMwD,OAAO,CAACC,GAAG,CAAC+B,QAAQ,CAAC;QAE3B,MAAM6C,kBAAkB,GAAGJ,OAAI,CAACtM,SAAS,CACtC2F,MAAM,CAACvB,QAAQ,IAAImI,UAAU,CAACI,QAAQ,CAACvI,QAAQ,CAACC,UAAU,CAAC,CAAC,CAC5DtF,GAAG,CAACqF,QAAQ,IAAIA,QAAQ,CAACuG,kBAAkB,CAAC;QAE/C2B,OAAI,CAACjN,KAAK,CAAC+M,eAAe,CAACM,kBAAkB,CAAC;QAE9CJ,OAAI,CAAChM,YAAY,CAACyL,UAAU,CAAC,sBAAsB,EAAE;UACnDxM,WAAW,EAAEA,WAAW;UACxBqN,KAAK,EAAEJ,mBAAmB,CAACK,MAAM,CAAC/H,QAAQ,EAAE;UAC5CgI,IAAI,EAAE;SACP,CAAC;QAEF;QACAR,OAAI,CAACjN,KAAK,CAAC8M,oBAAoB,EAAE;OAClC,CAAC,OAAO1D,KAAK,EAAE;QACd,IAAI,CAACtK,gBAAgB,CAACsK,KAAK,CAAC,EAAE;UAC5B6D,OAAI,CAACjM,cAAc,CAACqI,QAAQ,CAAC,2CAA2C,EAAED,KAAK,CAAC;;QAGlF,MAAMA,KAAK,CAAC,CAAC;OACd,SAAS;QACR6D,OAAI,CAACjN,KAAK,CAAC0N,0BAA0B,CAACR,UAAU,CAAC;;IAClD;EACH;EAEA;EAEMS,qBAAqBA,CAACzF,OAAsB;IAAA,IAAA0F,OAAA;IAAA,OAAAxF,iBAAA;MAChD,MAAM8E,UAAU,GAAa,EAAE;MAC/B,MAAM;QAAEhN,WAAW;QAAEyL;MAAe,CAAE,GAAGiC,OAAI,CAAC5N,KAAK,CAACC,KAAK;MAEzD,IAAI;QACF,MAAMmM,UAAU,GAAyB;UACvCC,KAAK,EAAE,iCAAiC;UACxCC,IAAI,EAAE;SACP;QAED;QACA,MAAMC,SAAS,SAASrN,SAAS,CAAC0O,OAAI,CAAC7M,mBAAmB,CAACyL,OAAO,CAACJ,UAAU,CAAC,EAAElE,OAAO,CAACuE,iBAAiB,CAAC;QAC1G,IAAI,CAACF,SAAS,EAAE;UACd;;QAGFqB,OAAI,CAAC5N,KAAK,CAAC6N,0BAA0B,EAAE;QAEvC,MAAMrD,QAAQ,GAAmB,EAAE;QAEnC;QACA,KAAK,MAAMxF,UAAU,IAAI2G,eAAe,EAAE;UACxC,MAAMtD,OAAO,GAA0B;YACrCnI,WAAW,EAAEA,WAAW;YACxB8E,UAAU,EAAEA;WACb;UAED,MAAMyF,OAAO,GAAGmD,OAAI,CAAC/N,iBAAiB,CAACqM,cAAc,CAAC,IAAInN,oBAAoB,CAACsJ,OAAO,EAAEH,OAAO,CAAC,CAAC;UACjGsC,QAAQ,CAACG,IAAI,CAACF,OAAO,CAAC;UAEtByC,UAAU,CAACvC,IAAI,CAAC3F,UAAU,CAAC;;QAG7B,MAAMwD,OAAO,CAACC,GAAG,CAAC+B,QAAQ,CAAC;QAE3B,MAAM6C,kBAAkB,GAAGO,OAAI,CAACjN,SAAS,CACtC2F,MAAM,CAACvB,QAAQ,IAAImI,UAAU,CAACI,QAAQ,CAACvI,QAAQ,CAACC,UAAU,CAAC,CAAC,CAC5DtF,GAAG,CAACqF,QAAQ,IAAIA,QAAQ,CAACuG,kBAAkB,CAAC;QAE/CsC,OAAI,CAAC5N,KAAK,CAAC+M,eAAe,CAACM,kBAAkB,CAAC;QAE9CO,OAAI,CAAC3M,YAAY,CAACyL,UAAU,CAAC,sBAAsB,EAAE;UACnDxM,WAAW,EAAEA,WAAW;UACxBqN,KAAK,EAAE5B,eAAe,CAAC6B,MAAM,CAAC/H,QAAQ,EAAE;UACxCgI,IAAI,EAAE;SACP,CAAC;OACH,CAAC,OAAOrE,KAAK,EAAE;QACd,IAAI,CAACtK,gBAAgB,CAACsK,KAAK,CAAC,EAAE;UAC5BwE,OAAI,CAAC5M,cAAc,CAACqI,QAAQ,CAAC,2CAA2C,EAAED,KAAK,CAAC;;QAGlF,MAAMA,KAAK,CAAC,CAAC;OACd,SAAS;QACRwE,OAAI,CAAC5N,KAAK,CAAC8N,wBAAwB,CAACZ,UAAU,CAAC;;IAChD;EACH;EAEA;;;;EAKMa,cAAcA,CAAC7F,OAA+C;IAAA,IAAA8F,OAAA;IAAA,OAAA5F,iBAAA;MAClE,MAAM5H,QAAQ,GAAG0H,OAAO,CAACG,OAAO;MAEhC,IAAI;QACF,MAAM+D,UAAU,GAAyB;UACvCC,KAAK,EAAE,iBAAiB;UACxBC,IAAI,EAAE;SACP;QAED;QACA,MAAMC,SAAS,SAASrN,SAAS,CAAC8O,OAAI,CAACjN,mBAAmB,CAACyL,OAAO,CAACJ,UAAU,CAAC,EAAElE,OAAO,CAACuE,iBAAiB,CAAC;QAC1G,IAAI,CAACF,SAAS,EAAE;UACd,OAAO,KAAK;;QAGd,MAAMlE,OAAO,GAAkC;UAC7CnI,WAAW,EAAEM,QAAQ,CAACN,WAAW;UACjCQ,UAAU,EAAEF,QAAQ,CAACuJ;SACtB;QAED,MAAMiE,OAAI,CAACnO,iBAAiB,CAACoO,sBAAsB,CAAC,IAAIlP,oBAAoB,CAACsJ,OAAO,EAAEH,OAAO,CAAC,CAAC;QAE/F;QACA8F,OAAI,CAAChO,KAAK,CAACkO,oBAAoB,EAAE;OAClC,CAAC,OAAO9E,KAAK,EAAE;QACd,IAAI,CAACtK,gBAAgB,CAACsK,KAAK,CAAC,EAAE;UAC5B4E,OAAI,CAAChN,cAAc,CAACqI,QAAQ,CAAC,2BAA2B,EAAED,KAAK,CAAC;;QAGlE,MAAMA,KAAK,CAAC,CAAC;;MAGf,OAAO,IAAI;IAAC;EACd;EAEA;EAEM+E,uBAAuBA,CAACjG,OAAsB;IAAA,IAAAkG,OAAA;IAAA,OAAAhG,iBAAA;MAClD,MAAM8E,UAAU,GAAa,EAAE;MAC/B,MAAM;QAAEhN,WAAW;QAAEmO;MAAmB,CAAE,GAAGD,OAAI,CAACpO,KAAK,CAACC,KAAK;MAE7D,IAAI;QACF,MAAMmM,UAAU,GAAyB;UACvCC,KAAK,EAAE,2BAA2B;UAClCC,IAAI,EAAE;SACP;QAED;QACA,MAAMC,SAAS,SAASrN,SAAS,CAACkP,OAAI,CAACrN,mBAAmB,CAACyL,OAAO,CAACJ,UAAU,CAAC,EAAElE,OAAO,CAACuE,iBAAiB,CAAC;QAC1G,IAAI,CAACF,SAAS,EAAE;UACd;;QAGF6B,OAAI,CAACpO,KAAK,CAACsO,4BAA4B,EAAE;QAEzC,MAAM9D,QAAQ,GAAmB,EAAE;QAEnC;QACA,KAAK,MAAM9J,UAAU,IAAI2N,mBAAmB,EAAE;UAC5C,MAAMhG,OAAO,GAAkC;YAC7CnI,WAAW,EAAEA,WAAW;YACxBQ,UAAU,EAAEA;WACb;UAED,MAAM+J,OAAO,GAAG2D,OAAI,CAACvO,iBAAiB,CAACoO,sBAAsB,CAAC,IAAIlP,oBAAoB,CAACsJ,OAAO,EAAEH,OAAO,CAAC,CAAC;UACzGsC,QAAQ,CAACG,IAAI,CAACF,OAAO,CAAC;UAEtByC,UAAU,CAACvC,IAAI,CAACjK,UAAU,CAAC;;QAG7B,MAAM8H,OAAO,CAACC,GAAG,CAAC+B,QAAQ,CAAC;QAE3B;QACA4D,OAAI,CAACpO,KAAK,CAACkO,oBAAoB,EAAE;OAClC,CAAC,OAAO9E,KAAK,EAAE;QACd,IAAI,CAACtK,gBAAgB,CAACsK,KAAK,CAAC,EAAE;UAC5BgF,OAAI,CAACpN,cAAc,CAACqI,QAAQ,CAAC,qCAAqC,EAAED,KAAK,CAAC;;QAG5E,MAAMA,KAAK,CAAC,CAAC;OACd,SAAS;QACRgF,OAAI,CAACpO,KAAK,CAACuO,0BAA0B,CAACrB,UAAU,CAAC;;IAClD;EACH;EAEA;;;;EAKMsB,sBAAsBA,CAACtG,OAA4C;IAAA,IAAAuG,OAAA;IAAA,OAAArG,iBAAA;MACvE,MAAMsG,aAAa,GAAGxG,OAAO,CAACG,OAAO;MACrC,MAAM;QAAEnI,WAAW;QAAEyO,iBAAiB;QAAEpO;MAAW,CAAE,GAAGkO,OAAI,CAACzO,KAAK,CAACC,KAAK;MAExE,IAAI;QACF,MAAM2O,MAAM,SAASH,OAAI,CAACI,gBAAgB,CAACH,aAAa,CAAC;QACzD,IAAI,CAACE,MAAM,EAAE;UACX,OAAO,KAAK;;QAGdH,OAAI,CAACzO,KAAK,CAAC8O,2BAA2B,EAAE;QAExC,MAAMxO,SAAS,GAAGmO,OAAI,CAAC5O,iBAAiB,CAACS,SAAS,CAACF,WAAW,CAACG,WAAW,CAAC;QAC3E,MAAMC,QAAQ,GAAGF,SAAS,CAAC4B,IAAI,CAAC6M,CAAC,IAAIN,OAAI,CAAClJ,0BAA0B,CAACwJ,CAAC,EAAEJ,iBAAiB,CAAC,CAAC;QAE3F,MAAMrJ,SAAS,GAAGmJ,OAAI,CAACvN,kBAAkB,CAAC8N,qBAAqB,CAACjP,GAAG,CAAC4O,iBAAiB,CAAClJ,QAAQ,EAAE,CAAC;QAEjG,MAAM1D,QAAQ,GAAsB;UAClC,GAAG,IAAIrE,sBAAsB,EAAE,CAACuR,WAAW,CAACP,aAAa,EAAE,IAAI,EAAED,OAAI,CAAC7O,SAAS,CAAC4G,UAAU,EAAE0I,MAAM,CAAC;UACnGhP,WAAW,EAAEA,WAAW;UACxBsD,IAAI,EAAEkL,aAAa,CAAClL,IAAI,IAAI8B,SAAS,CAACqB,WAAW;UACjDjG,UAAU,EAAEF,QAAQ,CAACuJ,kBAAkB;UACvCvD,UAAU,EAAE;YACVlB,SAAS,EAAEqJ;;SAEd;QAED;QACA,IAAInO,QAAQ,CAAC2O,oBAAoB,CAACjN,IAAI,CAACkN,SAAS,IAAIA,SAAS,CAAC5L,IAAI,KAAK,WAAW,CAAC,EAAE;UACnF,MAAM6L,oBAAoB,GAAGtN,QAAQ,CAACuN,iCAAiC,EAAED,oBAAoB;UAC7F,IAAIA,oBAAoB,IAAIA,oBAAoB,CAAC,CAAC,CAAC,EAAE;YACnD,IAAI,CAACA,oBAAoB,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,EAAE;cACzCA,oBAAoB,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,GAAGV,iBAAiB;;;;QAK9D;QACA,IAAInO,QAAQ,CAACgG,UAAU,EAAE+I,yBAAyB,EAAE;UAClDxN,QAAQ,CAAC8I,iBAAiB,CAAC2E,mBAAmB,GAAG,GAAGhP,QAAQ,CAACgG,UAAU,CAAC+I,yBAAyB,CAAC9J,QAAQ,EAAE,WAAW;;QAGzH,MAAMgK,WAAW,SAAShB,OAAI,CAAC5O,iBAAiB,CAAC6P,cAAc,CAAC,IAAI3Q,oBAAoB,CAACgD,QAAQ,EAAEmG,OAAO,CAAC,CAAC;QAE5GuG,OAAI,CAACxN,YAAY,CAACyL,UAAU,CAAC,iBAAiB,EAAE;UAC9CxM,WAAW,EAAEA,WAAW;UACxBQ,UAAU,EAAEF,QAAQ,CAACuJ,kBAAkB;UACvC4C,UAAU,EAAE8B,OAAI,CAACnN,cAAc,CAAC8B,mBAAmB,CAACqM,WAAW,EAAE,gBAAgB,CAAC;UAClFd,iBAAiB,EAAEA,iBAAiB,EAAElJ,QAAQ,EAAE;UAChDmH,cAAc,EAAE7K,QAAQ,CAAC8I,iBAAiB,CAAC+B,cAAc;UACzD+C,mBAAmB,EACjB5N,QAAQ,CAAC8I,iBAAiB,CAAC8E,mBAAmB,GAAG,CAAC,GAC9C5N,QAAQ,CAAC8I,iBAAiB,CAAC8E,mBAAmB,CAAClK,QAAQ,EAAE,GACzDmK,SAAS;UACfC,UAAU,EAAEC,IAAI,CAACC,SAAS,CAAChO,QAAQ,CAAC8I,iBAAiB,CAACgF,UAAU;SACjE,CAAC;QAEFpB,OAAI,CAACzO,KAAK,CAACgQ,WAAW,CAACP,WAAW,CAACzK,UAAU,CAAC;OAC/C,CAAC,OAAOoE,KAAK,EAAE;QACd,IAAI,CAACtK,gBAAgB,CAACsK,KAAK,CAAC,EAAE;UAC5BqF,OAAI,CAACzN,cAAc,CAACqI,QAAQ,CAAC,iCAAiC,EAAED,KAAK,CAAC;;QAGxE,MAAMA,KAAK,CAAC,CAAC;OACd,SAAS;QACRqF,OAAI,CAACzO,KAAK,CAACiQ,yBAAyB,EAAE;;MAGxC,OAAO,IAAI;IAAC;EACd;EAEA;;;;EAKMC,mBAAmBA,CAAChI,OAA4C;IAAA,IAAAiI,OAAA;IAAA,OAAA/H,iBAAA;MACpE,MAAMsG,aAAa,GAAGxG,OAAO,CAACG,OAAO;MACrC,MAAM;QAAEnI,WAAW;QAAEkQ,cAAc;QAAEzB;MAAiB,CAAE,GAAGwB,OAAI,CAACnQ,KAAK,CAACC,KAAK;MAE3E,IAAI;QACF,MAAM2O,MAAM,SAASuB,OAAI,CAACtB,gBAAgB,CAACH,aAAa,CAAC;QACzD,IAAI,CAACE,MAAM,EAAE;UACX,OAAO,KAAK;;QAGduB,OAAI,CAACnQ,KAAK,CAACqQ,wBAAwB,EAAE;QAErC,MAAMhI,OAAO,GAA2B;UACtCnI,WAAW,EAAEA,WAAW;UACxB8E,UAAU,EAAEoL;SACb;QAED,MAAME,gBAAgB,SAASH,OAAI,CAACtQ,iBAAiB,CAAC0Q,eAAe,CAAC,IAAIxR,oBAAoB,CAACsJ,OAAO,EAAEH,OAAO,CAAC,CAAC;QAEjH,MAAM1H,QAAQ,GAAG2P,OAAI,CAACtQ,iBAAiB,CAACS,SAAS,CAACP,GAAG,CAACuQ,gBAAgB,CAAC5P,UAAU,CAAC;QAElF,MAAMqB,QAAQ,GAAG,IAAIrE,sBAAsB,EAAE,CAACuR,WAAW,CACvDP,aAAa,EACb4B,gBAAgB,EAChBH,OAAI,CAACvQ,SAAS,CAAC4G,UAAU,EAAE0I,MAAM,CAClC;QAEDnN,QAAQ,CAACyE,UAAU,GAAG;UACpB,GAAG8J,gBAAgB,CAAC9J;SACrB;QAED;QACA,IAAIhG,QAAQ,CAAC2O,oBAAoB,CAACjN,IAAI,CAACkN,SAAS,IAAIA,SAAS,CAAC5L,IAAI,KAAK,WAAW,CAAC,EAAE;UACnF,MAAM6L,oBAAoB,GAAGtN,QAAQ,CAACuN,iCAAiC,EAAED,oBAAoB;UAC7F,IAAIA,oBAAoB,IAAIA,oBAAoB,CAAC,CAAC,CAAC,EAAE;YACnD,IAAI,CAACA,oBAAoB,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,EAAE;cACzCA,oBAAoB,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,GAAGV,iBAAiB;;;;QAK9D;QACA,IAAInO,QAAQ,CAACgG,UAAU,EAAE+I,yBAAyB,EAAE;UAClDxN,QAAQ,CAAC8I,iBAAiB,CAAC2E,mBAAmB,GAAG,GAAGhP,QAAQ,CAACgG,UAAU,CAAC+I,yBAAyB,CAAC9J,QAAQ,EAAE,WAAW;;QAGzH,MAAM0K,OAAI,CAACtQ,iBAAiB,CAAC2Q,cAAc,CAAC,IAAIzR,oBAAoB,CAACgD,QAAQ,EAAEmG,OAAO,CAAC,CAAC;QAExFiI,OAAI,CAAClP,YAAY,CAACyL,UAAU,CAAC,iBAAiB,EAAE;UAC9CxM,WAAW,EAAEA,WAAW;UACxBQ,UAAU,EAAEF,QAAQ,CAACuJ,kBAAkB;UACvC4C,UAAU,EAAEwD,OAAI,CAAC7O,cAAc,CAAC8B,mBAAmB,CAACrB,QAAQ,EAAE,gBAAgB,CAAC;UAC/E6K,cAAc,EAAE7K,QAAQ,CAAC8I,iBAAiB,CAAC+B;SAC5C,CAAC;QAEF,IAAIuD,OAAI,CAACnQ,KAAK,CAACC,KAAK,CAAC0L,eAAe,CAAC2B,QAAQ,CAACvL,QAAQ,CAACiD,UAAU,CAAC,EAAE;UAClE,MAAMmL,OAAI,CAACnH,qBAAqB,CAAC,IAAIjK,oBAAoB,CAACoR,OAAI,CAACnQ,KAAK,CAACC,KAAK,CAAC0L,eAAe,EAAEzD,OAAO,CAAC,CAAC;;OAExG,CAAC,OAAOkB,KAAK,EAAE;QACd,IAAI,CAACtK,gBAAgB,CAACsK,KAAK,CAAC,EAAE;UAC5B+G,OAAI,CAACnP,cAAc,CAACqI,QAAQ,CAAC,iCAAiC,EAAED,KAAK,CAAC;;QAGxE,MAAMA,KAAK,CAAC,CAAC;OACd,SAAS;QACR+G,OAAI,CAACnQ,KAAK,CAACyQ,sBAAsB,EAAE;;MAGrC,OAAO,IAAI;IAAC;EACd;EAEA;;;;EAKMC,sBAAsBA,CAACxI,OAA2C;IAAA,IAAAyI,OAAA;IAAA,OAAAvI,iBAAA;MACtE,MAAMwI,IAAI,GAAG1I,OAAO,CAACG,OAAO;MAC5B,MAAM;QAAEnI;MAAW,CAAE,GAAGyQ,OAAI,CAAC3Q,KAAK,CAACC,KAAK;MAExC,IAAI;QACF0Q,OAAI,CAAClQ,oBAAoB,CAACoQ,2BAA2B,EAAE;QAEvD,MAAMC,gBAAgB,GAAG,IAAInT,qBAAqB,EAAE,CAACsR,WAAW,CAAC2B,IAAI,EAAE1Q,WAAW,CAAC;QAEnF,IAAI4Q,gBAAgB,CAAC3B,oBAAoB,EAAE;UACzC,MAAM4B,eAAe,GAAGD,gBAAgB,CAAC3B,oBAAoB,CAACjN,IAAI,CAAC8O,KAAK,IAAIA,KAAK,CAACxN,IAAI,KAAK,WAAW,CAAC;UACvG,IAAI,CAACuN,eAAe,EAAE;YACpBD,gBAAgB,CAAC3B,oBAAoB,CAACxE,IAAI,CAAC;cACzCnH,IAAI,EAAE,WAAW;cACjByN,QAAQ,EAAEtS,iCAAiC,CAACuS;aAC7C,CAAC;;;QAIN,MAAMC,WAAW,SAASR,OAAI,CAAC9Q,iBAAiB,CAAC6Q,sBAAsB,CACrE,IAAI3R,oBAAoB,CAAC+R,gBAAgB,EAAE5I,OAAO,CAAC,CACpD;QAEDyI,OAAI,CAAC3Q,KAAK,CAACoR,WAAW,CAACD,WAAW,CAACpH,kBAAkB,CAAC;QAEtD,OAAOoH,WAAW,CAACpH,kBAAkB;OACtC,CAAC,OAAOX,KAAK,EAAE;QACd,IAAI,CAACtK,gBAAgB,CAACsK,KAAK,CAAC,EAAE;UAC5BuH,OAAI,CAAC3P,cAAc,CAACqI,QAAQ,CAAC,2BAA2B,EAAED,KAAK,CAAC;;QAGlE,MAAMA,KAAK,CAAC,CAAC;OACd,SAAS;QACRuH,OAAI,CAAClQ,oBAAoB,CAAC4Q,yBAAyB,EAAE;;IACtD;EACH;EAEA;EAEMC,sBAAsBA,CAACpJ,OAA8C;IAAA,IAAAqJ,OAAA;IAAA,OAAAnJ,iBAAA;MACzE,MAAM;QAAE5H,QAAQ;QAAEgR;MAAU,CAAE,GAAGtJ,OAAO,CAACG,OAAO;MAEhD,IAAIoJ,IAAmB;MAEvB,IAAI;QACFF,OAAI,CAAC9Q,oBAAoB,CAACiR,2BAA2B,EAAE;QAEvD,IAAIlR,QAAQ,CAAC2O,oBAAoB,EAAE;UACjC,MAAM4B,eAAe,GAAGvQ,QAAQ,CAAC2O,oBAAoB,CAACjN,IAAI,CAAC8O,KAAK,IAAIA,KAAK,CAACxN,IAAI,KAAK,WAAW,CAAC;UAC/F,IAAI,CAACuN,eAAe,EAAE;YACpBvQ,QAAQ,CAAC2O,oBAAoB,CAACxE,IAAI,CAAC;cACjCnH,IAAI,EAAE,WAAW;cACjByN,QAAQ,EAAEtS,iCAAiC,CAACuS;aAC7C,CAAC;;;QAIN,MAAM7I,OAAO,GAA0C;UACrD7H,QAAQ,EAAEA,QAAQ;UAClBgR,UAAU,EAAEA;SACb;QAEDC,IAAI,SAAUF,OAAI,CAAC1R,iBAAiB,CAAC8R,8BAA8B,CACjE,IAAI5S,oBAAoB,CAACsJ,OAAO,EAAEH,OAAO,CAAC,CAClC;OACX,CAAC,OAAOkB,KAAK,EAAE;QACd,IAAI,CAACtK,gBAAgB,CAACsK,KAAK,CAAC,EAAE;UAC5BmI,OAAI,CAACvQ,cAAc,CAACqI,QAAQ,CAAC,4BAA4B,EAAED,KAAK,CAAC;;QAGnE,MAAMA,KAAK,CAAC,CAAC;OACd,SAAS;QACRmI,OAAI,CAAC9Q,oBAAoB,CAACmR,yBAAyB,CAACH,IAAI,CAAC;;IAC1D;EACH;EAEA;;;;EAKMI,oBAAoBA,CAAC3J,OAA+C;IAAA,IAAA4J,OAAA;IAAA,OAAA1J,iBAAA;MACxE,MAAM5H,QAAQ,GAAG0H,OAAO,CAACG,OAAO;MAChC,MAAM;QAAEnI;MAAW,CAAE,GAAG4R,OAAI,CAAC9R,KAAK,CAACC,KAAK;MAExC,IAAI;QACF6R,OAAI,CAAC9R,KAAK,CAAC+R,sBAAsB,EAAE;QAEnCvR,QAAQ,CAACN,WAAW,GAAGA,WAAW;QAElC,MAAMiR,WAAW,SAASW,OAAI,CAACjS,iBAAiB,CAAC6Q,sBAAsB,CACrE,IAAI3R,oBAAoB,CAACyB,QAAQ,EAAE0H,OAAO,CAAC,CAC5C;QAED4J,OAAI,CAAC9R,KAAK,CAACoR,WAAW,CAACD,WAAW,CAACpH,kBAAkB,CAAC;OACvD,CAAC,OAAOX,KAAK,EAAE;QACd,IAAI,CAACtK,gBAAgB,CAACsK,KAAK,CAAC,EAAE;UAC5B0I,OAAI,CAAC9Q,cAAc,CAACqI,QAAQ,CAAC,2BAA2B,EAAED,KAAK,CAAC;;QAGlE,MAAMA,KAAK,CAAC,CAAC;OACd,SAAS;QACR0I,OAAI,CAAC9R,KAAK,CAACgS,oBAAoB,EAAE;;IAClC;EACH;EAEA;;;;EAKMC,sBAAsBA,CAAC/J,OAA2C;IAAA,IAAAgK,OAAA;IAAA,OAAA9J,iBAAA;MACtE,MAAM5H,QAAQ,GAAG0H,OAAO,CAACG,OAAO;MAEhC,IAAI;QACF6J,OAAI,CAACzR,oBAAoB,CAACoQ,2BAA2B,EAAE;QAEvD,MAAMC,gBAAgB,GAAG,IAAInT,qBAAqB,EAAE,CAACsR,WAAW,CAC9DzO,QAAQ,EACR0R,OAAI,CAAClS,KAAK,CAACC,KAAK,CAACC,WAAW,EAC5BgS,OAAI,CAACzR,oBAAoB,CAACR,KAAK,CAACS,UAAU,CAC3C;QAED,IAAIoQ,gBAAgB,CAAC3B,oBAAoB,EAAE;UACzC,MAAM4B,eAAe,GAAGD,gBAAgB,CAAC3B,oBAAoB,CAACjN,IAAI,CAAC8O,KAAK,IAAIA,KAAK,CAACxN,IAAI,KAAK,WAAW,CAAC;UACvG,IAAI,CAACuN,eAAe,EAAE;YACpBD,gBAAgB,CAAC3B,oBAAoB,CAACxE,IAAI,CAAC;cACzCnH,IAAI,EAAE,WAAW;cACjByN,QAAQ,EAAEtS,iCAAiC,CAACuS;aAC7C,CAAC;;;QAIN,MAAMgB,OAAI,CAACrS,iBAAiB,CAACoS,sBAAsB,CAAC,IAAIlT,oBAAoB,CAAC+R,gBAAgB,EAAE5I,OAAO,CAAC,CAAC;QAExGgK,OAAI,CAACzR,oBAAoB,CAAC0R,YAAY,EAAE;OACzC,CAAC,OAAO/I,KAAK,EAAE;QACd,IAAI,CAACtK,gBAAgB,CAACsK,KAAK,CAAC,EAAE;UAC5B8I,OAAI,CAAClR,cAAc,CAACqI,QAAQ,CAAC,yBAAyB,EAAED,KAAK,CAAC;;QAGhE,MAAMA,KAAK,CAAC,CAAC;OACd,SAAS;QACR8I,OAAI,CAACzR,oBAAoB,CAAC4Q,yBAAyB,EAAE;;IACtD;EACH;EAEA;;;;EAIAe,iBAAiBA,CAACrQ,QAAkB;IAClC,MAAM2M,aAAa,GAAG,IAAIhR,sBAAsB,EAAE,CAAC2U,OAAO,CAACtQ,QAAQ,CAAC;IAEpE,IAAI,CAAC/B,KAAK,CAACsS,2BAA2B,CAAC5D,aAAa,EAAE3M,QAAQ,CAAC;EACjE;EAEA;;;;EAIAwQ,sBAAsBA,CAACxQ,QAAkB;IACvC,MAAM2M,aAAa,GAAG,IAAIhR,sBAAsB,EAAE,CAAC2U,OAAO,CAACtQ,QAAQ,CAAC;IAEpE,IAAI,CAAC/B,KAAK,CAACwS,gCAAgC,CAAC9D,aAAa,EAAE3M,QAAQ,CAAC;EACtE;EAEA;;;;EAKM0Q,oBAAoBA,CAACvK,OAA+C;IAAA,IAAAwK,OAAA;IAAA,OAAAtK,iBAAA;MACxE,MAAMuK,gBAAgB,GAAGzK,OAAO,CAACG,OAAO;MAExC,IAAI;QACFqK,OAAI,CAAC1S,KAAK,CAAC4S,0BAA0B,CAACD,gBAAgB,CAACrH,kBAAkB,CAAC;QAE1E,IAAIuH,OAAO,GAAa,EAAE;QAC1B,IACEF,gBAAgB,CAAC1S,KAAK,KAAKxB,qBAAqB,CAACqU,SAAS,IAC1DH,gBAAgB,CAAC1S,KAAK,KAAKxB,qBAAqB,CAACsU,kBAAkB,EACnE;UACA,MAAMC,YAAY,GAAG,IAAIjV,gBAAgB,EAAE,CACxCkV,SAAS,CAAC,8BAA8B,EAAEjV,iBAAiB,CAACkV,MAAM,EAAEP,gBAAgB,CAACzS,WAAW,CAAC,CACjGiT,GAAG,CAAC,qBAAqB,EAAEnV,iBAAiB,CAACkV,MAAM,EAAEP,gBAAgB,CAAC3N,UAAU,CAAC,CACjFmO,GAAG,CAAC,6BAA6B,EAAEnV,iBAAiB,CAACkV,MAAM,EAAEP,gBAAgB,CAACrH,kBAAkB,CAAC;UAEpG,MAAMjD,OAAO,GAAuB;YAClC+K,KAAK,EAAEJ,YAAY;YACnBK,IAAI,EAAE;WACP;UAED,MAAMnO,KAAK,SAASwN,OAAI,CAACrR,aAAa,CAACiS,WAAW,CAAC,IAAIvU,oBAAoB,CAACsJ,OAAO,EAAEH,OAAO,CAAC,CAAC;UAE9F2K,OAAO,GAAG3N,KAAK,CAACkG,OAAO,CAAC1L,GAAG,CAAC+R,IAAI,IAAIA,IAAI,CAAC8B,EAAE,CAAC;;QAG9Cb,OAAI,CAAC1S,KAAK,CAACwT,kCAAkC,CAACX,OAAO,CAAC;OACvD,CAAC,OAAOzJ,KAAK,EAAE;QACd,IAAI,CAACtK,gBAAgB,CAACsK,KAAK,CAAC,EAAE;UAC5BsJ,OAAI,CAAC1R,cAAc,CAACqI,QAAQ,CAAC,uCAAuC,EAAED,KAAK,CAAC;;QAG9E,MAAMA,KAAK;;IACZ;EACH;EAEA;;;;EAKMqK,4BAA4BA,CAACvL,OAAuC;IAAA,IAAAwL,OAAA;IAAA,OAAAtL,iBAAA;MACxE,MAAMuL,OAAO,GAAGzL,OAAO,CAACG,OAAO;MAE/B,IAAI;QACF,MAAMuL,gBAAgB,GAAyB;UAC7CvH,KAAK,EAAE,eAAe;UACtBC,IAAI,EAAE;SACP;QAED;QACA,MAAMC,SAAS,SAASrN,SAAS,CAACwU,OAAI,CAAC3S,mBAAmB,CAACyL,OAAO,CAACoH,gBAAgB,CAAC,EAAE1L,OAAO,CAACuE,iBAAiB,CAAC;QAChH,IAAI,CAACF,SAAS,EAAE;UACd;;QAGFmH,OAAI,CAAC1T,KAAK,CAAC6T,iCAAiC,EAAE;QAE9C,MAAMC,gBAAgB,GAAsB;UAC1C,GAAGJ,OAAI,CAAC9T,SAAS;UACjB4G,UAAU,EAAE;YACV,GAAGkN,OAAI,CAAC9T,SAAS,CAAC4G,UAAU;YAC5BuN,OAAO,EAAEJ;;SAEZ;QAED,MAAMD,OAAI,CAAC7T,iBAAiB,CAACmU,eAAe,CAAC,IAAIjV,oBAAoB,CAAC+U,gBAAgB,EAAE5L,OAAO,CAAC,CAAC;QAEjGwL,OAAI,CAAC1T,KAAK,CAACiU,wBAAwB,EAAE;OACtC,CAAC,OAAO7K,KAAK,EAAE;QACd,IAAI,CAACtK,gBAAgB,CAACsK,KAAK,CAAC,EAAE;UAC5BsK,OAAI,CAAC1S,cAAc,CAACqI,QAAQ,CAAC,mCAAmC,EAAED,KAAK,CAAC;;QAG1E,MAAMA,KAAK;OACZ,SAAS;QACRsK,OAAI,CAAC1T,KAAK,CAACkU,+BAA+B,EAAE;;IAC7C;EACH;EAEQ3O,0BAA0BA,CAAC/E,QAA0B,EAAE8E,SAAiB;IAC9E,IAAI,CAAC9E,QAAQ,IAAI,CAACA,QAAQ,CAACgG,UAAU,EAAE;MACrC,OAAO,KAAK;;IAGd,OAAOhG,QAAQ,CAACgG,UAAU,CAAChB,UAAU,EAAE8H,QAAQ,CAAChI,SAAS,CAAC;EAC5D;EAEQK,yBAAyBA,CAACxF,SAAqB;IACrD,IAAI,CAACA,SAAS,EAAE;MACd,OAAO,EAAE;;IAGX,OAAOA,SAAS,CACbT,GAAG,CAACqC,QAAQ,IAAI,IAAI,CAACT,cAAc,CAAC8B,mBAAmB,CAACrB,QAAQ,EAAE,gBAAgB,CAAC,CAAC,CACpFrC,GAAG,CAACuH,MAAM,IAAI,IAAI,CAACC,cAAc,CAACD,MAAM,CAAC,CAAC,CAC1CX,MAAM,CAAC,CAAC6N,KAAK,EAAEC,KAAK,EAAEC,GAAG,KAAKA,GAAG,CAACC,OAAO,CAACH,KAAK,CAAC,KAAKC,KAAK,CAAC,CAC3D1N,IAAI,EAAE;EACX;EAEQ7D,gBAAgBA,CACtB1C,SAAqB,EACrBsC,cAAsB,EACtBC,MAAc,EACdC,sBAA+B,EAC/BC,UAAkB;IAElB,IAAI,CAACzC,SAAS,EAAE;MACd,OAAO,EAAE;;IAGX,IAAIoU,iBAAiB,GAAGpU,SAAS;IACjC,IAAIsC,cAAc,EAAE;MAClB8R,iBAAiB,GAAGA,iBAAiB,CAACjO,MAAM,CAACvE,QAAQ,IAAG;QACtD,MAAMkF,MAAM,GAAG,IAAI,CAAC3F,cAAc,CAAC8B,mBAAmB,CAACrB,QAAQ,EAAE,gBAAgB,CAAC;QAElF,OAAOkF,MAAM,KAAKxE,cAAc;MAClC,CAAC,CAAC;;IAGJ,IAAIC,MAAM,EAAE;MACV6R,iBAAiB,GAAGA,iBAAiB,CAACjO,MAAM,CAACvE,QAAQ,IAAIA,QAAQ,CAACgB,SAAS,KAAKL,MAAM,CAAC;;IAGzF,IAAI,CAACC,sBAAsB,EAAE;MAC3B4R,iBAAiB,GAAGA,iBAAiB,CAACjO,MAAM,CAACvE,QAAQ,IAAIA,QAAQ,CAAC9B,KAAK,KAAKvB,aAAa,CAACsD,MAAM,CAAC;;IAGnG,IAAIY,UAAU,IAAIA,UAAU,CAAC4R,IAAI,EAAE,EAAE;MACnC,MAAMC,eAAe,GAAG7R,UAAU,CAAC4R,IAAI,EAAE,CAAClR,WAAW,EAAE;MACvDiR,iBAAiB,GAAGA,iBAAiB,CAACjO,MAAM,CAACvE,QAAQ,IAAG;QACtD,IAAI,CAAC2S,iBAAiB,CAACD,eAAe,EAAE1S,QAAQ,CAAC;QACjD;QACA;QACA;QACA;QACA;QACA;QACA;QAEA;QACA;QACA;QACA;QAEA;QACA;QACA;QACA;QAEA;QACA;QACA;QACA;QAEA;QACA;QACA;QACA;QAEA;MACF,CAAC,CAAC;;IAGJ,OAAOwS,iBAAiB;EAC1B;EAEQG,iBAAiBA,CAAC9R,UAAkB,EAAEb,QAAkB;IAC9D,MAAM0S,eAAe,GAAG7R,UAAU,CAAC4R,IAAI,EAAE,CAAClR,WAAW,EAAE;IACvD,OAAOqR,MAAM,CAACC,MAAM,CAAC7S,QAAQ,CAAC,CAAC8S,IAAI,CAACV,KAAK,IAAG;MAC1C,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;QAC7B,OAAOA,KAAK,CAAC7Q,WAAW,EAAE,CAACgK,QAAQ,CAACmH,eAAe,CAAC;OACrD,MAAM,IAAI,OAAON,KAAK,KAAK,QAAQ,EAAE;QACpC,OAAOA,KAAK,CAAC1O,QAAQ,EAAE,CAAC6H,QAAQ,CAACmH,eAAe,CAAC;;MAEnD,OAAO,KAAK;IACd,CAAC,CAAC;EACJ;EAEc5F,gBAAgBA,CAACH,aAA4B;IAAA,IAAAoG,OAAA;IAAA,OAAA1M,iBAAA;MACzD,IAAIsG,aAAa,CAACqG,SAAS,CAACC,aAAa,KAAKvX,aAAa,CAACwX,OAAO,EAAE;QACnE,IAAIvG,aAAa,CAACqG,SAAS,CAACG,UAAU,CAACC,GAAG,GAAG,EAAE,EAAE;UAC/C,MAAMvB,gBAAgB,GAAyB;YAC7CvH,KAAK,EAAE,QAAQ;YACfC,IAAI,EAAE,+BAA+BoC,aAAa,CAACqG,SAAS,CAACG,UAAU,CAACC,GAAG,iFAAiF;YAC5JC,KAAK,EAAE,GAAG;YACVC,gBAAgB,EAAE,IAAI;YACtBC,gBAAgB,EAAE;WACnB;UAED,aAAapW,SAAS,CAAC4V,OAAI,CAAC/T,mBAAmB,CAACyL,OAAO,CAACoH,gBAAgB,CAAC,EAAE,IAAI,CAAC;;;MAIpF,OAAO,IAAI;IAAC;EACd;EAEQ1M,cAAcA,CAACD,MAAsB;IAC3C,OAAOA,MAAM,IAAI,SAAS;EAC5B;EAEQ5C,yBAAyBA,CAAC3D,UAAkB,EAAE0D,OAAe;IACnE,IAAI,CAAC1D,UAAU,IAAI,CAAC0D,OAAO,EAAE;MAC3B,OAAO,IAAI;;IAGb,OAAO,GAAG1D,UAAU,IAAI0D,OAAO,EAAE;EACnC;;;mBApzCWzE,sBAAsB,EAAA4V,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAA5X,kBAAA,GAAA0X,EAAA,CAAAC,QAAA,CAAAE,EAAA,CAAA9X,oBAAA,GAAA2X,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAA7X,QAAA,GAAAyX,EAAA,CAAAC,QAAA,CAAAI,EAAA,CAAA1X,sBAAA,GAAAqX,EAAA,CAAAC,QAAA,CAAAI,EAAA,CAAA3X,iBAAA,GAAAsX,EAAA,CAAAC,QAAA,CAAAI,EAAA,CAAAzX,eAAA,GAAAoX,EAAA,CAAAC,QAAA,CAAAK,EAAA,CAAAjX,gBAAA,GAAA2W,EAAA,CAAAC,QAAA,CAAAM,EAAA,CAAAxX,iBAAA,GAAAiX,EAAA,CAAAC,QAAA,CAAAM,EAAA,CAAAzX,kBAAA,GAAAkX,EAAA,CAAAC,QAAA,CAAAM,EAAA,CAAAtX,YAAA,GAAA+W,EAAA,CAAAC,QAAA,CAAAM,EAAA,CAAAvX,YAAA,GAAAgX,EAAA,CAAAC,QAAA,CAAAO,EAAA,CAAAvW,0BAAA;AAAA;;SAAtBG,sBAAsB;EAAAqW,OAAA,EAAtBrW,sBAAsB,CAAAsW,IAAA;EAAAC,UAAA,EADT;AAAM;AA6PxBC,UAAA,EADLtX,MAAM,CAAC;EAAEuX,IAAI,EAAEpX,UAAU,CAACqX;AAAgB,CAAE,CAAC,E,sEAChBtX,oBAAoB,I,oGASjD;AAOKoX,UAAA,EADLtX,MAAM,CAAC;EAAEuX,IAAI,EAAEpX,UAAU,CAACqX;AAAgB,CAAE,CAAC,E,sEACatX,oBAAoB,I,iIAU9E;AAOKoX,UAAA,EADLtX,MAAM,CAAC;EAAEuX,IAAI,EAAEpX,UAAU,CAACsX;AAAe,CAAE,CAAC,E,sEAChBvX,oBAAoB,I,mGAchD;AAIKoX,UAAA,EADLtX,MAAM,CAAC;EAAEuX,IAAI,EAAEpX,UAAU,CAACsX;AAAe,CAAE,CAAC,E,sEAChBrX,aAAa,I,mGA+BzC;AAIKkX,UAAA,EADLtX,MAAM,CAAC;EAAEuX,IAAI,EAAEpX,UAAU,CAACsX;AAAe,CAAE,CAAC,E,sEAChBrX,aAAa,I,mGAwBzC;AAGKkX,UAAA,EADLtX,MAAM,EAAE,E,sEAC2BE,oBAAoB,I,0GAuBvD;AAOKoX,UAAA,EADLtX,MAAM,CAAC;EAAEuX,IAAI,EAAEpX,UAAU,CAACqX;AAAgB,CAAE,CAAC,E,sEACTtX,oBAAoB,I,2GAmExD;AAIKoX,UAAA,EADLtX,MAAM,CAAC;EAAEuX,IAAI,EAAEpX,UAAU,CAACsX;AAAe,CAAE,CAAC,E,sEACJvX,oBAAoB,I,+GAmD5D;AAIKoX,UAAA,EADLtX,MAAM,CAAC;EAAEuX,IAAI,EAAEpX,UAAU,CAACsX;AAAe,CAAE,CAAC,E,sEACjBrX,aAAa,I,kGAUxC;AAOKkX,UAAA,EADLtX,MAAM,EAAE,E,sEACqBE,oBAAoB,I,oGA6CjD;AAIKoX,UAAA,EADLtX,MAAM,EAAE,E,sEAC8BI,aAAa,I,6GA0DnD;AAIKkX,UAAA,EADLtX,MAAM,EAAE,E,sEAC4BI,aAAa,I,2GAuDjD;AAOKkX,UAAA,EADLtX,MAAM,EAAE,E,sEACqBE,oBAAoB,I,oGAiCjD;AAIKoX,UAAA,EADLtX,MAAM,EAAE,E,sEAC8BI,aAAa,I,6GA8CnD;AAOKkX,UAAA,EADLtX,MAAM,EAAE,E,sEAC6BE,oBAAoB,I,4GAqEzD;AAOKoX,UAAA,EADLtX,MAAM,EAAE,E,sEAC0BE,oBAAoB,I,yGAqEtD;AAOKoX,UAAA,EADLtX,MAAM,EAAE,E,sEAC6BE,oBAAoB,I,4GAmCzD;AAIKoX,UAAA,EADLtX,MAAM,EAAE,E,sEAC6BE,oBAAoB,I,4GAmCzD;AAOKoX,UAAA,EADLtX,MAAM,EAAE,E,sEAC2BE,oBAAoB,I,0GAuBvD;AAOKoX,UAAA,EADLtX,MAAM,EAAE,E,sEAC6BE,oBAAoB,I,4GAkCzD;AA2BKoX,UAAA,EADLtX,MAAM,CAAC;EAAEuX,IAAI,EAAEpX,UAAU,CAACqX;AAAgB,CAAE,CAAC,E,sEACVtX,oBAAoB,I,0GAkCvD;AAOKoX,UAAA,EADLtX,MAAM,EAAE,E,sEACmCE,oBAAoB,I,kHAqC/D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}