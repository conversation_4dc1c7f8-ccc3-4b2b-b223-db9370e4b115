/**
 * @license
 * Copyright (c) Microsoft Corporation. All rights reserved.
 */

import { Component, ChangeDetectionStrategy, Input, Output, EventEmitter } from '@angular/core';
import { MatCheckboxChange } from '@angular/material/checkbox';
import { MatSelectChange } from '@angular/material/select';
import { GdcoAuth } from '@gdco/auth';
import { AzureGraphObject } from '@gdco/core-reference-systems/gdco-service';
import { Schedule } from '@gdco/reference-systems/gdco-service';

@Component({
  selector: 'tasks-scheduler-instances-actions',
  templateUrl: './instances-actions.component.html',
  styleUrls: ['./instances-actions.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ScheduleInstancesActionsComponent {
  @Input() selectedSchedules: Schedule[];
  @Input() deleting = false;
  @Input() campuses: string[];
  @Input() selectedCampuses: string[] = [];
  @Input() users: AzureGraphObject[];
  @Input() selectedUser: string;
  @Input() hasCompletedSchedules = false;
  @Input() showCompletedSchedules = false;

  @Output() delete: EventEmitter<void> = new EventEmitter();
  @Output() new: EventEmitter<void> = new EventEmitter();
  @Output() campusSelected: EventEmitter<string[]> = new EventEmitter();
  @Output() userSelected: EventEmitter<string> = new EventEmitter();
  @Output() showCompletedSchedulesChange: EventEmitter<boolean> = new EventEmitter();

  get userId(): string {
    return this._auth.currentUser.userId;
  }

  constructor(private _auth: GdcoAuth) {}

  onNewRecurringTaskClicked(): void {
    this.new.emit();
  }

  onDeleteClicked(): void {
    this.delete.emit();
  }

  onCampusSelectionChanged(event: MatSelectChange): void {
    this.campusSelected.emit(event.value);
  }

  onUserSelectionChanged(event: MatSelectChange): void {
    this.userSelected.emit(event.value);
  }

  selectedUserPrincipalName(): string {
    if (!this.selectedUser || !this.users) {
      return null;
    }

    const selected = this.users.find(user => user.ObjectId === this.selectedUser);
    if (selected) {
      return selected.UserPrincipalName;
    }

    return null;
  }

  onShowCompletedSchedulesChanged(event: MatCheckboxChange): void {
    this.showCompletedSchedulesChange.emit(event.checked);
  }
}
