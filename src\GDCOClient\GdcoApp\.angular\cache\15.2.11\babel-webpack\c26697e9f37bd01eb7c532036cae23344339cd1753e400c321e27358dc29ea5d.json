{"ast": null, "code": "/**\r\n * @license\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n */\nimport { EventEmitter } from '@angular/core';\nimport { GdcoAuth } from '@gdco/auth';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@gdco/auth\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"ngx-flexible-layout/flex\";\nimport * as i4 from \"@angular/material/core\";\nimport * as i5 from \"@angular/material/button\";\nimport * as i6 from \"@angular/material/checkbox\";\nimport * as i7 from \"@angular/material/form-field\";\nimport * as i8 from \"@angular/material/icon\";\nimport * as i9 from \"@angular/material/select\";\nimport * as i10 from \"@angular/router\";\nimport * as i11 from \"@gdco/common\";\nconst _c0 = function (a0) {\n  return {\n    option: a0\n  };\n};\nfunction ScheduleInstancesActionsComponent_mat_option_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 15);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const campus_r3 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", campus_r3)(\"gdcoTrackData\", i0.ɵɵpureFunction1(3, _c0, campus_r3));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(campus_r3);\n  }\n}\nconst _c1 = function () {\n  return {\n    option: \"User\"\n  };\n};\nfunction ScheduleInstancesActionsComponent_mat_option_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 12)(1, \"span\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const user_r4 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", user_r4.ObjectId)(\"gdcoTrackData\", i0.ɵɵpureFunction0(3, _c1));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(user_r4.DisplayName);\n  }\n}\nfunction ScheduleInstancesActionsComponent_mat_checkbox_26_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-checkbox\", 16);\n    i0.ɵɵlistener(\"change\", function ScheduleInstancesActionsComponent_mat_checkbox_26_Template_mat_checkbox_change_0_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.onShowCompletedSchedulesChanged($event));\n    });\n    i0.ɵɵtext(1, \"Show completed schedules\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"checked\", ctx_r2.showCompletedSchedules);\n  }\n}\nconst _c2 = function () {\n  return {\n    view: \"calendar\"\n  };\n};\nconst _c3 = function (a0) {\n  return {\n    count: a0\n  };\n};\nconst _c4 = function () {\n  return {\n    option: \"Anyone\"\n  };\n};\nconst _c5 = function () {\n  return {\n    option: \"Me\"\n  };\n};\nexport class ScheduleInstancesActionsComponent {\n  get userId() {\n    return this._auth.currentUser.userId;\n  }\n  constructor(_auth) {\n    this._auth = _auth;\n    this.deleting = false;\n    this.selectedCampuses = [];\n    this.hasCompletedSchedules = false;\n    this.showCompletedSchedules = false;\n    this.delete = new EventEmitter();\n    this.new = new EventEmitter();\n    this.campusSelected = new EventEmitter();\n    this.userSelected = new EventEmitter();\n    this.showCompletedSchedulesChange = new EventEmitter();\n  }\n  onNewRecurringTaskClicked() {\n    this.new.emit();\n  }\n  onDeleteClicked() {\n    this.delete.emit();\n  }\n  onCampusSelectionChanged(event) {\n    this.campusSelected.emit(event.value);\n  }\n  onUserSelectionChanged(event) {\n    this.userSelected.emit(event.value);\n  }\n  selectedUserPrincipalName() {\n    if (!this.selectedUser || !this.users) {\n      return null;\n    }\n    const selected = this.users.find(user => user.ObjectId === this.selectedUser);\n    if (selected) {\n      return selected.UserPrincipalName;\n    }\n    return null;\n  }\n  onShowCompletedSchedulesChanged(event) {\n    this.showCompletedSchedulesChange.emit(event.checked);\n  }\n}\nScheduleInstancesActionsComponent.ɵfac = function ScheduleInstancesActionsComponent_Factory(t) {\n  return new (t || ScheduleInstancesActionsComponent)(i0.ɵɵdirectiveInject(i1.GdcoAuth));\n};\nScheduleInstancesActionsComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: ScheduleInstancesActionsComponent,\n  selectors: [[\"tasks-scheduler-instances-actions\"]],\n  inputs: {\n    selectedSchedules: \"selectedSchedules\",\n    deleting: \"deleting\",\n    campuses: \"campuses\",\n    selectedCampuses: \"selectedCampuses\",\n    users: \"users\",\n    selectedUser: \"selectedUser\",\n    hasCompletedSchedules: \"hasCompletedSchedules\",\n    showCompletedSchedules: \"showCompletedSchedules\"\n  },\n  outputs: {\n    delete: \"delete\",\n    new: \"new\",\n    campusSelected: \"campusSelected\",\n    userSelected: \"userSelected\",\n    showCompletedSchedulesChange: \"showCompletedSchedulesChange\"\n  },\n  decls: 27,\n  vars: 18,\n  consts: [[\"wrap\", \"wrap\"], [\"fxLayout\", \"row wrap\", \"fxLayoutGap\", \"16px\", 1, \"actions\"], [\"mat-flat-button\", \"\", \"color\", \"primary\", \"gdcoTrack\", \"NewRecurringTaskClicked\", 1, \"template-action-button\", 3, \"gdcoTrackData\", \"click\"], [\"mat-stroked-button\", \"\", \"color\", \"primary\", \"gdcoTrack\", \"DeleteSchedulesClicked\", 1, \"template-action-button\", 3, \"disabled\", \"gdcoTrackData\", \"click\"], [\"text\", \"Delete\", \"workingText\", \"Deleting\", 3, \"working\"], [\"mat-stroked-button\", \"\", \"color\", \"primary\", \"routerLink\", \"..\", \"gdcoTrack\", \"ViewListClicked\", 1, \"template-action-button\"], [1, \"filter-item\"], [1, \"filter-label\"], [1, \"gdco-compact\"], [\"gdcoMatSelectAccessibility\", \"\", \"aria-label\", \"Select campus\", \"placeholder\", \"All\", \"multiple\", \"\", 3, \"value\", \"selectionChange\"], [\"gdcoTrack\", \"ScheduleCampusFilter\", 3, \"value\", \"gdcoTrackData\", 4, \"ngFor\", \"ngForOf\"], [\"gdcoMatSelectAccessibility\", \"\", \"aria-label\", \"Created by\", \"placeholder\", \"Anyone\", 3, \"value\", \"selectionChange\"], [\"gdcoTrack\", \"ScheduleCreatedByFilter\", 3, \"value\", \"gdcoTrackData\"], [\"gdcoTrack\", \"ScheduleCreatedByFilter\", 3, \"value\", \"gdcoTrackData\", 4, \"ngFor\", \"ngForOf\"], [3, \"checked\", \"change\", 4, \"ngIf\"], [\"gdcoTrack\", \"ScheduleCampusFilter\", 3, \"value\", \"gdcoTrackData\"], [3, \"checked\", \"change\"]],\n  template: function ScheduleInstancesActionsComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"gdco-action-bar\", 0)(1, \"div\", 1)(2, \"button\", 2);\n      i0.ɵɵlistener(\"click\", function ScheduleInstancesActionsComponent_Template_button_click_2_listener() {\n        return ctx.onNewRecurringTaskClicked();\n      });\n      i0.ɵɵtext(3, \" New recurring task \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(4, \"button\", 3);\n      i0.ɵɵlistener(\"click\", function ScheduleInstancesActionsComponent_Template_button_click_4_listener() {\n        return ctx.onDeleteClicked();\n      });\n      i0.ɵɵelement(5, \"gdco-spinner-button-content\", 4);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(6, \"a\", 5)(7, \"mat-icon\");\n      i0.ɵɵtext(8, \"view_list\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵtext(9, \" View list\");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(10, \"div\", 6)(11, \"gdco-label\", 7);\n      i0.ɵɵtext(12, \"Campus:\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(13, \"mat-form-field\", 8)(14, \"mat-select\", 9);\n      i0.ɵɵlistener(\"selectionChange\", function ScheduleInstancesActionsComponent_Template_mat_select_selectionChange_14_listener($event) {\n        return ctx.onCampusSelectionChanged($event);\n      });\n      i0.ɵɵtemplate(15, ScheduleInstancesActionsComponent_mat_option_15_Template, 2, 5, \"mat-option\", 10);\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(16, \"div\", 6)(17, \"gdco-label\", 7);\n      i0.ɵɵtext(18, \"Created by:\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(19, \"mat-form-field\", 8)(20, \"mat-select\", 11);\n      i0.ɵɵlistener(\"selectionChange\", function ScheduleInstancesActionsComponent_Template_mat_select_selectionChange_20_listener($event) {\n        return ctx.onUserSelectionChanged($event);\n      });\n      i0.ɵɵelementStart(21, \"mat-option\", 12);\n      i0.ɵɵtext(22, \"Anyone\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(23, \"mat-option\", 12);\n      i0.ɵɵtext(24, \"Me\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(25, ScheduleInstancesActionsComponent_mat_option_25_Template, 3, 4, \"mat-option\", 13);\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵtemplate(26, ScheduleInstancesActionsComponent_mat_checkbox_26_Template, 2, 1, \"mat-checkbox\", 14);\n      i0.ɵɵelementEnd();\n    }\n    if (rf & 2) {\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"gdcoTrackData\", i0.ɵɵpureFunction0(13, _c2));\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"disabled\", !(ctx.selectedSchedules == null ? null : ctx.selectedSchedules.length) || ctx.deleting)(\"gdcoTrackData\", i0.ɵɵpureFunction1(14, _c3, ctx.selectedSchedules == null ? null : ctx.selectedSchedules.length));\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"working\", ctx.deleting);\n      i0.ɵɵadvance(9);\n      i0.ɵɵproperty(\"value\", ctx.selectedCampuses);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngForOf\", ctx.campuses);\n      i0.ɵɵadvance(5);\n      i0.ɵɵproperty(\"value\", ctx.selectedUser);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"value\", null)(\"gdcoTrackData\", i0.ɵɵpureFunction0(16, _c4));\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"value\", ctx.userId)(\"gdcoTrackData\", i0.ɵɵpureFunction0(17, _c5));\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngForOf\", ctx.users);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.hasCompletedSchedules);\n    }\n  },\n  dependencies: [i2.NgForOf, i2.NgIf, i3.DefaultLayoutDirective, i3.DefaultLayoutGapDirective, i4.MatOption, i5.MatAnchor, i5.MatButton, i6.MatCheckbox, i7.MatFormField, i8.MatIcon, i9.MatSelect, i10.RouterLink, i11.GdcoMatSelectAccessibilityDirective, i11.GdcoTrackDirective, i11.GdcoActionBarComponent, i11.GdcoLabelComponent, i11.GdcoSpinnerButtonContentComponent],\n  styles: [\"[_nghost-%COMP%] {\\r\\n  display: block;\\r\\n  margin-bottom: 8px;\\r\\n  font-size: 14px;\\r\\n}\\r\\n\\r\\n.actions[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  flex-direction: row;\\r\\n  align-items: center;\\r\\n  width: 500px;\\r\\n}\\r\\n.actions[_ngcontent-%COMP%]    > [_ngcontent-%COMP%]:not(:last-child) {\\r\\n  margin-right: 16px;\\r\\n}\\r\\n\\r\\n.filter-item[_ngcontent-%COMP%] {\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n}\\r\\n\\r\\n.filter-label[_ngcontent-%COMP%] {\\r\\n  margin-right: 8px;\\r\\n}\\r\\n\\r\\n.template-action-button[_ngcontent-%COMP%] {\\r\\n  margin-bottom: 4px;\\r\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3Byb2plY3RzL2FwcGxpY2F0aW9ucy90YXNrcy1zY2hlZHVsZXIvc3JjL2FwcC9wYWdlcy93b3Jrc3BhY2Uvc2NoZWR1bGUtaW5zdGFuY2VzL2FjdGlvbnMvaW5zdGFuY2VzLWFjdGlvbnMuY29tcG9uZW50LmNzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNFLGNBQWM7RUFDZCxrQkFBa0I7RUFDbEIsZUFBZTtBQUNqQjs7QUFFQTtFQUNFLGFBQWE7RUFDYixtQkFBbUI7RUFDbkIsbUJBQW1CO0VBQ25CLFlBQVk7QUFDZDtBQUNBO0VBQ0Usa0JBQWtCO0FBQ3BCOztBQUVBO0VBQ0UsYUFBYTtFQUNiLG1CQUFtQjtBQUNyQjs7QUFFQTtFQUNFLGlCQUFpQjtBQUNuQjs7QUFFQTtFQUNFLGtCQUFrQjtBQUNwQiIsInNvdXJjZXNDb250ZW50IjpbIjpob3N0IHtcclxuICBkaXNwbGF5OiBibG9jaztcclxuICBtYXJnaW4tYm90dG9tOiA4cHg7XHJcbiAgZm9udC1zaXplOiAxNHB4O1xyXG59XHJcblxyXG4uYWN0aW9ucyB7XHJcbiAgZGlzcGxheTogZmxleDtcclxuICBmbGV4LWRpcmVjdGlvbjogcm93O1xyXG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAgd2lkdGg6IDUwMHB4O1xyXG59XHJcbi5hY3Rpb25zID4gOm5vdCg6bGFzdC1jaGlsZCkge1xyXG4gIG1hcmdpbi1yaWdodDogMTZweDtcclxufVxyXG5cclxuLmZpbHRlci1pdGVtIHtcclxuICBkaXNwbGF5OiBmbGV4O1xyXG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbn1cclxuXHJcbi5maWx0ZXItbGFiZWwge1xyXG4gIG1hcmdpbi1yaWdodDogOHB4O1xyXG59XHJcblxyXG4udGVtcGxhdGUtYWN0aW9uLWJ1dHRvbiB7XHJcbiAgbWFyZ2luLWJvdHRvbTogNHB4O1xyXG59XHJcbiJdLCJzb3VyY2VSb290IjoiIn0= */\"],\n  changeDetection: 0\n});", "map": {"version": 3, "names": ["EventEmitter", "GdcoAuth", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "campus_r3", "ɵɵpureFunction1", "_c0", "ɵɵadvance", "ɵɵtextInterpolate", "user_r4", "ObjectId", "ɵɵpureFunction0", "_c1", "DisplayName", "ɵɵlistener", "ScheduleInstancesActionsComponent_mat_checkbox_26_Template_mat_checkbox_change_0_listener", "$event", "ɵɵrestoreView", "_r6", "ctx_r5", "ɵɵnextContext", "ɵɵresetView", "onShowCompletedSchedulesChanged", "ctx_r2", "showCompletedSchedules", "ScheduleInstancesActionsComponent", "userId", "_auth", "currentUser", "constructor", "deleting", "selectedCampuses", "hasCompletedSchedules", "delete", "new", "campusSelected", "userSelected", "showCompletedSchedulesChange", "onNewRecurringTaskClicked", "emit", "onDeleteClicked", "onCampusSelectionChanged", "event", "value", "onUserSelectionChanged", "selectedUserPrincipalName", "selected<PERSON>ser", "users", "selected", "find", "user", "UserPrincipalName", "checked", "ɵɵdirectiveInject", "i1", "selectors", "inputs", "selectedSchedules", "campuses", "outputs", "decls", "vars", "consts", "template", "ScheduleInstancesActionsComponent_Template", "rf", "ctx", "ScheduleInstancesActionsComponent_Template_button_click_2_listener", "ScheduleInstancesActionsComponent_Template_button_click_4_listener", "ɵɵelement", "ScheduleInstancesActionsComponent_Template_mat_select_selectionChange_14_listener", "ɵɵtemplate", "ScheduleInstancesActionsComponent_mat_option_15_Template", "ScheduleInstancesActionsComponent_Template_mat_select_selectionChange_20_listener", "ScheduleInstancesActionsComponent_mat_option_25_Template", "ScheduleInstancesActionsComponent_mat_checkbox_26_Template", "_c2", "length", "_c3", "_c4", "_c5"], "sources": ["D:\\Repo\\GDCO\\MCIO-GDCO-AppService\\src\\GDCOClient\\GdcoApp\\projects\\applications\\tasks-scheduler\\src\\app\\pages\\workspace\\schedule-instances\\actions\\instances-actions.component.ts", "D:\\Repo\\GDCO\\MCIO-GDCO-AppService\\src\\GDCOClient\\GdcoApp\\projects\\applications\\tasks-scheduler\\src\\app\\pages\\workspace\\schedule-instances\\actions\\instances-actions.component.html"], "sourcesContent": ["/**\r\n * @license\r\n * Copyright (c) Microsoft Corporation. All rights reserved.\r\n */\r\n\r\nimport { Component, ChangeDetectionStrategy, Input, Output, EventEmitter } from '@angular/core';\r\nimport { MatCheckboxChange } from '@angular/material/checkbox';\r\nimport { MatSelectChange } from '@angular/material/select';\r\nimport { GdcoAuth } from '@gdco/auth';\r\nimport { AzureGraphObject } from '@gdco/core-reference-systems/gdco-service';\r\nimport { Schedule } from '@gdco/reference-systems/gdco-service';\r\n\r\n@Component({\r\n  selector: 'tasks-scheduler-instances-actions',\r\n  templateUrl: './instances-actions.component.html',\r\n  styleUrls: ['./instances-actions.component.css'],\r\n  changeDetection: ChangeDetectionStrategy.OnPush\r\n})\r\nexport class ScheduleInstancesActionsComponent {\r\n  @Input() selectedSchedules: Schedule[];\r\n  @Input() deleting = false;\r\n  @Input() campuses: string[];\r\n  @Input() selectedCampuses: string[] = [];\r\n  @Input() users: AzureGraphObject[];\r\n  @Input() selectedUser: string;\r\n  @Input() hasCompletedSchedules = false;\r\n  @Input() showCompletedSchedules = false;\r\n\r\n  @Output() delete: EventEmitter<void> = new EventEmitter();\r\n  @Output() new: EventEmitter<void> = new EventEmitter();\r\n  @Output() campusSelected: EventEmitter<string[]> = new EventEmitter();\r\n  @Output() userSelected: EventEmitter<string> = new EventEmitter();\r\n  @Output() showCompletedSchedulesChange: EventEmitter<boolean> = new EventEmitter();\r\n\r\n  get userId(): string {\r\n    return this._auth.currentUser.userId;\r\n  }\r\n\r\n  constructor(private _auth: GdcoAuth) {}\r\n\r\n  onNewRecurringTaskClicked(): void {\r\n    this.new.emit();\r\n  }\r\n\r\n  onDeleteClicked(): void {\r\n    this.delete.emit();\r\n  }\r\n\r\n  onCampusSelectionChanged(event: MatSelectChange): void {\r\n    this.campusSelected.emit(event.value);\r\n  }\r\n\r\n  onUserSelectionChanged(event: MatSelectChange): void {\r\n    this.userSelected.emit(event.value);\r\n  }\r\n\r\n  selectedUserPrincipalName(): string {\r\n    if (!this.selectedUser || !this.users) {\r\n      return null;\r\n    }\r\n\r\n    const selected = this.users.find(user => user.ObjectId === this.selectedUser);\r\n    if (selected) {\r\n      return selected.UserPrincipalName;\r\n    }\r\n\r\n    return null;\r\n  }\r\n\r\n  onShowCompletedSchedulesChanged(event: MatCheckboxChange): void {\r\n    this.showCompletedSchedulesChange.emit(event.checked);\r\n  }\r\n}\r\n", "<gdco-action-bar wrap=\"wrap\">\r\n  <div fxLayout=\"row wrap\" fxLayoutGap=\"16px\" class=\"actions\">\r\n    <button\r\n      class=\"template-action-button\"\r\n      mat-flat-button\r\n      color=\"primary\"\r\n      (click)=\"onNewRecurringTaskClicked()\"\r\n      gdcoTrack=\"NewRecurringTaskClicked\"\r\n      [gdcoTrackData]=\"{ view: 'calendar' }\"\r\n    >\r\n      New recurring task\r\n    </button>\r\n    <button\r\n      class=\"template-action-button\"\r\n      mat-stroked-button\r\n      color=\"primary\"\r\n      [disabled]=\"!selectedSchedules?.length || deleting\"\r\n      (click)=\"onDeleteClicked()\"\r\n      gdcoTrack=\"DeleteSchedulesClicked\"\r\n      [gdcoTrackData]=\"{ count: selectedSchedules?.length }\"\r\n    >\r\n      <gdco-spinner-button-content\r\n        text=\"Delete\"\r\n        workingText=\"Deleting\"\r\n        [working]=\"deleting\"\r\n      ></gdco-spinner-button-content>\r\n    </button>\r\n    <a mat-stroked-button class=\"template-action-button\" color=\"primary\" routerLink=\"..\" gdcoTrack=\"ViewListClicked\"\r\n      ><mat-icon>view_list</mat-icon> View list</a\r\n    >\r\n  </div>\r\n\r\n  <div class=\"filter-item\">\r\n    <gdco-label class=\"filter-label\">Campus:</gdco-label>\r\n    <mat-form-field class=\"gdco-compact\">\r\n      <mat-select\r\n        gdcoMatSelectAccessibility\r\n        aria-label=\"Select campus\"\r\n        placeholder=\"All\"\r\n        [value]=\"selectedCampuses\"\r\n        multiple\r\n        (selectionChange)=\"onCampusSelectionChanged($event)\"\r\n      >\r\n        <mat-option\r\n          *ngFor=\"let campus of campuses\"\r\n          [value]=\"campus\"\r\n          gdcoTrack=\"ScheduleCampusFilter\"\r\n          [gdcoTrackData]=\"{ option: campus }\"\r\n          >{{ campus }}</mat-option\r\n        >\r\n      </mat-select>\r\n    </mat-form-field>\r\n  </div>\r\n\r\n  <div class=\"filter-item\">\r\n    <gdco-label class=\"filter-label\">Created by:</gdco-label>\r\n    <mat-form-field class=\"gdco-compact\">\r\n      <mat-select\r\n        gdcoMatSelectAccessibility\r\n        aria-label=\"Created by\"\r\n        placeholder=\"Anyone\"\r\n        [value]=\"selectedUser\"\r\n        (selectionChange)=\"onUserSelectionChanged($event)\"\r\n      >\r\n        <mat-option [value]=\"null\" gdcoTrack=\"ScheduleCreatedByFilter\" [gdcoTrackData]=\"{ option: 'Anyone' }\"\r\n          >Anyone</mat-option\r\n        >\r\n        <mat-option [value]=\"userId\" gdcoTrack=\"ScheduleCreatedByFilter\" [gdcoTrackData]=\"{ option: 'Me' }\"\r\n          >Me</mat-option\r\n        >\r\n        <mat-option\r\n          *ngFor=\"let user of users\"\r\n          [value]=\"user.ObjectId\"\r\n          gdcoTrack=\"ScheduleCreatedByFilter\"\r\n          [gdcoTrackData]=\"{ option: 'User' }\"\r\n        >\r\n          <span>{{ user.DisplayName }}</span>\r\n        </mat-option>\r\n      </mat-select>\r\n    </mat-form-field>\r\n  </div>\r\n\r\n  <mat-checkbox\r\n    *ngIf=\"hasCompletedSchedules\"\r\n    [checked]=\"showCompletedSchedules\"\r\n    (change)=\"onShowCompletedSchedulesChanged($event)\"\r\n    >Show completed schedules</mat-checkbox\r\n  >\r\n</gdco-action-bar>\r\n"], "mappings": "AAAA;;;;AAKA,SAA4DA,YAAY,QAAQ,eAAe;AAG/F,SAASC,QAAQ,QAAQ,YAAY;;;;;;;;;;;;;;;;;;;;ICmC7BC,EAAA,CAAAC,cAAA,qBAKG;IAAAD,EAAA,CAAAE,MAAA,GAAY;IAAAF,EAAA,CAAAG,YAAA,EACd;;;;IAJCH,EAAA,CAAAI,UAAA,UAAAC,SAAA,CAAgB,kBAAAL,EAAA,CAAAM,eAAA,IAAAC,GAAA,EAAAF,SAAA;IAGfL,EAAA,CAAAQ,SAAA,GAAY;IAAZR,EAAA,CAAAS,iBAAA,CAAAJ,SAAA,CAAY;;;;;;;;;;IAsBfL,EAAA,CAAAC,cAAA,qBAKC;IACOD,EAAA,CAAAE,MAAA,GAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAJnCH,EAAA,CAAAI,UAAA,UAAAM,OAAA,CAAAC,QAAA,CAAuB,kBAAAX,EAAA,CAAAY,eAAA,IAAAC,GAAA;IAIjBb,EAAA,CAAAQ,SAAA,GAAsB;IAAtBR,EAAA,CAAAS,iBAAA,CAAAC,OAAA,CAAAI,WAAA,CAAsB;;;;;;IAMpCd,EAAA,CAAAC,cAAA,uBAIG;IADDD,EAAA,CAAAe,UAAA,oBAAAC,0FAAAC,MAAA;MAAAjB,EAAA,CAAAkB,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAApB,EAAA,CAAAqB,aAAA;MAAA,OAAUrB,EAAA,CAAAsB,WAAA,CAAAF,MAAA,CAAAG,+BAAA,CAAAN,MAAA,CAAuC;IAAA,EAAC;IACjDjB,EAAA,CAAAE,MAAA,+BAAwB;IAAAF,EAAA,CAAAG,YAAA,EAC1B;;;;IAHCH,EAAA,CAAAI,UAAA,YAAAoB,MAAA,CAAAC,sBAAA,CAAkC;;;;;;;;;;;;;;;;;;;;;;;ADlEtC,OAAM,MAAOC,iCAAiC;EAgB5C,IAAIC,MAAMA,CAAA;IACR,OAAO,IAAI,CAACC,KAAK,CAACC,WAAW,CAACF,MAAM;EACtC;EAEAG,YAAoBF,KAAe;IAAf,KAAAA,KAAK,GAALA,KAAK;IAlBhB,KAAAG,QAAQ,GAAG,KAAK;IAEhB,KAAAC,gBAAgB,GAAa,EAAE;IAG/B,KAAAC,qBAAqB,GAAG,KAAK;IAC7B,KAAAR,sBAAsB,GAAG,KAAK;IAE7B,KAAAS,MAAM,GAAuB,IAAIpC,YAAY,EAAE;IAC/C,KAAAqC,GAAG,GAAuB,IAAIrC,YAAY,EAAE;IAC5C,KAAAsC,cAAc,GAA2B,IAAItC,YAAY,EAAE;IAC3D,KAAAuC,YAAY,GAAyB,IAAIvC,YAAY,EAAE;IACvD,KAAAwC,4BAA4B,GAA0B,IAAIxC,YAAY,EAAE;EAM5C;EAEtCyC,yBAAyBA,CAAA;IACvB,IAAI,CAACJ,GAAG,CAACK,IAAI,EAAE;EACjB;EAEAC,eAAeA,CAAA;IACb,IAAI,CAACP,MAAM,CAACM,IAAI,EAAE;EACpB;EAEAE,wBAAwBA,CAACC,KAAsB;IAC7C,IAAI,CAACP,cAAc,CAACI,IAAI,CAACG,KAAK,CAACC,KAAK,CAAC;EACvC;EAEAC,sBAAsBA,CAACF,KAAsB;IAC3C,IAAI,CAACN,YAAY,CAACG,IAAI,CAACG,KAAK,CAACC,KAAK,CAAC;EACrC;EAEAE,yBAAyBA,CAAA;IACvB,IAAI,CAAC,IAAI,CAACC,YAAY,IAAI,CAAC,IAAI,CAACC,KAAK,EAAE;MACrC,OAAO,IAAI;;IAGb,MAAMC,QAAQ,GAAG,IAAI,CAACD,KAAK,CAACE,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACxC,QAAQ,KAAK,IAAI,CAACoC,YAAY,CAAC;IAC7E,IAAIE,QAAQ,EAAE;MACZ,OAAOA,QAAQ,CAACG,iBAAiB;;IAGnC,OAAO,IAAI;EACb;EAEA7B,+BAA+BA,CAACoB,KAAwB;IACtD,IAAI,CAACL,4BAA4B,CAACE,IAAI,CAACG,KAAK,CAACU,OAAO,CAAC;EACvD;;;mBArDW3B,iCAAiC,EAAA1B,EAAA,CAAAsD,iBAAA,CAAAC,EAAA,CAAAxD,QAAA;AAAA;;QAAjC2B,iCAAiC;EAAA8B,SAAA;EAAAC,MAAA;IAAAC,iBAAA;IAAA3B,QAAA;IAAA4B,QAAA;IAAA3B,gBAAA;IAAAgB,KAAA;IAAAD,YAAA;IAAAd,qBAAA;IAAAR,sBAAA;EAAA;EAAAmC,OAAA;IAAA1B,MAAA;IAAAC,GAAA;IAAAC,cAAA;IAAAC,YAAA;IAAAC,4BAAA;EAAA;EAAAuB,KAAA;EAAAC,IAAA;EAAAC,MAAA;EAAAC,QAAA,WAAAC,2CAAAC,EAAA,EAAAC,GAAA;IAAA,IAAAD,EAAA;MClB9ClE,EAAA,CAAAC,cAAA,yBAA6B;MAMvBD,EAAA,CAAAe,UAAA,mBAAAqD,mEAAA;QAAA,OAASD,GAAA,CAAA5B,yBAAA,EAA2B;MAAA,EAAC;MAIrCvC,EAAA,CAAAE,MAAA,2BACF;MAAAF,EAAA,CAAAG,YAAA,EAAS;MACTH,EAAA,CAAAC,cAAA,gBAQC;MAHCD,EAAA,CAAAe,UAAA,mBAAAsD,mEAAA;QAAA,OAASF,GAAA,CAAA1B,eAAA,EAAiB;MAAA,EAAC;MAI3BzC,EAAA,CAAAsE,SAAA,qCAI+B;MACjCtE,EAAA,CAAAG,YAAA,EAAS;MACTH,EAAA,CAAAC,cAAA,WACG;MAAUD,EAAA,CAAAE,MAAA,gBAAS;MAAAF,EAAA,CAAAG,YAAA,EAAW;MAACH,EAAA,CAAAE,MAAA,iBAAS;MAAAF,EAAA,CAAAG,YAAA,EAC1C;MAGHH,EAAA,CAAAC,cAAA,cAAyB;MACUD,EAAA,CAAAE,MAAA,eAAO;MAAAF,EAAA,CAAAG,YAAA,EAAa;MACrDH,EAAA,CAAAC,cAAA,yBAAqC;MAOjCD,EAAA,CAAAe,UAAA,6BAAAwD,kFAAAtD,MAAA;QAAA,OAAmBkD,GAAA,CAAAzB,wBAAA,CAAAzB,MAAA,CAAgC;MAAA,EAAC;MAEpDjB,EAAA,CAAAwE,UAAA,KAAAC,wDAAA,yBAMC;MACHzE,EAAA,CAAAG,YAAA,EAAa;MAIjBH,EAAA,CAAAC,cAAA,cAAyB;MACUD,EAAA,CAAAE,MAAA,mBAAW;MAAAF,EAAA,CAAAG,YAAA,EAAa;MACzDH,EAAA,CAAAC,cAAA,yBAAqC;MAMjCD,EAAA,CAAAe,UAAA,6BAAA2D,kFAAAzD,MAAA;QAAA,OAAmBkD,GAAA,CAAAtB,sBAAA,CAAA5B,MAAA,CAA8B;MAAA,EAAC;MAElDjB,EAAA,CAAAC,cAAA,sBACG;MAAAD,EAAA,CAAAE,MAAA,cAAM;MAAAF,EAAA,CAAAG,YAAA,EACR;MACDH,EAAA,CAAAC,cAAA,sBACG;MAAAD,EAAA,CAAAE,MAAA,UAAE;MAAAF,EAAA,CAAAG,YAAA,EACJ;MACDH,EAAA,CAAAwE,UAAA,KAAAG,wDAAA,yBAOa;MACf3E,EAAA,CAAAG,YAAA,EAAa;MAIjBH,EAAA,CAAAwE,UAAA,KAAAI,0DAAA,2BAKC;MACH5E,EAAA,CAAAG,YAAA,EAAkB;;;MAhFZH,EAAA,CAAAQ,SAAA,GAAsC;MAAtCR,EAAA,CAAAI,UAAA,kBAAAJ,EAAA,CAAAY,eAAA,KAAAiE,GAAA,EAAsC;MAQtC7E,EAAA,CAAAQ,SAAA,GAAmD;MAAnDR,EAAA,CAAAI,UAAA,eAAA+D,GAAA,CAAAT,iBAAA,kBAAAS,GAAA,CAAAT,iBAAA,CAAAoB,MAAA,KAAAX,GAAA,CAAApC,QAAA,CAAmD,kBAAA/B,EAAA,CAAAM,eAAA,KAAAyE,GAAA,EAAAZ,GAAA,CAAAT,iBAAA,kBAAAS,GAAA,CAAAT,iBAAA,CAAAoB,MAAA;MAQjD9E,EAAA,CAAAQ,SAAA,GAAoB;MAApBR,EAAA,CAAAI,UAAA,YAAA+D,GAAA,CAAApC,QAAA,CAAoB;MAepB/B,EAAA,CAAAQ,SAAA,GAA0B;MAA1BR,EAAA,CAAAI,UAAA,UAAA+D,GAAA,CAAAnC,gBAAA,CAA0B;MAKLhC,EAAA,CAAAQ,SAAA,GAAW;MAAXR,EAAA,CAAAI,UAAA,YAAA+D,GAAA,CAAAR,QAAA,CAAW;MAiBhC3D,EAAA,CAAAQ,SAAA,GAAsB;MAAtBR,EAAA,CAAAI,UAAA,UAAA+D,GAAA,CAAApB,YAAA,CAAsB;MAGV/C,EAAA,CAAAQ,SAAA,GAAc;MAAdR,EAAA,CAAAI,UAAA,eAAc,kBAAAJ,EAAA,CAAAY,eAAA,KAAAoE,GAAA;MAGdhF,EAAA,CAAAQ,SAAA,GAAgB;MAAhBR,EAAA,CAAAI,UAAA,UAAA+D,GAAA,CAAAxC,MAAA,CAAgB,kBAAA3B,EAAA,CAAAY,eAAA,KAAAqE,GAAA;MAITjF,EAAA,CAAAQ,SAAA,GAAQ;MAARR,EAAA,CAAAI,UAAA,YAAA+D,GAAA,CAAAnB,KAAA,CAAQ;MAY9BhD,EAAA,CAAAQ,SAAA,GAA2B;MAA3BR,EAAA,CAAAI,UAAA,SAAA+D,GAAA,CAAAlC,qBAAA,CAA2B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}